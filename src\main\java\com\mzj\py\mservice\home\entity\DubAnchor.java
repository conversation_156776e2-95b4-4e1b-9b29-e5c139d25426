package com.mzj.py.mservice.home.entity;

import com.mzj.py.mservice.wxuser.entity.IdEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "dub_anchor")
public class DubAnchor extends IdEntity {
    /**
     * 名称
     */
    @Column(name = "name",columnDefinition = "varchar")
    private String name;

    /**
     * voice参数
     */
    @Column(name = "voice_name",columnDefinition = "varchar")
    private String voiceName;

    /**
     * 类型名称
     */
    @Column(name = "type_name",columnDefinition = "varchar")
    private String typeName;

    /**
     * 使用场景
     */
    @Column(name = "usage_scenario",columnDefinition = "varchar")
    private String usageScenario;

    /**
     * 1中文及英文混合 2纯中文场景 3标准粤文
     */
    @Column(name = "support_voice_type",columnDefinition = "smallint")
    private Integer supportVoiceType;

    /**
     * 支持采样率
     */
    @Column(name = "sample_rate",columnDefinition = "smallint")
    private Integer sampleRate;

    /**
     * 支持时间戳
     */
    @Column(name = "enable_subtitle",columnDefinition = "smallint")
    private Integer enableSubtitle;

    /**
     * 支持儿化音
     */
    @Column(name = "rhotic_accent",columnDefinition = "smallint")
    private Integer rhoticAccent;

    @Column(name = "url",columnDefinition = "varchar")
    private String url;

    @Column(name = "type",columnDefinition = "varchar")
    private String type;

    @Column(name = "voice_url",columnDefinition = "varchar")
    private String voiceUrl;

    @Column(name = "archor_tag",columnDefinition = "varchar")
    private String archorTag;

    @Column(name = "is_emotion",columnDefinition = "smallint")
    private Integer isEmotion;

    @Column(name = "emotion",columnDefinition = "varchar")
    private String emotion;

    @Column(name = "order_index",columnDefinition = "smallint")
    private Integer orderIndex;
}
