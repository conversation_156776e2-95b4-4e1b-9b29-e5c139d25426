package com.mzj.py.mservice.work.service;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.StringUtils;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.commons.exception.CustomException;
import com.mzj.py.mservice.home.entity.DeviceVoice;
import com.mzj.py.mservice.home.entity.VoicePacket;
import com.mzj.py.mservice.home.entity.VoiceWork;
import com.mzj.py.mservice.home.repository.DeviceVoiceRepository;
import com.mzj.py.mservice.home.repository.VoicePacketRepository;
import com.mzj.py.mservice.home.repository.VoiceWorkRepository;
import com.mzj.py.mservice.oss.service.OSSService;
import com.mzj.py.mservice.redis.RedisService;
import com.mzj.py.mservice.work.vo.WorkInfoVo;
import com.mzj.py.mservice.work.vo.WorkPageParam;
import com.mzj.py.mservice.work.vo.WorkVo;
import com.mzj.py.mservice.wxuser.entity.WxUser;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.io.File;
import java.util.*;

@Service
public class WorkService {
    @Resource
    private RedisService redisService;

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private VoiceWorkRepository workRepository;

    @Resource
    private VoicePacketRepository packetRepository;

    @Resource
    private DeviceVoiceRepository voiceRepository;

    @Resource
    private OSSService ossService;

    public ResultBean<TokenRedisVo> getUser(String accessToken) {
        TokenRedisVo vo = redisService.findTokenVo(accessToken);
        if (vo == null) {
            return ResultBean.failedResultOfToken();
        }
        return ResultBean.successfulResult(vo);
    }

    public ResultBean<Map<String, Object>> list(String accessToken, Integer pageNumber, Integer pageSize,
                                                String keyword) {
        TokenRedisVo redisVo = getUser(accessToken).getResultData();
        if (redisVo == null) {
            return ResultBean.failedResultOfToken();
        }

        List<Object> args = new ArrayList<>();
        args.add(redisVo.getId());
        StringBuilder sql = new StringBuilder("SELECT\n" +
                "\tw.*,\n" +
                "\tp.file_url \n" +
                "FROM\n" +
                "\tdub_voice_work w\n" +
                "\tLEFT JOIN dub_voice_packet p ON p.id = w.voice_id \n" +
                "WHERE\n" +
                "\tw.user_id = ? \n" +
                "\tAND w.del_status = 0 ");
        if (StringUtils.isNotBlank(keyword)) {
            sql.append(" AND w.title like ?");
            args.add("%" + keyword + "%");
        }
        sql.append(" order by w.create_time desc");
        Map<String, Object> map = new HashMap<>();
        Long count = jdbcTemplate.queryForObject("select count(1) as total from (" + sql + ") s", Long.class,
                args.toArray());
        if (count == null || count == 0) {
            map.put("total", 0L);
            map.put("result", null);
            return ResultBean.successfulResult(map);
        }
        sql.append(" limit ? offset ? ");
        args.add(pageSize);
        args.add(pageSize * pageNumber);
        List<WorkVo> list = jdbcTemplate.query(sql.toString(), new BeanPropertyRowMapper<>(WorkVo.class),
                args.toArray());
        map.put("total", count);
        map.put("result", list);
        return ResultBean.successfulResult(map);
    }

    public void delById(Long id, List<Long> shopIds,Long userId) throws CustomException {
        // 标记作品逻辑删除并移除语音包数据
        workRepository.editById(id,shopIds,userId);
        VoicePacket packet = packetRepository.findByVoiceWorkId(id);
        // 若未查询到语音包或 url 为空，一律认为文件不存在，直接抛出业务异常
        if (packet != null) {
            // 如果 OSS 上存在对应文件则删除，忽略不存在的情况
            if (StringUtils.isNotBlank(packet.getFileUrl()) && ossService.doesObjectExist(packet.getFileUrl())) {
                ossService.deleteObject(null, packet.getFileUrl());
            }
            packetRepository.deleteByVoiceWorkId(id);
        }
    }

    public ResultBean<WorkInfoVo> info(Long id) {
        StringBuilder sql = new StringBuilder("SELECT\n" +
                "\tw.*,\n" +
                "\tp.file_url,\n" +
                "\ta.name as anchorName,\n" +
                "\tm.name as musicName\n" +
                "FROM\n" +
                "\tdub_voice_work w\n" +
                "\tLEFT JOIN dub_voice_packet p ON p.id = w.voice_id \n" +
                "\tLEFT JOIN dub_background_music m on w.background_music_id = m.id\n" +
                "\tLEFT JOIN dub_anchor a on a.id = w.anchor_id\n" +
                "WHERE\n" +
                "\tw.id = ? \n" +
                "\tAND w.del_status = 0");
        List<WorkInfoVo> list = jdbcTemplate.query(sql.toString(), new BeanPropertyRowMapper<>(WorkInfoVo.class), id);
        if (list.size() > 0) {
            return ResultBean.successfulResult(list.get(0));
        }
        return null;
    }

    public void save(WorkInfoVo vo) throws CustomException {
        if (StringUtils.isEmpty(vo.getFileUrl())) {
            throw new CustomException("音频文件不能为空");
        }
        VoiceWork work = null;
        if (vo.getId() != null) {
            work = workRepository.getOne(vo.getId());
        } else {
            work = new VoiceWork();
        }

        // 生成作品
        work.setVoiceTime(vo.getVoiceTime());
        work.setSpeed(vo.getSpeed());
        work.setVolume(vo.getVolume());
        work.setPitch(vo.getPitch());
        work.setTitle(vo.getTitle());
        work.setAnchorId(vo.getAnchorId());
        work.setBackgroundMusicVolume(vo.getBackgroundMusicVolume() != null ? vo.getBackgroundMusicVolume() : 0);
        work.setBackgroundMusicId(vo.getBackgroundMusicId());
        work.setCreateTime(new Date());
        work.setDelStatus(0);
        work.setUserId(vo.getUserId());
        work.setShopId(vo.getShopId());
        work.setContent(vo.getContent());
        workRepository.save(work);

        // 生成语音包
        VoicePacket packet = packetRepository.findByVoiceWorkId(work.getId());// 语音包id 查询
        if (packet == null) {
            packet = new VoicePacket();
        }
        File file = ossService.getObjectFile(null, vo.getFileUrl());
        String url = ossService.putFileToName(null, file, "voice", UUID.randomUUID().toString());
        packet.setVoiceWorkId(work.getId());//
        packet.setFileUrl(url);
        packet.setName(vo.getTitle());
        packet.setVoiceTime(vo.getVoiceTime());
        packet.setShopId(vo.getShopId());
        packetRepository.save(packet);
        work.setVoiceId(packet.getId());
        workRepository.save(work);
        file.delete();
    }

    public ResultBean<Map<String, Object>> getWorkList(List<Long> shopIds, WorkPageParam param) {
        Pageable pageable = PageRequest.of(param.getPageNumber(), param.getPageSize());
        Page<VoiceWork> page = workRepository.findAll((root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            // 动态添加模糊查询条件（参数非空时生效）
            if (StringUtils.isNotBlank(param.getKeyword())) {
                predicates.add(criteriaBuilder.like(root.get("title"), "%" + param.getKeyword() + "%"));
            }
            if (shopIds != null) {
                predicates.add(root.get("shopId").in(shopIds));
            }
            // 组合所有条件（AND 关系）
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        }, pageable);
        if (page == null) {
            return ResultBean.getResultMap(0, new ArrayList<>());
        }
        return ResultBean.getResultMap((int) page.getTotalElements(), page.getContent());
    }

}
