package com.mzj.py.mservice.paidPackages.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PaidPackagesVo {


    private Long id;
    private String name;

    private Integer amount;
    private Integer validDays;
    private BigDecimal price;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createdAt;

}
