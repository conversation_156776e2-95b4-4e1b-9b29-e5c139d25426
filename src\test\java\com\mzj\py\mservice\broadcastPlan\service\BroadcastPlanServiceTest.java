package com.mzj.py.mservice.broadcastPlan.service;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.mservice.broadcastPlan.entity.BroadcastPlan;
import com.mzj.py.mservice.broadcastPlan.entity.BroadcastPlanVoiceWorkRef;
import com.mzj.py.mservice.broadcastPlan.repository.BroadcastPlanRepository;
import com.mzj.py.mservice.broadcastPlan.repository.BroadcastPlanVoiceWorkRepository;
import com.mzj.py.mservice.broadcastPlan.vo.BroadcastPlanVo;
import com.mzj.py.mservice.broadcastPlan.vo.dto.BroadcastPlanAddDto;
import com.mzj.py.mservice.redis.RedisService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

/**
 * BroadcastPlanService 单元测试
 * 
 * 测试覆盖：
 * 1. 播报计划列表查询功能
 * 2. 播报计划添加/更新功能
 * 3. 播报计划删除功能
 * 4. 各种边缘情况和异常场景
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("BroadcastPlanService 单元测试")
class BroadcastPlanServiceTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private RedisService redisService;

    @Mock
    private BroadcastPlanRepository broadcastPlanRepository;

    @Mock
    private BroadcastPlanVoiceWorkRepository broadcastPlanVoiceWorkRepository;

    @InjectMocks
    private BroadcastPlanService broadcastPlanService;

    private TokenRedisVo mockTokenVo;
    private BroadcastPlanAddDto mockAddDto;
    private BroadcastPlan mockBroadcastPlan;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockTokenVo = createMockTokenVo();
        mockAddDto = createMockAddDto();
        mockBroadcastPlan = createMockBroadcastPlan();
    }

    // ==================== list 方法测试 ====================

    @Test
    @DisplayName("测试播报计划列表查询 - 正常情况")
    @SuppressWarnings("unchecked")
    void testList_Success() {
        // Given
        List<Long> shopIds = Arrays.asList(1L, 2L);
        Integer pageSize = 10;
        Integer pageNumber = 0;
        Long totalCount = 5L;

        List<BroadcastPlanVo> mockPlans = Arrays.asList(
                createMockBroadcastPlanVo(1L, "测试计划1"),
                createMockBroadcastPlanVo(2L, "测试计划2"));

        lenient().when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(totalCount);
        lenient().when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn((List<Object>) (List<?>) mockPlans);

        // When
        ResultBean<Map<String, Object>> result = broadcastPlanService.list(shopIds, pageSize, pageNumber);

        // Then
        assertNotNull(result);
        assertTrue(result.isOk());
        assertNotNull(result.getResultData());
        assertNotNull(result.getResultData().get("count"));
        assertNotNull(result.getResultData().get("result"));
        assertEquals(totalCount.intValue(), result.getResultData().get("count"));
        assertEquals(mockPlans, result.getResultData().get("result"));

        // 验证SQL查询被调用
        verify(jdbcTemplate, times(1)).queryForObject(contains("select count(1)"), eq(Long.class), any());
        verify(jdbcTemplate, times(1)).query(contains("SELECT b.id"), any(BeanPropertyRowMapper.class),
                any());
    }

    @Test
    @DisplayName("测试播报计划列表查询 - 无数据情况")
    void testList_NoData() {
        // Given
        List<Long> shopIds = Arrays.asList(1L);
        Integer pageSize = 10;
        Integer pageNumber = 0;
        Long totalCount = 0L;

        lenient().when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(totalCount);

        // When
        ResultBean<Map<String, Object>> result = broadcastPlanService.list(shopIds, pageSize, pageNumber);

        // Then
        assertTrue(result.isOk());
        assertNotNull(result.getResultData());
        assertEquals(0, result.getResultData().get("count"));
        assertNotNull(result.getResultData().get("result"));
        assertTrue(((List<?>) result.getResultData().get("result")).isEmpty());

        // 验证只调用了count查询，没有调用数据查询
        verify(jdbcTemplate, times(1)).queryForObject(contains("select count(1)"), eq(Long.class), any(Object[].class));
        verify(jdbcTemplate, never()).query(contains("SELECT b.id"), any(BeanPropertyRowMapper.class),
                any(Object[].class));
    }

    @Test
    @DisplayName("测试播报计划列表查询 - shopIds为空列表")
    @SuppressWarnings("unchecked")
    void testList_EmptyShopIds() {
        // Given
        List<Long> shopIds = Collections.emptyList();
        Integer pageSize = 10;
        Integer pageNumber = 0;
        Long totalCount = 3L;

        List<BroadcastPlanVo> mockPlans = Arrays.asList(createMockBroadcastPlanVo(1L, "全部计划"));

        lenient().when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(totalCount);
        lenient().when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn((List<Object>) (List<?>) mockPlans);

        // When
        ResultBean<Map<String, Object>> result = broadcastPlanService.list(shopIds, pageSize, pageNumber);

        // Then
        assertTrue(result.isOk());
        assertNotNull(result.getResultData());
        assertEquals(totalCount.intValue(), result.getResultData().get("count"));
    }

    // ==================== addOrUpdate 方法测试 ====================

    @Test
    @DisplayName("测试添加播报计划 - 成功添加")
    void testAddOrUpdate_AddSuccess() {
        // Given
        String accessToken = "valid-token";
        mockAddDto.setId(null); // 新增场景

        when(redisService.findTokenVo(accessToken)).thenReturn(mockTokenVo);
        when(broadcastPlanRepository.save(any(BroadcastPlan.class))).thenReturn(mockBroadcastPlan);
        when(broadcastPlanVoiceWorkRepository.deleteByPlanId(anyLong())).thenReturn(1);
        when(broadcastPlanVoiceWorkRepository.saveAll(anyList())).thenReturn(Collections.emptyList());

        // When
        ResultBean<Boolean> result = broadcastPlanService.addOrUpdate(mockAddDto, accessToken);

        // Then
        assertTrue(result.isOk());
        assertTrue(result.getResultData());

        // 验证保存操作被调用
        verify(broadcastPlanRepository, times(1)).save(any(BroadcastPlan.class));
        verify(broadcastPlanVoiceWorkRepository, times(1)).deleteByPlanId(anyLong());
        verify(broadcastPlanVoiceWorkRepository, times(1)).saveAll(anyList());
    }

    @Test
    @DisplayName("测试更新播报计划 - 成功更新")
    void testAddOrUpdate_UpdateSuccess() {
        // Given
        String accessToken = "valid-token";
        mockAddDto.setId(1L); // 更新场景

        when(redisService.findTokenVo(accessToken)).thenReturn(mockTokenVo);
        when(broadcastPlanRepository.findById(1L)).thenReturn(Optional.of(mockBroadcastPlan));
        when(broadcastPlanRepository.save(any(BroadcastPlan.class))).thenReturn(mockBroadcastPlan);
        when(broadcastPlanVoiceWorkRepository.deleteByPlanId(anyLong())).thenReturn(1);
        when(broadcastPlanVoiceWorkRepository.saveAll(anyList())).thenReturn(Collections.emptyList());

        // When
        ResultBean<Boolean> result = broadcastPlanService.addOrUpdate(mockAddDto, accessToken);

        // Then
        assertTrue(result.isOk());
        assertTrue(result.getResultData());

        // 验证查找和保存操作被调用
        verify(broadcastPlanRepository, times(1)).findById(1L);
        verify(broadcastPlanRepository, times(1)).save(any(BroadcastPlan.class));
    }

    @Test
    @DisplayName("测试添加播报计划 - 用户不存在")
    void testAddOrUpdate_UserNotFound() {
        // Given
        String accessToken = "invalid-token";

        when(redisService.findTokenVo(accessToken)).thenReturn(null);

        // When
        ResultBean<Boolean> result = broadcastPlanService.addOrUpdate(mockAddDto, accessToken);

        // Then
        assertFalse(result.isOk());
        assertEquals("用户不存在", result.getMsg());

        // 验证没有进行保存操作
        verify(broadcastPlanRepository, never()).save(any(BroadcastPlan.class));
    }

    @Test
    @DisplayName("测试添加播报计划 - 数据为空")
    void testAddOrUpdate_NullData() {
        // Given
        String accessToken = "valid-token";

        when(redisService.findTokenVo(accessToken)).thenReturn(mockTokenVo);

        // When
        ResultBean<Boolean> result = broadcastPlanService.addOrUpdate(null, accessToken);

        // Then
        assertFalse(result.isOk());
        assertEquals("数据不能为空", result.getMsg());
    }

    @Test
    @DisplayName("测试添加播报计划 - 开始时间为空")
    void testAddOrUpdate_EmptyStartTime() {
        // Given
        String accessToken = "valid-token";
        mockAddDto.setStartTime("");

        when(redisService.findTokenVo(accessToken)).thenReturn(mockTokenVo);

        // When
        ResultBean<Boolean> result = broadcastPlanService.addOrUpdate(mockAddDto, accessToken);

        // Then
        assertFalse(result.isOk());
        assertEquals("播报开始时段不能为空", result.getMsg());
    }

    @Test
    @DisplayName("测试添加播报计划 - 结束时间为空")
    void testAddOrUpdate_EmptyEndTime() {
        // Given
        String accessToken = "valid-token";
        mockAddDto.setEndTime(null);

        when(redisService.findTokenVo(accessToken)).thenReturn(mockTokenVo);

        // When
        ResultBean<Boolean> result = broadcastPlanService.addOrUpdate(mockAddDto, accessToken);

        // Then
        assertFalse(result.isOk());
        assertEquals("播报结束时段不能为空", result.getMsg());
    }

    @Test
    @DisplayName("测试添加播报计划 - 门店为空")
    void testAddOrUpdate_NullShopId() {
        // Given
        String accessToken = "valid-token";
        mockAddDto.setShopId(null);

        when(redisService.findTokenVo(accessToken)).thenReturn(mockTokenVo);

        // When
        ResultBean<Boolean> result = broadcastPlanService.addOrUpdate(mockAddDto, accessToken);

        // Then
        assertFalse(result.isOk());
        assertEquals("门店不能为空", result.getMsg());
    }

    @Test
    @DisplayName("测试添加播报计划 - 设备列表为空")
    void testAddOrUpdate_EmptyDeviceIds() {
        // Given
        String accessToken = "valid-token";
        mockAddDto.setDeviceIds(Collections.emptyList());

        when(redisService.findTokenVo(accessToken)).thenReturn(mockTokenVo);

        // When
        ResultBean<Boolean> result = broadcastPlanService.addOrUpdate(mockAddDto, accessToken);

        // Then
        assertFalse(result.isOk());
        assertEquals("关联设备不能为空", result.getMsg());
    }

    @Test
    @DisplayName("测试添加播报计划 - 播报类型为空")
    void testAddOrUpdate_NullType() {
        // Given
        String accessToken = "valid-token";
        mockAddDto.setType(null);

        when(redisService.findTokenVo(accessToken)).thenReturn(mockTokenVo);

        // When
        ResultBean<Boolean> result = broadcastPlanService.addOrUpdate(mockAddDto, accessToken);

        // Then
        assertFalse(result.isOk());
        assertEquals("播报类型不能为空", result.getMsg());
    }

    @Test
    @DisplayName("测试添加播报计划 - 自定义日期类型但日期为空")
    void testAddOrUpdate_CustomDateTypeWithNullDates() {
        // Given
        String accessToken = "valid-token";
        mockAddDto.setType("2"); // 自定义日期
        mockAddDto.setStartDate(null);
        mockAddDto.setEndDate(null);

        when(redisService.findTokenVo(accessToken)).thenReturn(mockTokenVo);

        // When
        ResultBean<Boolean> result = broadcastPlanService.addOrUpdate(mockAddDto, accessToken);

        // Then
        assertFalse(result.isOk());
        assertEquals("开始日期和结束日期不能为空", result.getMsg());
    }

    @Test
    @DisplayName("测试添加播报计划 - 间隔时间为空")
    void testAddOrUpdate_NullIntervalTime() {
        // Given
        String accessToken = "valid-token";
        mockAddDto.setIntervalTime(null);

        when(redisService.findTokenVo(accessToken)).thenReturn(mockTokenVo);

        // When
        ResultBean<Boolean> result = broadcastPlanService.addOrUpdate(mockAddDto, accessToken);

        // Then
        assertFalse(result.isOk());
        assertEquals("播放间隔时间不能为空", result.getMsg());
    }

    // ==================== delete 方法测试 ====================

    @Test
    @DisplayName("测试删除播报计划 - 成功删除")
    void testDelete_Success() {
        // Given
        Long planId = 1L;

        doNothing().when(broadcastPlanRepository).deleteById(planId);
        when(broadcastPlanVoiceWorkRepository.deleteByPlanId(planId)).thenReturn(1);

        // When
        ResultBean<Boolean> result = broadcastPlanService.delete(planId);

        // Then
        assertTrue(result.isOk());
        assertTrue(result.getResultData());

        // 验证删除操作被调用
        verify(broadcastPlanRepository, times(1)).deleteById(planId);
        verify(broadcastPlanVoiceWorkRepository, times(1)).deleteByPlanId(planId);
    }

    @Test
    @DisplayName("测试删除播报计划 - 删除不存在的计划")
    void testDelete_NonExistentPlan() {
        // Given
        Long planId = 999L;

        doNothing().when(broadcastPlanRepository).deleteById(planId);
        when(broadcastPlanVoiceWorkRepository.deleteByPlanId(planId)).thenReturn(0);

        // When
        ResultBean<Boolean> result = broadcastPlanService.delete(planId);

        // Then
        assertTrue(result.isOk());
        assertTrue(result.getResultData());

        // 验证删除操作仍然被调用（即使记录不存在）
        verify(broadcastPlanRepository, times(1)).deleteById(planId);
        verify(broadcastPlanVoiceWorkRepository, times(1)).deleteByPlanId(planId);
    }

    // ==================== 边缘情况和异常测试 ====================

    @Test
    @DisplayName("测试更新播报计划 - 计划不存在")
    void testAddOrUpdate_UpdateNonExistentPlan() {
        // Given
        String accessToken = "valid-token";
        mockAddDto.setId(999L); // 不存在的计划ID

        when(redisService.findTokenVo(accessToken)).thenReturn(mockTokenVo);
        when(broadcastPlanRepository.findById(999L)).thenReturn(Optional.empty());
        when(broadcastPlanVoiceWorkRepository.deleteByPlanId(anyLong())).thenReturn(0);
        when(broadcastPlanVoiceWorkRepository.saveAll(anyList())).thenReturn(Collections.emptyList());

        // When
        ResultBean<Boolean> result = broadcastPlanService.addOrUpdate(mockAddDto, accessToken);

        // Then
        assertTrue(result.isOk());
        assertTrue(result.getResultData());

        // 验证查找操作被调用，但没有更新操作
        verify(broadcastPlanRepository, times(1)).findById(999L);
        verify(broadcastPlanRepository, never()).save(any(BroadcastPlan.class));
    }

    @Test
    @DisplayName("测试添加播报计划 - 空的语音作品列表")
    void testAddOrUpdate_EmptyVoiceWorkList() {
        // Given
        String accessToken = "valid-token";
        mockAddDto.setId(null);
        mockAddDto.setVoiceWorkList(Collections.emptyList());

        when(redisService.findTokenVo(accessToken)).thenReturn(mockTokenVo);
        when(broadcastPlanRepository.save(any(BroadcastPlan.class))).thenReturn(mockBroadcastPlan);
        when(broadcastPlanVoiceWorkRepository.deleteByPlanId(anyLong())).thenReturn(0);

        // When
        ResultBean<Boolean> result = broadcastPlanService.addOrUpdate(mockAddDto, accessToken);

        // Then
        assertTrue(result.isOk());
        assertTrue(result.getResultData());

        // 验证保存操作被调用，但没有保存语音作品关联
        verify(broadcastPlanRepository, times(1)).save(any(BroadcastPlan.class));
        verify(broadcastPlanVoiceWorkRepository, times(1)).deleteByPlanId(anyLong());
        verify(broadcastPlanVoiceWorkRepository, never()).saveAll(anyList());
    }

    @Test
    @DisplayName("测试添加播报计划 - 大量语音作品")
    void testAddOrUpdate_LargeVoiceWorkList() {
        // Given
        String accessToken = "valid-token";
        mockAddDto.setId(null);
        List<Long> largeVoiceWorkList = new ArrayList<>();
        for (long i = 1; i <= 100; i++) {
            largeVoiceWorkList.add(i);
        }
        mockAddDto.setVoiceWorkList(largeVoiceWorkList);

        when(redisService.findTokenVo(accessToken)).thenReturn(mockTokenVo);
        when(broadcastPlanRepository.save(any(BroadcastPlan.class))).thenReturn(mockBroadcastPlan);
        when(broadcastPlanVoiceWorkRepository.deleteByPlanId(anyLong())).thenReturn(0);
        when(broadcastPlanVoiceWorkRepository.saveAll(anyList())).thenReturn(Collections.emptyList());

        // When
        ResultBean<Boolean> result = broadcastPlanService.addOrUpdate(mockAddDto, accessToken);

        // Then
        assertTrue(result.isOk());
        assertTrue(result.getResultData());

        // 验证保存了100个语音作品关联
        verify(broadcastPlanVoiceWorkRepository, times(1)).saveAll(argThat(list -> {
            List<BroadcastPlanVoiceWorkRef> refList = (List<BroadcastPlanVoiceWorkRef>) list;
            return refList.size() == 100 &&
                    refList.get(0).getSort() == 1 &&
                    refList.get(99).getSort() == 100;
        }));
    }

    @Test
    @DisplayName("测试列表查询 - 数据库异常")
    void testList_DatabaseException() {
        // Given
        List<Long> shopIds = Arrays.asList(1L);
        Integer pageSize = 10;
        Integer pageNumber = 0;

        when(jdbcTemplate.queryForObject(contains("select count(1)"), eq(Long.class), any(Object[].class)))
                .thenThrow(new RuntimeException("数据库连接异常"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            broadcastPlanService.list(shopIds, pageSize, pageNumber);
        });
    }

    @Test
    @DisplayName("测试添加播报计划 - 保存异常")
    void testAddOrUpdate_SaveException() {
        // Given
        String accessToken = "valid-token";
        mockAddDto.setId(null);

        when(redisService.findTokenVo(accessToken)).thenReturn(mockTokenVo);
        when(broadcastPlanRepository.save(any(BroadcastPlan.class)))
                .thenThrow(new RuntimeException("保存失败"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            broadcastPlanService.addOrUpdate(mockAddDto, accessToken);
        });
    }

    @Test
    @DisplayName("测试删除播报计划 - 删除异常")
    void testDelete_DeleteException() {
        // Given
        Long planId = 1L;

        doThrow(new RuntimeException("删除失败")).when(broadcastPlanRepository).deleteById(planId);

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            broadcastPlanService.delete(planId);
        });
    }

    // ==================== 辅助方法 ====================

    private TokenRedisVo createMockTokenVo() {
        TokenRedisVo tokenVo = new TokenRedisVo();
        tokenVo.setId(1L);
        tokenVo.setOpenid("test-openid");
        tokenVo.setNicknames("测试用户");
        return tokenVo;
    }

    private BroadcastPlanAddDto createMockAddDto() {
        BroadcastPlanAddDto dto = new BroadcastPlanAddDto();
        dto.setShopId(1L);
        dto.setDeviceIds(Arrays.asList(1L, 2L));
        dto.setStartTime("09:00");
        dto.setEndTime("18:00");
        dto.setStartDate("2024-01-01");
        dto.setEndDate("2024-12-31");
        dto.setType("1");
        dto.setIntervalTime("30");
        dto.setVoiceWorkList(Arrays.asList(1L, 2L));
        return dto;
    }

    private BroadcastPlan createMockBroadcastPlan() {
        BroadcastPlan plan = new BroadcastPlan();
        plan.setId(1L);
        plan.setShopId(1L);
        plan.setDeviceIds("1,2");
        plan.setStartTime("09:00");
        plan.setEndTime("18:00");
        plan.setStartDate("2024-01-01");
        plan.setEndDate("2024-12-31");
        plan.setType("1");
        plan.setIntervalTime("30");
        plan.setCreateTime(new Date());
        plan.setCreateUserId(1L);
        return plan;
    }

    private BroadcastPlanVo createMockBroadcastPlanVo(Long id, String storeName) {
        BroadcastPlanVo vo = new BroadcastPlanVo();
        vo.setId(id);
        vo.setStoreId(1L);
        vo.setStore(storeName);
        vo.setStartTime("09:00");
        vo.setEndTime("18:00");
        vo.setDeviceIds("1,2");
        vo.setDeviceSn("SN001,SN002");
        vo.setMusicIds("1,2");
        vo.setMusicName("音乐1,音乐2");
        vo.setType("1");
        vo.setIntervalTime("30");
        vo.setCreatedTime("2024-01-01 09:00:00");
        return vo;
    }
}
