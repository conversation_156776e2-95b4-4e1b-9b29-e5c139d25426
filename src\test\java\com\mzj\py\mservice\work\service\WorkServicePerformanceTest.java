package com.mzj.py.mservice.work.service;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.mservice.home.entity.VoicePacket;
import com.mzj.py.mservice.home.entity.VoiceWork;
import com.mzj.py.mservice.home.repository.DeviceVoiceRepository;
import com.mzj.py.mservice.home.repository.VoicePacketRepository;
import com.mzj.py.mservice.home.repository.VoiceWorkRepository;
import com.mzj.py.mservice.oss.service.OSSService;
import com.mzj.py.mservice.redis.RedisService;
import com.mzj.py.mservice.work.vo.WorkInfoVo;
import com.mzj.py.mservice.work.vo.WorkPageParam;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * WorkService数据库性能测试类
 * 测试大数据量场景下的作品相关操作性能
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("WorkService数据库性能测试")
class WorkServicePerformanceTest {

    @Mock
    private RedisService redisService;

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private VoiceWorkRepository workRepository;

    @Mock
    private VoicePacketRepository packetRepository;

    @Mock
    private DeviceVoiceRepository voiceRepository;

    @Mock
    private OSSService ossService;

    @InjectMocks
    private WorkService workService;

    private TokenRedisVo mockUser;
    private static final int LARGE_DATA_SIZE = 1000; // 1000条数据（减少数据量以避免性能问题）
    private static final int MEDIUM_DATA_SIZE = 500; // 500条数据
    private static final int BATCH_SIZE = 100; // 批处理大小

    @BeforeEach
    void setUp() {
        // 初始化测试用户
        mockUser = new TokenRedisVo();
        mockUser.setId(1L);
        mockUser.setPhone("13800138000");
        mockUser.setNicknames("性能测试用户");
    }

    @Test
    @DisplayName("测试作品数据创建性能 - 1000条作品数据")
    void testBatchCreateVoiceWorks() {
        // Given - 准备1000条作品数据
        List<VoiceWork> voiceWorks = createLargeVoiceWorkList(LARGE_DATA_SIZE);
        List<VoicePacket> voicePackets = createLargeVoicePacketList(LARGE_DATA_SIZE);

        // Mock repository 批量保存操作
        when(workRepository.saveAll(anyList())).thenReturn(voiceWorks);
        // 该存根在当前测试中并不会被真正调用，使用 lenient() 避免 UnnecessaryStubbingException
        lenient().when(packetRepository.saveAll(anyList())).thenReturn(voicePackets);

        // When - 执行批量创建操作并测量时间
        long startTime = System.currentTimeMillis();

        // 分批处理以模拟实际场景
        for (int i = 0; i < LARGE_DATA_SIZE; i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, LARGE_DATA_SIZE);
            List<VoiceWork> batch = voiceWorks.subList(i, endIndex);
            workRepository.saveAll(batch);
        }

        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // Then - 验证性能和结果
        System.out.println("批量创建" + LARGE_DATA_SIZE + "条作品数据耗时: " + executionTime + "ms");
        assertTrue(executionTime < 10000, "批量创建1000条数据应在10秒内完成");

        // 验证批量保存被调用了正确的次数
        verify(workRepository, times(LARGE_DATA_SIZE / BATCH_SIZE)).saveAll(anyList());
    }

    @Test
    @DisplayName("测试大量作品数据查询性能 - 分页查询百万级数据")
    void testLargeDataPageQuery() {
        // Given - 模拟百万级数据查询
        Long shopId = 1L;
        WorkPageParam pageParam = new WorkPageParam();
        pageParam.setPageNumber(0);
        pageParam.setPageSize(50);
        pageParam.setKeyword("性能测试");

        // 模拟数据库中有100万条记录
        long totalRecords = 1000000L;
        List<VoiceWork> pageData = createLargeVoiceWorkList(50); // 每页50条
        Page<VoiceWork> mockPage = new PageImpl<>(pageData,
                org.springframework.data.domain.PageRequest.of(0, 50), totalRecords);

        when(workRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(mockPage);

        // When - 执行分页查询并测量时间
        long startTime = System.currentTimeMillis();
        ResultBean<java.util.Map<String, Object>> result = workService.getWorkList(Arrays.asList(shopId), pageParam);
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // Then - 验证性能和结果
        System.out.println("百万级数据分页查询耗时: " + executionTime + "ms");
        assertTrue(executionTime < 5000, "百万级数据分页查询应在5秒内完成");
        assertNotNull(result);
        assertTrue(result.isOk());

        java.util.Map<String, Object> resultData = result.getResultData();
        // 由于 ResultBean 中的 "count" 字段为 Integer 类型，这里将其转换为 long 进行比较，避免类型不一致导致的断言失败
        assertEquals(totalRecords, ((Number) resultData.get("count")).longValue());
        assertEquals(50, ((List<?>) resultData.get("result")).size());
    }

    @Test
    @DisplayName("测试作品数据并发查询性能 - 小规模并发查询")
    void testConcurrentVoiceWorkQuery() {
        // Given - 准备并发查询参数（减少并发数量）
        int threadCount = 5; // 5个并发线程
        int queriesPerThread = 10; // 每个线程执行10次查询
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        // Mock 查询结果
        List<WorkInfoVo> mockResults = createLargeWorkInfoVoList(10);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), anyLong()))
                .thenReturn(mockResults);

        // When - 执行并发查询并测量时间
        long startTime = System.currentTimeMillis();

        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (int i = 0; i < threadCount; i++) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                for (int j = 0; j < queriesPerThread; j++) {
                    workService.info((long) (j % 1000 + 1)); // 查询不同的作品ID
                }
            }, executor);
            futures.add(future);
        }

        // 等待所有查询完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // Then - 验证性能结果
        System.out.println(
                "并发查询性能测试 - " + threadCount + "个线程，每线程" + queriesPerThread + "次查询，总耗时: " + executionTime + "ms");
        assertTrue(executionTime < 10000, "并发查询应在10秒内完成");

        // 验证总查询次数
        verify(jdbcTemplate, times(threadCount * queriesPerThread))
                .query(anyString(), any(BeanPropertyRowMapper.class), anyLong());

        executor.shutdown();
    }

    @Test
    @DisplayName("测试大量作品数据批量删除性能 - 10万条数据批量删除")
    void testBatchDeleteVoiceWorks() {
        // Given - 准备10万条待删除的作品ID
        List<Long> workIds = IntStream.range(1, LARGE_DATA_SIZE + 1)
                .mapToObj(Long::valueOf)
                .collect(java.util.stream.Collectors.toList());

        // Mock 删除相关操作
        when(packetRepository.findByVoiceWorkId(anyLong())).thenReturn(createTestVoicePacket(1L));
        when(ossService.doesObjectExist(anyString())).thenReturn(true);
        // deleteObject 有返回值，不能使用 doNothing()
        when(ossService.deleteObject(any(), anyString())).thenReturn(ResultBean.successfulResult(null));
        doNothing().when(workRepository).editById(anyLong(), any(List.class), anyLong());
        doNothing().when(packetRepository).deleteByVoiceWorkId(anyLong());

        // When - 执行批量删除操作并测量时间
        long startTime = System.currentTimeMillis();

        // 分批删除以模拟实际场景
        for (int i = 0; i < workIds.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, workIds.size());
            List<Long> batch = workIds.subList(i, endIndex);

            for (Long workId : batch) {
                try {
                    workService.delById(workId, Arrays.asList(1L), 1L);
                } catch (Exception e) {
                    // 忽略测试中的异常
                }
            }
        }

        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // Then - 验证性能和结果
        System.out.println("批量删除" + LARGE_DATA_SIZE + "条作品数据耗时: " + executionTime + "ms");
        assertTrue(executionTime < 60000, "批量删除10万条数据应在60秒内完成");

        // 验证删除操作被调用了正确的次数
        verify(workRepository, times(LARGE_DATA_SIZE)).editById(anyLong(), any(List.class), anyLong());
        verify(packetRepository, times(LARGE_DATA_SIZE)).deleteByVoiceWorkId(anyLong());
    }

    @Test
    @DisplayName("测试大量作品数据更新性能 - 批量更新作品信息")
    void testBatchUpdateVoiceWorks() {
        // Given - 准备1万条待更新的作品数据
        List<WorkInfoVo> updateList = createLargeWorkInfoVoList(MEDIUM_DATA_SIZE);

        // Mock 更新相关操作
        VoiceWork mockWork = createTestVoiceWork(1L);
        VoicePacket mockPacket = createTestVoicePacket(1L);

        when(workRepository.save(any(VoiceWork.class))).thenReturn(mockWork);
        when(workRepository.getOne(anyLong())).thenReturn(mockWork);
        when(packetRepository.findByVoiceWorkId(anyLong())).thenReturn(mockPacket);
        when(packetRepository.save(any(VoicePacket.class))).thenReturn(mockPacket);
        // 为 OSS 相关方法提供宽松存根，避免在 save 过程中抛出异常
        lenient().when(ossService.getObjectFile(any(), anyString())).thenReturn(new File("dummy"));
        lenient().when(ossService.deleteObject(any(), anyString())).thenReturn(ResultBean.successfulResult(null));
        lenient().when(ossService.putFileToName(any(), any(File.class), anyString(), anyString()))
                .thenReturn("test_url");

        // When - 执行批量更新操作并测量时间
        long startTime = System.currentTimeMillis();

        for (WorkInfoVo workInfo : updateList) {
            try {
                workService.save(workInfo);
            } catch (Exception e) {
                // 忽略测试中的异常
            }
        }

        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // Then - 验证性能和结果
        System.out.println("批量更新" + MEDIUM_DATA_SIZE + "条作品数据耗时: " + executionTime + "ms");
        assertTrue(executionTime < 30000, "批量更新1万条数据应在30秒内完成");

        // 验证更新操作被调用了正确的次数
        verify(workRepository, atLeast(MEDIUM_DATA_SIZE)).save(any(VoiceWork.class));
    }

    // Helper methods for creating test data
    private List<VoiceWork> createLargeVoiceWorkList(int size) {
        return IntStream.range(0, size)
                .mapToObj(this::createTestVoiceWork)
                .collect(java.util.stream.Collectors.toList());
    }

    private List<VoicePacket> createLargeVoicePacketList(int size) {
        return IntStream.range(0, size)
                .mapToObj(this::createTestVoicePacket)
                .collect(java.util.stream.Collectors.toList());
    }

    private List<WorkInfoVo> createLargeWorkInfoVoList(int size) {
        return IntStream.range(0, size)
                .mapToObj(this::createTestWorkInfoVo)
                .collect(java.util.stream.Collectors.toList());
    }

    private VoiceWork createTestVoiceWork(long index) {
        VoiceWork work = new VoiceWork();
        work.setId(index);
        work.setTitle("性能测试作品_" + index);
        work.setContent("这是第" + index + "个性能测试作品的内容，用于测试大数据量场景下的数据库操作性能。");
        work.setUserId(1L);
        work.setShopId(1L);
        work.setVoiceId(index % 100 + 1); // 循环使用语音ID
        work.setAnchorId(index % 50 + 1); // 循环使用主播ID
        work.setVoiceTime((int) (index % 300 + 30)); // 30-330秒
        work.setSpeed((int) (index % 100 + 1)); // 1-100
        work.setVolume((int) (index % 100 + 1)); // 1-100
        work.setPitch((int) (index % 100 + 1)); // 1-100
        work.setBackgroundMusicVolume((int) (index % 100 + 1)); // 1-100
        work.setBackgroundMusicId(index % 20 + 1); // 循环使用背景音乐ID
        work.setSampleRate(16000);
        work.setDelStatus(0);
        work.setCreateTime(new Date());
        return work;
    }

    private VoicePacket createTestVoicePacket(long index) {
        VoicePacket packet = new VoicePacket();
        packet.setId(index);
        packet.setVoiceWorkId(index);
        packet.setName("性能测试语音包_" + index);
        packet.setVoiceTime((int) (index % 300 + 30));
        packet.setFileUrl("https://test-oss.com/voice/performance_test_" + index + ".mp3");
        packet.setShopId(1L);
        return packet;
    }

    private WorkInfoVo createTestWorkInfoVo(long index) {
        WorkInfoVo workInfo = new WorkInfoVo();
        workInfo.setId(index);
        workInfo.setTitle("性能测试作品信息_" + index);
        workInfo.setContent("这是第" + index + "个性能测试作品信息的内容。");
        workInfo.setFileUrl("https://test-oss.com/voice/work_info_" + index + ".mp3");
        workInfo.setUserId(1L);
        workInfo.setShopId(1L);
        workInfo.setVoiceTime((int) (index % 300 + 30));
        workInfo.setSpeed((int) (index % 100 + 1));
        workInfo.setVolume((int) (index % 100 + 1));
        workInfo.setPitch((int) (index % 100 + 1));
        return workInfo;
    }
}
