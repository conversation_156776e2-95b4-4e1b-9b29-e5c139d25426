package com.mzj.py.config.oss;

import com.aliyun.oss.OSSClient;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
public class OssConfig {
	/**
	 * OSS配置
	 */
	@Value("${ali.oss.endpoint}")
	private String endpoint;
	@Value("${ali.oss.accessKeyId}")
	private String accessKeyId;
	@Value("${ali.oss.accessKeySecret}")
	private String accessKeySecret;

	@Value("${ali.oss.bucket.path}")
	private String fristFilePath;

	/**
	 * 对象存储桶名称
	 */
	@Value("${ali.oss.bucket.name}")
	private String ossBucketName;
	/**
	 * 桶路径
	 */
	@Value("${ali.oss.bucket.url}")
	private String bucketUrl;

	/**
	 * 图片下载临时目录
	 */
	@Value("${ali.oss.downFile.tempdir}")
	private String downFileTempdir;




	/**
	 * 客户端
	 *
	 * @return
	 * <AUTHOR>
	 * @date 2021年3月23日
	 */
	@Bean
	public OSSClient obsClient() {
		return new OSSClient(endpoint, accessKeyId, accessKeySecret);
	}



}
