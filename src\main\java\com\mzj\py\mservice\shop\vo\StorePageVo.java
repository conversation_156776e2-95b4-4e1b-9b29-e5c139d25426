package com.mzj.py.mservice.shop.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date: 2025/3/20
 * @description:
 * {"code":10000,"data":{"result":[{"id":0,"storeName":"","storeType":"","deviceCount":0,"merchantCount":0,"createTime":""}],"total_page":0,"total_record":0},"msg":"","success":""}
 *
 */
@Data
public class StorePageVo {

    // 门店id
    private Long id;

    // 门店名称
    private String storeName;

    // 门店类型
    private String storeType;

    // 设备数量
    private Integer deviceCount;

    // 商户数量
    private Integer merchantCount;

    // 创建时间
    private String createTime;

}
