package com.mzj.py.mservice.home.entity;

import com.mzj.py.mservice.wxuser.entity.IdEntity;
import lombok.Data;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2025-06-19-13:11
 */
@Data
@Entity
@Table(name = "dub_follow_anchor")
public class DubFollowAnchor extends IdEntity {
    @Basic
    @Column(name = "user_id")
    private Long userId;

    @Basic
    @Column(name = "anchor_id")
    private Long anchorId;
}
