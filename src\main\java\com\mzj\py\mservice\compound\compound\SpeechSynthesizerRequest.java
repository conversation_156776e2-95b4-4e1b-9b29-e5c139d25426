package com.mzj.py.mservice.compound.compound;

import com.alibaba.nls.client.AccessToken;
import com.alibaba.nls.client.protocol.NlsClient;
import com.alibaba.nls.client.protocol.OutputFormatEnum;
import com.alibaba.nls.client.protocol.SampleRateEnum;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizer;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizerListener;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizerResponse;
import com.mzj.py.commons.StatusCode;
import com.mzj.py.commons.StringUtils;
import com.mzj.py.commons.exception.BizException;
import com.mzj.py.mservice.compound.entity.SpeechSynthesizerDto;
import com.mzj.py.mservice.compound.utils.FfmpegUtil;
import com.mzj.py.mservice.compound.utils.FileUtil;
import com.mzj.py.mservice.redis.RedisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;

public class SpeechSynthesizerRequest {

    private static final String speechUrl = "wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1";

    private static final Logger logger = LoggerFactory.getLogger(SpeechSynthesizerRequest.class);

    private NlsClient client;

    private static String codeStatus = "200";
    private SpeechSynthesizerDto speechSynthesizerDto;


    public SpeechSynthesizerRequest(SpeechSynthesizerDto speechSynthesizerDto, RedisService redisService) {
        this.speechSynthesizerDto = speechSynthesizerDto;
        String token = redisService.getVoiceToken();
        if(StringUtils.isEmpty(token)){
            AccessToken accessToken = new AccessToken(speechSynthesizerDto.getAccessKeyId(), speechSynthesizerDto.getSecret());
            try {
                accessToken.apply();
            } catch (IOException e) {
                logger.error(e.getMessage(), e);
            }
            token = accessToken.getToken();
            // 有效期毫秒
            Long millisTime = accessToken.getExpireTime() - (System.currentTimeMillis() / 1000);
            // 缓存token
            redisService.putVoiceToken(token, millisTime);
        }

        logger.info("accessToken = {}", token);
        client = new NlsClient(speechUrl, token);
    }


    private static SpeechSynthesizerListener getSynthesizerListener(File file) {
        SpeechSynthesizerListener listener = null;
        try {
            listener = new SpeechSynthesizerListener() {
                FileOutputStream fout = new FileOutputStream(file);
                private boolean firstRecvBinary = true;

                //语音合成结束
                @Override
                public void onComplete(SpeechSynthesizerResponse response) {
                    logger.info("name: " + response.getName() + ", status: " + response.getStatus() + ", output file :" + file.getAbsolutePath());
                    try {
                        fout.close();
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }

                //语音合成的语音二进制数据
                @Override
                public void onMessage(ByteBuffer message) {
                    try {
                        if (firstRecvBinary) {
                            firstRecvBinary = false;
                        }
                        byte[] bytesArray = new byte[message.remaining()];
                        message.get(bytesArray, 0, bytesArray.length);
                        //System.out.println("write array:" + bytesArray.length);
                        fout.write(bytesArray);
                    } catch (IOException e) {
                        logger.error(e.getMessage(), e);
                    }
                }
                @Override
                public void onFail(SpeechSynthesizerResponse response) {
                    logger.info(
                            "task_id: " + response.getTaskId() +
                                    //状态码 20000000 表示识别成功
                                    ", status: " + response.getStatus() +
                                    //错误信息
                                    ", status_text: " + response.getStatusText());
                    if(response.getStatus()==41020001){
                        codeStatus= String.valueOf(41020001);
                    }
                }

                @Override
                public void onMetaInfo(SpeechSynthesizerResponse response) {
                    logger.info("MetaInfo event:{}" + response.getTaskId());
                }
            };
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return listener;
    }


    public String process(RedisService redisService) {
        SpeechSynthesizer synthesizer = null;
        try {
            /**
             * 2023-07-03 逻辑修改
             * 1.音频文件名改为长度为8位数的数字， 从00000001开始
             * 2.如果长度到了 9999，9999 ， 则重新从00000001开始
             * 3.存储到redis中
             *
             * 2023-07-17 逻辑修改
             * 1.合成音频的文件名改为文字转语音的第一个字（中文就是第一个字，英文取前两个字符）+6位数字
             * 2.如果长度到了 99，9999 ， 则重新从000001开始
             * 3.存储到redis中
             */
            //创建实例,建立连接
            File file = new File(  speechSynthesizerDto.getUrl() + FileUtil.getFileNewName(".mp3"));
            File templateFile = new File(  speechSynthesizerDto.getUrl() + FileUtil.getFileNewName(".mp3"));
            synthesizer = new SpeechSynthesizer(client, getSynthesizerListener(templateFile));
            synthesizer.setAppKey(speechSynthesizerDto.getAppKey());
            //设置返回音频的编码格式
            synthesizer.setFormat(OutputFormatEnum.MP3);
            if(speechSynthesizerDto.getSampleRate() != null){
                //设置返回音频的采样率
                synthesizer.setSampleRate(speechSynthesizerDto.getSampleRate());
            }else {
                synthesizer.setSampleRate(24000);
            }

            //发音人
            synthesizer.setVoice(speechSynthesizerDto.getVoice());
            if(speechSynthesizerDto.getVolume() != null){
                //音量
                if(speechSynthesizerDto.getVolume()==0){
                    speechSynthesizerDto.setVolume(1);
                }
                synthesizer.setVolume(speechSynthesizerDto.getVolume());
            }
            if(speechSynthesizerDto.getPitchRate() != null){
                //语调，范围是-500~500，可选，默认是0
                synthesizer.setPitchRate(speechSynthesizerDto.getPitchRate());
            }
            if(speechSynthesizerDto.getSpeechRate() != null){
                //语速，范围是-500~500，默认是0
                synthesizer.setSpeechRate(speechSynthesizerDto.getSpeechRate());
            }
            if(speechSynthesizerDto.getIsEmotion() ==1) {
                synthesizer.setText("<speak>" +" <emotion category=\""+speechSynthesizerDto.getEmotion()+"\" intensity=\"2.0\">"
                        + speechSynthesizerDto.getText()+"</emotion>" + " </speak>");
            }else {
                synthesizer.setText("<speak>" + speechSynthesizerDto.getText()+ " </speak>");
            }
            synthesizer.addCustomedParam("enable_subtitle", true);

            //此方法将以上参数设置序列化为json发送给服务端,并等待服务端确认
            long start = System.currentTimeMillis();
            synthesizer.start();
            logger.info("tts start latency " + (System.currentTimeMillis() - start) + " ms");

            //等待语音合成结束
            synthesizer.waitForComplete();
            if(!StringUtils.isEmpty(speechSynthesizerDto.getBgm())){
                String bgmFileName = speechSynthesizerDto.getUrl() +FileUtil.getFileNewName(".wav");
                File bgmFile=FileUtil.taiseng(speechSynthesizerDto.getBgm(),bgmFileName,speechSynthesizerDto.getBugRate());
                File outFile=new File(speechSynthesizerDto.getUrl() +FileUtil.getFileNewName(".wav"));
                FfmpegUtil.mixBgm(templateFile, bgmFile, outFile,speechSynthesizerDto.getBeforeDelay(),speechSynthesizerDto.getAfterDelay(),speechSynthesizerDto.getBgmCenterVolum());
                if(speechSynthesizerDto.getIsHeighVoice()==1){
                    FileUtil.coverToMp3Heigh(outFile, file);
                }else{
                    FileUtil.coverToMp3(outFile, file);
                }
            }else{
                if(speechSynthesizerDto.getIsHeighVoice()==1){
                    FileUtil.coverToMp3Heigh(templateFile, file);
                }else{
                    FileUtil.coverToMp3(templateFile, file);
                }
            }
            logger.info("tts stop latency " + (System.currentTimeMillis() - start) + " ms");
            logger.info("url = {}", file.getAbsolutePath());
            return file.getAbsolutePath();//返回存储的内存地址
        } catch (Exception e) {
            if(codeStatus.equals("41020001")){
                codeStatus="200";
                throw new BizException(StatusCode.ERROR_CODE_20012);
            }
            logger.error(e.getMessage(), e);
        } finally {
            //关闭连接`
            if (null != synthesizer) {
                synthesizer.close();
            }
        }
        return null;
    }
    public void shutdown() {
        client.shutdown();
    }
}
