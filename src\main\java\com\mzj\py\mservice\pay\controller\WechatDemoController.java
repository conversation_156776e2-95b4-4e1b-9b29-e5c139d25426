package com.mzj.py.mservice.pay.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mzj.py.mservice.pay.conf.WxpayConfig;
import com.mzj.py.mservice.pay.util.*;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import javax.servlet.http.HttpServletRequest;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("/wechatPay")
@Log4j2
public class WechatDemoController {




    /**
     * SUCCESS：支付成功
     *
     * REFUND：转入退款
     *
     * NOTPAY：未支付
     *
     * CLOSED：已关闭
     *
     * REVOKED：已撤销（仅付款码支付会返回）
     *
     * USERPAYING：用户支付中（仅付款码支付会返回）
     *
     * PAYERROR：支付失败（仅付款码支付会返回）
    * */


    /**
     * 取消订单
     *
     * @param orderId 订单ID
     * @return 取消订单的结果
     */
   @GetMapping("/cancelOrder")
    public void cancelOrder(String orderId) {
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> refundParams = new HashMap<>();
            refundParams.put("appid", WxpayConfig.app_id); // 公众号ID
            refundParams.put("mchid", WxpayConfig.mch_id); // 商户号
            refundParams.put("out_trade_no", orderId); // 微信订单号

            ObjectMapper objectMapper = new ObjectMapper();
            String refundBody = "";

            refundBody = objectMapper.writeValueAsString(refundParams);
            // 调用关闭订单API
            Map<String, Object> stringObjectMap = HttpUtils.doPostWexin("https://api.mch.weixin.qq.com/v3/pay/transactions/out-trade-no/" + orderId + "/close", refundBody);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("code", "fail");
            result.put("message", "系统错误：" + e.getMessage());
        }
    }
}
