package com.mzj.py.mservice.home.repository;


import com.mzj.py.mservice.home.entity.CategoryAiKey;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface CategoryAiKeyRepository extends JpaRepository<CategoryAiKey,Long>, JpaSpecificationExecutor<CategoryAiKey> {

    CategoryAiKey findByCategoryId(Integer categoryId);
}
