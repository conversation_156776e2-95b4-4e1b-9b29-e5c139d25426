package com.mzj.py.mservice.home.repository;


import com.mzj.py.mservice.home.entity.UserRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface UserRecordRepository extends JpaRepository<UserRecord, Long>, JpaSpecificationExecutor<UserRecord> {
    @Query(value = "SELECT * FROM user_record ur WHERE ur.user_id = ?1 AND ur.archor_type = ?2 AND DATE(ur.gmt_create) = CURRENT_DATE", nativeQuery = true)
    List<UserRecord> findByUserIdAndArchorTypeOnToday(Long userId, String archorType);
}
