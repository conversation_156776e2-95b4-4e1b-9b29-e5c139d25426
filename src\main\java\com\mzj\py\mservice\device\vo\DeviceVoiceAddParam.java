package com.mzj.py.mservice.device.vo;

import lombok.Data;

import java.util.List;

/**
 * 设备与语音包表
 *
 * @author: duanjinze
 * @date: 2022/11/10 13:40
 * @version: 1.0
 */
@Data
public class DeviceVoiceAddParam {

//    音频标题，语速语调音量，背景音乐id，背景音乐音量，主播id，音频文本内容，设备id是数组
    /**
     * 设备ids
     */
    private List<Long> deviceIds;
    /**
     * 音频标题
     */
    private String title;

    /**
     * 设备音频id
     */
    private Long deviceVoiceId;
    /**
     * 背景音乐id
     */
    private Long backgroundMusicId;
    /**
     * 背景音乐音量
     */
    private Integer backgroundMusicVolume;
    /**
     * 主播id
     */
    private Long voiceId;

    /**
     * 音频文本内容
     */
    private String content;

    private Long shopId;

    private Long userId;

    /**
     * 播放时长
     */
    private Integer time;

    /**
     * 音频地址
     */
    private String url;

    private Integer pitch;

    private Integer speed;

    private Integer volume;

    private Integer type;
}
