package com.mzj.py.mservice.shop.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date: 2025/3/20
 * @description:
 * {"code":10000,"data":{"result":[{"id":0,"username":"","phone":"","createTime":""}],"total_page":0,"total_record":0},"msg":"","success":""}
 */
@Data
public class StoreUserPageVo {

    // 用户id
    private Long id;

    // 用户名
    private String username;

    // 手机号
    private String phone;

    // 创建时间
    private String createTime;

    // 角色 1 管理员 2 店员
    private Integer role;

}
