package com.mzj.py.mservice.redis;

import com.alibaba.fastjson.JSON;
import com.mzj.py.commons.RedisKeyConstant;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.mservice.wxuser.entity.WxUser;
import com.mzj.py.mservice.wxuser.repository.WxUserRepository;
import org.apache.commons.lang3.StringUtils;
import org.apache.tools.ant.taskdefs.optional.depend.constantpool.LongCPInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Random;
import java.util.concurrent.TimeUnit;

@Service
public class RedisService {
    private Logger LOG = LoggerFactory.getLogger(RedisService.class);
    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate redisTemplate;
    @Autowired
    private WxUserRepository wxUserRepository;

    /**
     * 获取系统参数数据
     *
     * @param key
     * @return
     */
    public String getValue(String key) {
        Object object = redisTemplate.opsForHash().get(RedisKeyConstant.MZJ_PY_SYS_PARAMS, key);
        if (object == null) {
            return null;
        }

        return object.toString();
    }

    /**
     * 将数据放入set缓存
     *
     * @param key    键
     * @param values 值 可以是多个
     * @return
     */
    public void sSet(String key, Object values) {
        redisTemplate.opsForHash().put(RedisKeyConstant.MZJ_PY_SYS_PARAMS, key, (String) values);
    }

    /**
     * 查询用户TOKEN缓存信息
     *
     * @param token
     * @return
     * <AUTHOR>
     * @date 2021年1月26日
     */
    public TokenRedisVo findTokenVo(String token) {
        if (token.equals("123")) {
            TokenRedisVo vo = new TokenRedisVo();
            WxUser wxUser = wxUserRepository.findByOpenid("123");
            LOG.info("findTokenVo WxUser = {}", wxUser);
            vo.setId(wxUser.getId());
            vo.setOpenid(wxUser.getOpenid());
            vo.setAvatar(wxUser.getAvatar());
            vo.setGender(wxUser.getGender());
            vo.setPhone(wxUser.getPhone());
            vo.setArea(wxUser.getArea());
            vo.setNicknames(wxUser.getNicknameToString());
            return vo;
        }

        Object object = redisTemplate.opsForValue().get(token);
        if (object != null) {
            TokenRedisVo vo = JSON.parseObject(object.toString(), TokenRedisVo.class);
            return vo;
        }
        return null;
    }

    /**
     * 缓存短信验证码
     *
     * @param mobile
     * @param code
     * <AUTHOR>
     * @date 2021年3月19日
     */
    public void putSmsCode(String mobile, String code) {
        redisTemplate.opsForValue().set(code, mobile, 300, TimeUnit.SECONDS);
    }

    /**
     * 删除短信验证码
     *
     * @param code
     * <AUTHOR>
     * @date 2021年3月19日
     */
    public void deleteSmsCode(String code) {
        redisTemplate.delete(code);
    }

    /**
     * 获取验证码手机号
     *
     * @param code
     * @return
     * <AUTHOR>
     * @date 2021年3月19日
     */
    public String getSmsCodeMobile(String code) {
        return redisTemplate.opsForValue().get(code);
    }


    /**
     * 缓存语音合成token
     *
     * @param token
     * @param time
     */
    public void putVoiceToken(String token, Long time) {
        redisTemplate.opsForValue().set(RedisKeyConstant.VOICE_TOKEN, token, time, TimeUnit.SECONDS);
    }

    /**
     * 获取语音合成token
     *
     * @return
     */
    public String getVoiceToken() {
        return redisTemplate.opsForValue().get(RedisKeyConstant.VOICE_TOKEN);
    }

    /**
     * 缓存语音合成名称序号
     *
     * @param number
     */
    public void putVoiceNumber(Integer number,Long userId) {
        String key = String.format(RedisKeyConstant.VOICE_NUMBER, userId);
        redisTemplate.opsForValue().set(key, String.valueOf(number));
        redisTemplate.expire(String.format(RedisKeyConstant.VOICE_NUMBER, userId),600,TimeUnit.HOURS);
    }

    /**
     * 获取语音合成名称序号
     *
     * @return
     */
    public Integer getVoiceNumber(Long userId) {
        String key = String.format(RedisKeyConstant.VOICE_NUMBER, userId);
        Random random = new Random();
        Integer number = null;
        String voiceNumber = redisTemplate.opsForValue().get(key);
        if (StringUtils.isEmpty(voiceNumber)) {
            number = random.nextInt(99) + 1;
        } else {
            number = Integer.parseInt(voiceNumber);
            number = number + 1;
            // 如果长度到了 99，9999 ， 则重新从000001开始
            if (number > 99) {
                number = random.nextInt(99) + 1;
            }
        }
        return number;
    }

    /**
     * 合成判断重复
     *
     * @param dto
     * @param time
     */
    public void setvoiceRepeat(Long userId, String dto, Long time) {
        redisTemplate.opsForValue().set(String.format(RedisKeyConstant.PY_REPEAT, userId), dto, time, TimeUnit.MILLISECONDS);
    }

    /**
     * 合成判断重复
     *
     * @return
     */
    public String getvoiceRepeat(Long userId) {
        return redisTemplate.opsForValue().get(String.format(RedisKeyConstant.PY_REPEAT, userId));
    }


    public String getPyLockNum(String taskId) {
        return redisTemplate.opsForValue().get(String.format(RedisKeyConstant.PYLOCK_NUM, taskId));
    }

    public void incrPyLockNum(String taskId) {
        redisTemplate.opsForValue().increment(String.format(RedisKeyConstant.PYLOCK_NUM, taskId), 1);
        redisTemplate.expire(String.format(RedisKeyConstant.PYLOCK_NUM, taskId), 5, TimeUnit.SECONDS);
    }
}
