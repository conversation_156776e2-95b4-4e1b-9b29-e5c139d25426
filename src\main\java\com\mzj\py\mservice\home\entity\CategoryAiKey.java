package com.mzj.py.mservice.home.entity;

import com.mzj.py.mservice.wxuser.entity.IdEntity;
import lombok.Data;

import javax.persistence.*;

@Entity
@Table(name = "category_ai")
@Data
public class CategoryAiKey extends IdEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Basic
    @Column(name = "agent_key")
    private String agentKey;

    @Basic
    @Column(name = "app_id")
    private String appId;

    @Basic
    @Column(name = "prompt_id")
    private String promptId;

    @Basic
    @Column(name = "prompt",columnDefinition = "text")
    private String prompt;

    @Basic
    @Column(name = "api_key")
    private String apiKey;

    @Basic
    @Column(name = "category_id",columnDefinition = "smallint")
    private Integer categoryId;
}
