package com.mzj.py.commons.exception;

import com.mzj.py.commons.StatusCode;
import lombok.Data;


@Data
public class BizException extends RuntimeException {

    private String code;
    private String msg;
    private String detail;
    public BizException(String code, String message) {
        super(message);
        this.code = code;
        this.msg = message;
    }

    public BizException(StatusCode statusCode){
        super(statusCode.getErrorMsg());
        this.code = statusCode.getErrorCode();
        this.msg = statusCode.getErrorMsg();
    }

    public BizException(StatusCode statusCode, Exception e){
        super(statusCode.getErrorMsg());
        this.code = statusCode.getErrorCode();
        this.msg = statusCode.getErrorMsg();
        this.detail = e.toString();
    }
}
