package com.mzj.py.mservice.broadcastPlan.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class BroadcastPlanVo {

    private Long id;
    //门店名称
    private String store;
    //开始时间
    private String startDate;
    //结束时间
    private String endDate;
    //开始时段
    private String startTime;
    //结束时段
    private String endTime;
    //门店id
    private Long storeId;

    //设备ids
    private String deviceIds;
    //设备号  222,444,555
    private String deviceSn;

    //作品ids
    private String musicIds;
    //作品  333,444,555
    private String musicName;

    //类型
    private String type;
    //间隔时间
    private String intervalTime;

    //创建时间
    private String createdTime;


    public String[] getDeviceSn() {
        String str = this.deviceSn;
        if (str != null) {
            return str.split(",");
        }
        return null;
    }

    public String[] getMusicName() {
        String str = this.musicName;
        if (str != null) {
            return str.split(",");
        }
        return null;
    }

    public String[] getMusicIds() {
        String str = this.musicIds;
        if (str != null) {
            return str.split(",");
        }
        return null;
    }

    public String[] getDeviceIds() {
        String str = this.deviceIds;
        if (str != null) {
            return str.split(",");
        }
        return null;
    }

}
