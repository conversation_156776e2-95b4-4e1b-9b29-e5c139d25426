package com.mzj.py.mservice.wxuser.vo;

/**
 * 押金vo
 * 
 * <AUTHOR>
 * @Date 2019-12-5
 *
 */
public class WxDepositVo {
	private Long userId;// 用户id
	private Integer depositFee;// 押金金额
	private Long deviceId;// 设备号

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Integer getDepositFee() {
		return depositFee;
	}

	public void setDepositFee(Integer depositFee) {
		this.depositFee = depositFee;
	}

	public Long getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(Long deviceId) {
		this.deviceId = deviceId;
	}

	@Override
	public String toString() {
		return "WxDepositVo [userId=" + userId + ", depositFee=" + depositFee + ", deviceId=" + deviceId + "]";
	}

}
