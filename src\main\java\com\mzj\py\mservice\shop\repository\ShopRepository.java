package com.mzj.py.mservice.shop.repository;

import com.mzj.py.mservice.shop.entity.Shop;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface ShopRepository extends JpaRepository<Shop,Long>, JpaSpecificationExecutor<Shop> {


    @Transactional
    @Modifying
    @Query(value = "update dub_shop set status = ?2 where id = ?1 ",nativeQuery = true)
    Integer updateStatusById(Long id, Integer status);

    @Modifying
    @Query("delete from Shop where id in ?1")
    void deleteByIds(List<Long> shopIds);

    @Query("select s.id from Shop s where s.parentId in ?1")
    List<Long> selectByParentIdIn(List<Long> shopIds);

    List<Shop> findByParentId(Long id);
}
