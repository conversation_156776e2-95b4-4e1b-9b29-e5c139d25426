package com.mzj.py.mservice.pay.conf;

import lombok.Data;
import org.springframework.stereotype.Component;

import java.security.cert.X509Certificate;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Data
@Component
public class WxpayConfig {
    public static String app_id = "wxfdb8bf236b7e23bc"; // 公众账号ID  改

    public static String mch_id = "1675024609"; // 商户号 改a

    public static String mchSerialNo = "7FFC26A64E2F65E48FCE52E381B9F79330B74BA0"; //微信商家api序列号 改

    public static String v3Key = "13858985525lanxishihongjingdianz"; // 回调报文解密V3密钥key  改

    public static String KeyPath = "apiclient_key.pem"; // 商户的key【API密匙】存放路径 改



    public static String notify_order_url = "https://ht.at1984.com/prod-api/mini/manager/order/callback"; // 服务器异步通知页面路径--下单

    public static String notify_refound_url = "https://ht.at1984.com/prod-api/mini/manager/order/refundCallback"; // 服务器异步通知页面路径-退款

    public static String return_url = "*************************************"; // 服务器同步通知页面路径


    public static Map<String, X509Certificate> certificateMap = new ConcurrentHashMap<>(); // 定义全局容器 保存微信平台证书公钥

}
