package com.mzj.py.mservice.home.entity;

import lombok.Data;

import javax.persistence.*;

/**
 * aikey
 */
@Entity
@Table(name = "ai_eye_key")
@Data
public class AiEyeKey{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Basic
    @Column(name = "agent_key")
    private String agentKey;

    @Basic
    @Column(name = "app_id")
    private String appId;

    @Basic
    @Column(name = "prompt_id")
    private String promptId;
    @Basic
    @Column(name = "prompt",columnDefinition = "text")
    private String prompt;

    @Basic
    @Column(name = "api_key")
    private String apiKey;
}
