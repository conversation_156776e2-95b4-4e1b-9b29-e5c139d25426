package com.mzj.py.mservice.installationPackage.controller;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.mservice.installationPackage.service.InstallationPackageLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 安装包日志
 */
@Controller
@RequestMapping("/mini/installationPackageLog")
public class InstallationPackageLogController {

    @Autowired
    private InstallationPackageLogService installationPackageLogService;

    /**
     * 分页查询
     * @param accessToken
     * @param pageSize
     * @param pageNumber
     * @param installationPackageId
     * @return
     */
    @GetMapping
    @ResponseBody
    public ResultBean<Map<String, Object>> list(@RequestHeader String accessToken,
                                                @RequestParam(name = "pageSize",defaultValue = "10") Integer pageSize,
                                                @RequestParam(name = "pageNumber",defaultValue = "0") Integer pageNumber,
                                                @RequestParam(name = "installationPackageId")Long installationPackageId) {
        return installationPackageLogService.list(accessToken, pageSize, pageNumber, installationPackageId);
    }


}
