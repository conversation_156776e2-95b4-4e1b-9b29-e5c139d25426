package com.mzj.py.mservice.installationPackage.service;

import cn.hutool.core.collection.CollUtil;
import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.mservice.installationPackage.vo.installationPackageLog.InstallationPackageLogVo;
import com.mzj.py.mservice.redis.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class InstallationPackageLogService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private RedisService redisService;


    public ResultBean<Map<String, Object>> list(String accessToken, Integer pageSize, Integer pageNumber,Long installationPackageId) {


        TokenRedisVo tokenVo = redisService.findTokenVo(accessToken);
        if (tokenVo == null) {
            return ResultBean.failedResultWithMsg("用户不存在");
        }

        List<String> query = new ArrayList<>();
        List<Object> args = new ArrayList<>();
        if(installationPackageId != null){
            query.add(" l.package_id = ? ");
            args.add(installationPackageId.toString());
        }
        StringBuilder where = new StringBuilder();
        if (CollUtil.isNotEmpty(query)) {
            where.append(" WHERE ");
            where.append(String.join(" AND ", query));
        }

        String sql = "select " +
                "l.id," +
                "s.shop_name as store," +
                "d.`name` deviceName," +
                "d.sn as deviceId," +
                "p.version_name as version ," +
                "l.create_time as completionTime," +
                "l.response_time as updateTime," +
                "l.status " +
                "from dub_installation_package_log l " +
                "left join dub_installation_package p on l.package_id = p.id " +
                "left join dub_device d on l.device_id = d.id " +
                "left join dub_shop s on s.id=d.shop_id " ;

        Map<String, Object> map = new HashMap<>();
        where.append("order by l.create_time DESC  ");

        Long count = jdbcTemplate.queryForObject("select count(1) from(" + sql + where + ")t ", Long.class, args);
        if (count == null || count == 0) {
            map.put("count", 0);
            map.put("result", null);
            return ResultBean.successfulResult(map);
        }
        where.append(" limit ? offset ? ");
        args.add(pageSize);
        args.add(pageSize * pageNumber);
        List<InstallationPackageLogVo> devices = jdbcTemplate.query(sql + where, new BeanPropertyRowMapper<>(InstallationPackageLogVo.class), args);
        map.put("count", count);
        map.put("result", devices);
        return ResultBean.successfulResult(map);
    }



}
