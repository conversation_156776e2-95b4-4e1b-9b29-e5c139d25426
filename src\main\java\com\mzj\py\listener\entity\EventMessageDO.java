package com.mzj.py.listener.entity;

import com.mzj.py.mservice.wxuser.entity.IdEntity;
import lombok.Data;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name ="event_message")
@Data
public class EventMessageDO extends IdEntity {

    /**
     * 消息队列的消息id
     */
    @Basic
    @Column(name = "message_id", nullable = true)
    private String messageId;


    /**
     * 事件类型
     */
    @Basic
    @Column(name = "event_message_type", nullable = true)
    private String eventMessageType;


    /**
     * 业务id
     */
    @Basic
    @Column(name = "biz_id", nullable = true)
    private String bizId;


    /**
     * 用户id
     */
    @Basic
    @Column(name = "user_id", nullable = true)
    private Long userId;


    /**
     * 用户openId
     */
    @Basic
    @Column(name = "open_id", nullable = true)
    private String openId;

    /**
     * 用户unionId
     */
    @Basic
    @Column(name = "union_id", nullable = true)
    private String unionId;


    /**
     * 消息体
     */
    @Basic
    @Column(name = "content", nullable = true)
    private String content;

    /**
     * 备注
     */
    @Basic
    @Column(name = "remark", nullable = true)
    private String remark;
}
