package com.mzj.py.mservice.installationPackage.vo.installationPackageLog;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class InstallationPackageLogVo {

    private Long id;

    private String store;

    private String version;

    private String deviceId;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date completionTime;

    private Integer status;


}
