package com.mzj.py.mservice.home.entity;

import com.mzj.py.mservice.wxuser.entity.IdEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;


@Entity
@Table(name = "long_anchor")
public class LongAnchor extends IdEntity {
    /**
     * 名称
     */
    private String name;

    /**
     * voice参数
     */
    private String voiceName;

    /**
     * 类型名称
     */
    private String typeName;

    /**
     * 使用场景
     */
    private String usageScenario;

    /**
     * 1中文及英文混合 2纯中文场景 3标准粤文
     */
    @Column(name = "support_voice_type",columnDefinition = "smallint")
    private Integer supportVoiceType;

    /**
     * 支持采样率
     */
    private Integer sampleRate;

    /**
     * 支持时间戳
     */
    @Column(name = "enable_subtitle",columnDefinition = "smallint")
    private Integer enableSubtitle;

    /**
     * 支持儿化音
     */
    @Column(name = "rhotic_accent",columnDefinition = "smallint")
    private Integer rhoticAccent;
    @Column(name = "url")
    private String url;
    @Column(name = "type")
    private String type;
    @Column(name = "voice_url")
    private String voiceUrl;

    @Column(name = "vip",columnDefinition = "smallint")
    private Integer vip;
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVoiceName() {
        return voiceName;
    }

    public void setVoiceName(String voiceName) {
        this.voiceName = voiceName;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getUsageScenario() {
        return usageScenario;
    }

    public void setUsageScenario(String usageScenario) {
        this.usageScenario = usageScenario;
    }

    public Integer getSupportVoiceType() {
        return supportVoiceType;
    }

    public void setSupportVoiceType(Integer supportVoiceType) {
        this.supportVoiceType = supportVoiceType;
    }

    public Integer getSampleRate() {
        return sampleRate;
    }

    public void setSampleRate(Integer sampleRate) {
        this.sampleRate = sampleRate;
    }

    public Integer getEnableSubtitle() {
        return enableSubtitle;
    }

    public void setEnableSubtitle(Integer enableSubtitle) {
        this.enableSubtitle = enableSubtitle;
    }

    public Integer getRhoticAccent() {
        return rhoticAccent;
    }

    public void setRhoticAccent(Integer rhoticAccent) {
        this.rhoticAccent = rhoticAccent;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getVoiceUrl() {
        return voiceUrl;
    }

    public void setVoiceUrl(String voiceUrl) {
        this.voiceUrl = voiceUrl;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getVip() {
        return vip;
    }

    public void setVip(Integer vip) {
        this.vip = vip;
    }
}
