package com.mzj.py.mservice.order.repository;

import com.mzj.py.mservice.order.entity.Order;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

public interface OrderRepository extends JpaRepository<Order,Long>, JpaSpecificationExecutor<Order> {

    @Transactional
    @Modifying
    @Query(value = "update dub_order set  time_expire = null , status = 1 where id =  ?1 ",nativeQuery = true)
    Integer paySuccess(Long orderId);

    @Modifying
    @Query(value = "select *from dub_order  where out_trade_no =  ?1 ",nativeQuery = true)
    List<Order> getByNo(String outTradeNo);

    @Transactional
    @Modifying
    @Query(value = "update dub_order set status=?3 where openid = ?2 and out_trade_no=?1 and status=?4",nativeQuery = true)
    Integer updateOrderState(String outTradeNo, String openId, int ordinal, int ordinal1);

    @Transactional
    @Modifying
    @Query(value = "update dub_order set transaction_id = ?3,pay_time=?4,pay_type=?5,trade_state=?6,status=?7 where openid = ?2 and out_trade_no=?1 and status=?8",nativeQuery = true)
    Integer updateOrderPayState(String outTradeNo, String openId, String transactionId, Date successTime, String tradeType, String tradeState, String afterName, String beforeName);

    Order findByOutTradeNoAndOpenid(String orderNo, String openId);

    @Transactional
    @Modifying
    @Query(value = "update dub_order set status=?3,trade_state=?5 where openid = ?2 and out_trade_no=?1 and status=?4",nativeQuery = true)
    Integer updateOrderAndWxState(String outTradeNo, String openId, String afterName, String beforeName,String tradeState);
}
