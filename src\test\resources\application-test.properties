# 测试环境配置文件
# 数据库配置
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA配置
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Redis配置（使用嵌入式Redis进行测试）
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.database=0

# OSS配置
ali.oss.downFile.tempdir=/tmp/test/
ali.oss.bucket.url=https://test-oss.com/

# 日志配置
logging.level.com.mzj.py=DEBUG
logging.level.org.springframework.web=DEBUG
