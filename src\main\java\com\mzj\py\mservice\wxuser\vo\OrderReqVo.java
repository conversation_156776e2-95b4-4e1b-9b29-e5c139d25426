package com.mzj.py.mservice.wxuser.vo;

/**
 * 先付费后使用创建订单vo
 * <AUTHOR>
 *
 */
public class OrderReqVo {

	private Long deviceId;// 设备id
	private Long userId;// 用户id
	private Long payFirstPlansId;//先付费套餐id
	private Integer payCoin;//支付的币
	private Integer payFee;//支付的现金
	private Integer minute;//分钟数
	private Integer price;//套餐价格
	private Long userCouponId;//用户优惠券id

	public Long getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(Long deviceId) {
		this.deviceId = deviceId;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getPayFirstPlansId() {
		return payFirstPlansId;
	}

	public void setPayFirstPlansId(Long payFirstPlansId) {
		this.payFirstPlansId = payFirstPlansId;
	}

	public Integer getPayCoin() {
		return payCoin;
	}

	public void setPayCoin(Integer payCoin) {
		this.payCoin = payCoin;
	}

	public Integer getPayFee() {
		return payFee;
	}

	public void setPayFee(Integer payFee) {
		this.payFee = payFee;
	}

	public Long getUserCouponId() {
		return userCouponId;
	}

	public void setUserCouponId(Long userCouponId) {
		this.userCouponId = userCouponId;
	}

	public Integer getMinute() {
		return minute;
	}

	public void setMinute(Integer minute) {
		this.minute = minute;
	}

	public Integer getPrice() {
		return price;
	}

	public void setPrice(Integer price) {
		this.price = price;
	}

	@Override
	public String toString() {
		return "OrderReqVo [deviceId=" + deviceId + ", userId=" + userId + ", payFirstPlansId=" + payFirstPlansId
				+ ", payCoin=" + payCoin + ", payFee=" + payFee + ", minute=" + minute + ", price=" + price
				+ ", userCouponId=" + userCouponId + "]";
	}

}
