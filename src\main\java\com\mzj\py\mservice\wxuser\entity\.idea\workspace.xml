<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="8520f91e-379e-43ec-a99b-e2a64e4ce0f4" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/../../device/service/DeviceService.java" beforeDir="false" afterPath="$PROJECT_DIR$/../../device/service/DeviceService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../home/<USER>/AnchorService.java" beforeDir="false" afterPath="$PROJECT_DIR$/../../home/<USER>/AnchorService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../work/service/WorkService.java" beforeDir="false" afterPath="$PROJECT_DIR$/../../work/service/WorkService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../resources/application-dev.properties" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../resources/application-dev.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../resources/application-prod.properties" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../resources/application-prod.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../resources/application-uat.properties" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../resources/application-uat.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../resources/application-ver.properties" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../resources/application-ver.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../test/java/com/mzj/py/mservice/device/service/DeviceServiceIntegrationTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../test/java/com/mzj/py/mservice/device/service/DeviceServiceIntegrationTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../test/java/com/mzj/py/mservice/device/service/DeviceServicePerformanceTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../test/java/com/mzj/py/mservice/device/service/DeviceServicePerformanceTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../test/java/com/mzj/py/mservice/device/service/DeviceServiceTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../test/java/com/mzj/py/mservice/device/service/DeviceServiceTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../test/java/com/mzj/py/mservice/home/<USER>/AnchorControllerTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../test/java/com/mzj/py/mservice/home/<USER>/AnchorControllerTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../test/java/com/mzj/py/mservice/my/controller/MyControllerTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../test/java/com/mzj/py/mservice/my/controller/MyControllerTest.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectId" id="30MEnejyzTXcY46g3VqKlt0XbUe" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "last_opened_file_path": "D:/yny/ai/二期交付代码",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="8520f91e-379e-43ec-a99b-e2a64e4ce0f4" name="Changes" comment="" />
      <created>1753429210026</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753429210026</updated>
      <workItem from="1753429211447" duration="7000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>