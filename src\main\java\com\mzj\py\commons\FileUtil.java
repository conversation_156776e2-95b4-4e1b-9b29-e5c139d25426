package com.mzj.py.commons;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 
 * <AUTHOR>
 *
 */
public class FileUtil {
	private static final Logger LOG = LoggerFactory.getLogger(FileUtil.class);
	/** 
     * 将存放在sourceFilePath目录下的源文件，打包成fileName名称的zip文件，并存放到zipFilePath路径下（只支持）
     * @param sourceFilePath :待压缩的文件路径 
     * @param zipFilePath :压缩后存放路径 
     * @param fileName :压缩后文件的名称 
     * @return 
     */  
    public static boolean fileToZip(String sourceFilePath, String zipFilePath, String fileName){
        boolean flag = false;  
        File sourceFile = new File(sourceFilePath);
        FileInputStream fis = null;
        BufferedInputStream bis = null;
        FileOutputStream fos = null;
        ZipOutputStream zos = null;
          
        if(sourceFile.exists() == false){  
        	LOG.warn("待压缩的文件目录："+sourceFilePath+"不存在.");  
        }else{  
            try {  
                File zipFile = new File(zipFilePath + "/" + fileName +".zip");
                if(zipFile.exists()){  
                	LOG.warn(zipFilePath + "目录下存在名字为:" + fileName +".zip" +"打包文件.");  
                }else{  
                    File[] sourceFiles = sourceFile.listFiles();
                    if(null == sourceFiles || sourceFiles.length<1){  
                    	LOG.warn("待压缩的文件目录：" + sourceFilePath + "里面不存在文件，无需压缩.");  
                    }else{  
                        fos = new FileOutputStream(zipFile);
                        zos = new ZipOutputStream(new BufferedOutputStream(fos));
                        byte[] bufs = new byte[1024*10];  
                        for(int i=0;i<sourceFiles.length;i++){  
                            //创建ZIP实体，并添加进压缩包  
                            ZipEntry zipEntry = new ZipEntry(sourceFiles[i].getName());
                            zos.putNextEntry(zipEntry);  
                            //读取待压缩的文件并写进压缩包里  
                            fis = new FileInputStream(sourceFiles[i]);
                            bis = new BufferedInputStream(fis, 1024*10);
                            int read = 0;  
                            while((read=bis.read(bufs, 0, 1024*10)) != -1){  
                                zos.write(bufs,0,read);  
                            }  
                        }  
                        flag = true;  
                    }  
                }  
            } catch (FileNotFoundException e) {
                LOG.error(e.getMessage(),e);
                throw new RuntimeException(e);
            } catch (IOException e) {
            	LOG.error(e.getMessage(),e);
                throw new RuntimeException(e);
            } finally{  
                //关闭流  
                try {  
                    if(null != bis) bis.close();  
                    if(null != zos) zos.close();
                    if(null !=fis)  fis.close();
                    if(null != fos) fos.close();
                } catch (IOException e) {
                	LOG.error(e.getMessage(),e);  
                    //throw new RuntimeException(e);
                }  
            }  
        }  
        return flag;  
    }


    public static InputStream httpFile(String strurl){
        try{
            URL url = new URL(strurl);
            //打开链接
            HttpURLConnection conn = (HttpURLConnection)url.openConnection();
            //设置请求方式为"GET"
            conn.setRequestMethod("GET");
            //超时响应时间为5秒
            conn.setConnectTimeout(5 * 1000);
            //通过输入流获取图片数据
            InputStream inStream = conn.getInputStream();
            //得到图片的二进制 数据，以二进制封装得到数据，具有通用性
            return inStream;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }

    }


    /**
     * @param byteArrayOutputStream 将文件内容写入ByteArrayOutputStream
     * @param response HttpServletResponse	写入response
     * @param returnName 返回的文件名
     */
    public static void download(ByteArrayOutputStream byteArrayOutputStream, HttpServletResponse response, String returnName) throws IOException {
        response.setContentType("application/octet-stream;charset=utf-8");
        returnName = response.encodeURL(new String(returnName.getBytes(),"utf-8"));			//保存的文件名,必须和页面编码一致,否则乱码
        response.addHeader("Content-Disposition",   "attachment;filename=" + returnName);
        response.setContentLength(byteArrayOutputStream.size());

        ServletOutputStream outputstream = response.getOutputStream();	//取得输出流
        byteArrayOutputStream.writeTo(outputstream);					//写到输出流
        byteArrayOutputStream.close();									//关闭
        outputstream.flush();											//刷数据
    }
    
    /**
     * 创建文件
     * @param file
     * @param data
     * @return
     */
    public static boolean createFile(String filePath, String fileName, String data) {
        boolean flag = false;
    	try {
    		File file = new File(filePath);
    		if(!file.isDirectory()) {
    			file.mkdirs();
    		}
    		
            FileWriter fileWriter = new FileWriter(filePath + fileName);
            fileWriter.write(data);
            fileWriter.flush();
            fileWriter.close();
            flag = true;
        } catch (Exception e) {
        	LOG.error("CreateFile Exception = {}", e.getMessage());
        }
    	return flag;
    }

    public static String[] readFileTexts(String filePath) {
    	String[] textArray = null;
    	try {
    		List<String> textList = new ArrayList<>();
    		BufferedReader in = new BufferedReader(new FileReader(filePath));
        	String str;
        	while ((str = in.readLine()) != null) {
        		textList.add(str);
        	}
        	
        	if(textList.size() > 0) {
        		textArray =  textList.toArray((new String[0]));
        	}
        	
        	in.close();
		} catch (Exception e) {
			LOG.error("readFileTexts Exception = {}", e.getMessage());
		}
    	
    	return textArray;
    }

    public static String[] readFromFileTexts(File file) {
        String[] textArray = null;
        try {
            List<String> textList = new ArrayList<>();
            DataInputStream dataInputStream = new DataInputStream(new FileInputStream(file));
            BufferedReader in = new BufferedReader(new InputStreamReader(dataInputStream,"GBK"));
            String str;
            while ((str = in.readLine()) != null) {
                textList.add(str);
            }

            if(textList.size() > 0) {
                textArray =  textList.toArray((new String[0]));
            }

            in.close();
            dataInputStream.close();
        } catch (Exception e) {
            LOG.error("readFileTexts Exception = {}", e.getMessage());
        }

        return textArray;
    }
    
    public static String[] subArray(Object[] array, int beginIndex, int endIndex) {
		int length = endIndex - beginIndex + 1;
		String[] subArray = new String[length];
		System.arraycopy(array, beginIndex, subArray, 0, length);
		return subArray;
	}
    /*public static void main(String[] args) {
    	String [] fileTexts = readFileTexts("E:\\auditfiles\\weixin\\1503997831-20190116-20190316143127.txt");
    	for (Object text : fileTexts) {
    		System.out.println(text);
		}
    	System.out.println("=========================");
    	fileTexts = FileUtil.subArray(fileTexts, 1, fileTexts.length - 3);
    	System.out.println();
    	for (Object text : fileTexts) {
    		System.out.println(text);
		}
    	
	}*/
}  

