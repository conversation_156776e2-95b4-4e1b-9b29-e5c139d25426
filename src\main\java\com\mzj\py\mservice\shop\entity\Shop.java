package com.mzj.py.mservice.shop.entity;


import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2025/3/2
 * @description: 店铺表
 * CREATE TABLE `dub_shop` (
 *   `id` bigint NOT NULL AUTO_INCREMENT,
 *   `parent_id` bigint DEFAULT NULL,
 *   `type` int DEFAULT NULL COMMENT '店铺类型 1-总店 2-分店',
 *   `shop_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
 *   `create_user_id` bigint DEFAULT NULL,
 *   `careate_time` datetime DEFAULT NULL,
 *   `status` int DEFAULT NULL COMMENT '绑定审核状态 0-审核不通过 1-审核通过\n2-待审核',
 *   PRIMARY KEY (`id`)
 * ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='店铺表';
 */
@Data
@Entity
@Table(name = "dub_shop")
public class Shop {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 店铺ID
     */
    @Basic
    @Column(name = "parent_id")
    private Long parentId;

    /**
     * 店铺类型 1-总店 2-分店
     */
    @Basic
    @Column(name = "type")
    private Integer type;

    /**
     * 店铺名称
     */
    @Basic
    @Column(name = "shop_name")
    private String shopName;

    /**
     * 地址
     */
    @Basic
    @Column(name = "address")
    private String address;

    /**
     * 创建人
     */
    @Basic
    @Column(name = "create_user_id")
    private Long createUserId;

    /**
     * 创建时间
     */
    @Basic
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 绑定审核状态 0-审核不通过 1-审核通过\n2-待审核
     */
    @Basic
    @Column(name = "status")
    private Integer status;
}
