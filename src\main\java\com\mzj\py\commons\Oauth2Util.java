package com.mzj.py.commons;

import java.net.URLEncoder;

import org.springframework.stereotype.Component;

/**
 * 微信Oauth2授权工具类
 * 
 * <AUTHOR>
 * @date: 2019-12-23
 */
@Component
public class Oauth2Util {


	/**
	 * 获取code的scope
	 */
	private static String SCOPE = "snsapi_userinfo";
	/**
	 * 微信Oauth2授权换取code
	 */
	private static String GET_CODE_REQUEST = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope=%s&state=STATE#wechat_redirect";
	/**
	 * 通过code换取网页授权access_token
	 */
	private static String GET_ACCESS_TOKEN = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&code=%s&secret=%s&grant_type=authorization_code";
	/**
	 * 获取用户信息
	 */
	private static String GET_USER_INFO = "https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s&lang=zh_CN";

	/**
	 * 获取页面code不走三方平台
	 * 
	 * @param backUrl 微信回调地址
	 * @param appid   服务号appid 
	 * @param state
	 * @return
	 */
	public static String getCodeRequest(String backUrl, String appid) {
		return String.format(GET_CODE_REQUEST, appid, urlToU8(backUrl), SCOPE);
	}

	/**
	 * 获取用户信息url
	 * 
	 * @param accessToken
	 * @param openid
	 * @return
	 */
	public static String getUserInfoUrl(String accessToken, String openid) {
		return String.format(GET_USER_INFO, accessToken, openid);
	}

	/**
	 * 重定向URL 转码
	 * 
	 * @param str
	 * @return
	 */
	private static String urlToU8(String str) {
		String result = str;
		try {
			result = URLEncoder.encode(str, "UTF-8");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	/**
	 * 获取页面token
	 * 
	 * @param code
	 * @param appid 服务号appid
	 * @return
	 */
	public static String getAccessTokenUrl(String code, String appid, String secret) {
		return String.format(GET_ACCESS_TOKEN, appid, code, secret);
	}

}
