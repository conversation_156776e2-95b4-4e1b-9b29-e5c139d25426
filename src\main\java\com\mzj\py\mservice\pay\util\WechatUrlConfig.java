package com.mzj.py.mservice.pay.util;

public class WechatUrlConfig {
    /**
     * 适用对象：小程序下单
     * 请求方式：POST
     */
    public static final String JSAPIURL = "https://api.mch.weixin.qq.com/v3/pay/transactions/jsapi";

    /**
     * 获取证书
     */
    public static final String CERTIFICATESURL = "https://api.mch.weixin.qq.com/v3/certificates";


    /**
     * 退款地址
     */
    public static final String REFUNDSURL = "https://api.mch.weixin.qq.com/v3/refund/domestic/refunds";

}
