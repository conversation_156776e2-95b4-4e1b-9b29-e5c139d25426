package com.mzj.py.mservice.my.service;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.StatusCode;
import com.mzj.py.commons.StringUtils;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.mservice.my.entity.BaseAbout;
import com.mzj.py.mservice.my.entity.BaseService;
import com.mzj.py.mservice.my.entity.BaseServiceMessage;
import com.mzj.py.mservice.my.repository.BaseServiceMessageRepository;
import com.mzj.py.mservice.my.vo.BaseServiceMessageVo;
import com.mzj.py.mservice.redis.RedisService;
import com.mzj.py.mservice.wxuser.entity.WxUser;
import com.mzj.py.mservice.wxuser.repository.WxUserRepository;
import com.mzj.py.mservice.wxuser.vo.UserVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MyService单元测试类
 * 测试MyService中的所有公共方法，包括正面和负面场景
 * 
 * @author: Test Author
 * @date: 2024/12/12
 * @version: 1.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("MyService单元测试")
class MyServiceTest {

    @Mock
    private RedisService redisService;

    @Mock
    private WxUserRepository wxUserRepository;

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private RedisTemplate redisTemplate;

    @Mock
    private BaseServiceMessageRepository baseServiceMessageRepository;

    @InjectMocks
    private MyService myService;

    private TokenRedisVo mockTokenRedisVo;
    private WxUser mockWxUser;
    private String validAccessToken;
    private String invalidAccessToken;

    @BeforeEach
    void setUp() {
        validAccessToken = "valid_token_123";
        invalidAccessToken = "invalid_token_456";

        // 设置模拟的TokenRedisVo
        mockTokenRedisVo = new TokenRedisVo();
        mockTokenRedisVo.setId(1L);
        mockTokenRedisVo.setOpenid("test_openid");
        mockTokenRedisVo.setAvatar("test_avatar");
        mockTokenRedisVo.setGender(1);
        mockTokenRedisVo.setPhone("13800138000");
        mockTokenRedisVo.setArea("北京");
        mockTokenRedisVo.setNicknames("测试用户");

        // 设置模拟的WxUser
        mockWxUser = new WxUser();
        mockWxUser.setId(1L);
        mockWxUser.setOpenid("test_openid");
        mockWxUser.setNickname("测试用户");
        mockWxUser.setAvatar("test_avatar");
        mockWxUser.setGender(1);
        mockWxUser.setPhone("13800138000");
        mockWxUser.setArea("北京");
    }

    @Test
    @DisplayName("测试myInfo方法 - 成功获取用户信息")
    void testMyInfo_Success() {
        // Given
        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(wxUserRepository.findByOpenid(mockTokenRedisVo.getOpenid())).thenReturn(mockWxUser);

        // When
        ResultBean<WxUser> result = myService.myInfo(validAccessToken);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNotNull(result.getResultData());
        assertEquals(mockWxUser.getId(), result.getResultData().getId());
        assertEquals(mockWxUser.getOpenid(), result.getResultData().getOpenid());

        // 验证方法调用
        verify(redisService, times(1)).findTokenVo(validAccessToken);
        verify(wxUserRepository, times(1)).findByOpenid(mockTokenRedisVo.getOpenid());
    }

    @Test
    @DisplayName("测试myInfo方法 - Token失效")
    void testMyInfo_TokenInvalid() {
        // Given
        when(redisService.findTokenVo(invalidAccessToken)).thenReturn(null);

        // When
        ResultBean<WxUser> result = myService.myInfo(invalidAccessToken);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.ERROR_CODE_10001.getErrorCode(), result.getCode());
        assertEquals("token失效，请重新登录", result.getMsg());
        assertNull(result.getResultData());

        // 验证方法调用
        verify(redisService, times(1)).findTokenVo(invalidAccessToken);
        verify(wxUserRepository, never()).findByOpenid(any());
    }

    @Test
    @DisplayName("测试myInfo方法 - 用户昵称为空")
    void testMyInfo_NicknameNull() {
        // Given
        mockWxUser.setNickname(null);
        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(wxUserRepository.findByOpenid(mockTokenRedisVo.getOpenid())).thenReturn(mockWxUser);

        // When
        ResultBean<WxUser> result = myService.myInfo(validAccessToken);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertNotNull(result.getResultData());
        assertNull(result.getResultData().getNickname());
    }

    @Test
    @DisplayName("测试aboutUs方法 - 成功获取关于我们信息")
    void testAboutUs_Success() {
        // Given
        BaseAbout mockBaseAbout = new BaseAbout();
        mockBaseAbout.setId(1L);
        mockBaseAbout.setContent("关于我们的内容");

        List<BaseAbout> baseAbouts = new ArrayList<>();
        baseAbouts.add(mockBaseAbout);

        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class))).thenReturn(baseAbouts);

        // When
        ResultBean<BaseAbout> result = myService.aboutUs();

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNotNull(result.getResultData());
        assertEquals(mockBaseAbout.getId(), result.getResultData().getId());
        assertEquals(mockBaseAbout.getContent(), result.getResultData().getContent());

        // 验证SQL查询
        verify(jdbcTemplate, times(1)).query(eq("select * from dub_base_about limit 1 "),
                any(BeanPropertyRowMapper.class));
    }

    @Test
    @DisplayName("测试aboutUs方法 - 数据库中无数据")
    void testAboutUs_NoData() {
        // Given
        List<BaseAbout> emptyList = new ArrayList<>();
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class))).thenReturn(emptyList);

        // When
        ResultBean<BaseAbout> result = myService.aboutUs();

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNull(result.getResultData());
    }

    @Test
    @DisplayName("测试customerService方法 - 成功获取客服信息")
    void testCustomerService_Success() {
        // Given
        BaseService mockBaseService = new BaseService();
        mockBaseService.setId(1L);
        mockBaseService.setWechat("customer_wechat");
        mockBaseService.setPhone("************");

        List<BaseService> baseServices = new ArrayList<>();
        baseServices.add(mockBaseService);

        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class))).thenReturn(baseServices);

        // When
        ResultBean result = myService.customerService();

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNotNull(result.getResultData());

        // 验证SQL查询
        verify(jdbcTemplate, times(1)).query(eq("select * from dub_base_service limit 1 "),
                any(BeanPropertyRowMapper.class));
    }

    @Test
    @DisplayName("测试customerService方法 - 数据库中无数据")
    void testCustomerService_NoData() {
        // Given
        List<BaseService> emptyList = new ArrayList<>();
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class))).thenReturn(emptyList);

        // When
        ResultBean result = myService.customerService();

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNull(result.getResultData());
    }

    @Test
    @DisplayName("测试customerMessage方法 - 成功提交客服留言")
    void testCustomerMessage_Success() {
        // Given
        BaseServiceMessageVo messageVo = new BaseServiceMessageVo();
        messageVo.setContent("这是一条测试留言");

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(baseServiceMessageRepository.save(any(BaseServiceMessage.class))).thenReturn(new BaseServiceMessage());

        // When
        ResultBean result = myService.customerMessage(validAccessToken, messageVo);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertTrue((Boolean) result.getResultData());

        // 验证保存的消息内容
        ArgumentCaptor<BaseServiceMessage> messageCaptor = ArgumentCaptor.forClass(BaseServiceMessage.class);
        verify(baseServiceMessageRepository, times(1)).save(messageCaptor.capture());

        BaseServiceMessage savedMessage = messageCaptor.getValue();
        assertEquals(mockTokenRedisVo.getId(), savedMessage.getUserId());
        assertEquals(messageVo.getContent(), savedMessage.getContent());
        assertNotNull(savedMessage.getMessageTime());
    }

    @Test
    @DisplayName("测试customerMessage方法 - Token失效")
    void testCustomerMessage_TokenInvalid() {
        // Given
        BaseServiceMessageVo messageVo = new BaseServiceMessageVo();
        messageVo.setContent("这是一条测试留言");

        when(redisService.findTokenVo(invalidAccessToken)).thenReturn(null);

        // When
        ResultBean result = myService.customerMessage(invalidAccessToken, messageVo);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.ERROR_CODE_10001.getErrorCode(), result.getCode());
        assertEquals("token失效，请重新登录", result.getMsg());

        // 验证不会保存消息
        verify(baseServiceMessageRepository, never()).save(any(BaseServiceMessage.class));
    }

    @Test
    @DisplayName("测试customerMessage方法 - 留言内容为空")
    void testCustomerMessage_EmptyContent() {
        // Given
        BaseServiceMessageVo messageVo = new BaseServiceMessageVo();
        messageVo.setContent("");

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);

        // When
        ResultBean result = myService.customerMessage(validAccessToken, messageVo);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.ERROR_CODE_10001.getErrorCode(), result.getCode());
        assertEquals("失败", result.getMsg());
        assertEquals("留言内容不能为空", result.getResultData());

        // 验证不会保存消息
        verify(baseServiceMessageRepository, never()).save(any(BaseServiceMessage.class));
    }

    @Test
    @DisplayName("测试customerMessage方法 - 留言内容为null")
    void testCustomerMessage_NullContent() {
        // Given
        BaseServiceMessageVo messageVo = new BaseServiceMessageVo();
        messageVo.setContent(null);

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);

        // When
        ResultBean result = myService.customerMessage(validAccessToken, messageVo);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.ERROR_CODE_10001.getErrorCode(), result.getCode());
        assertEquals("失败", result.getMsg());
        assertEquals("留言内容不能为空", result.getResultData());

        // 验证不会保存消息
        verify(baseServiceMessageRepository, never()).save(any(BaseServiceMessage.class));
    }

    @Test
    @DisplayName("测试customerMessage方法 - 留言内容为空白字符")
    void testCustomerMessage_BlankContent() {
        // Given
        BaseServiceMessageVo messageVo = new BaseServiceMessageVo();
        messageVo.setContent("   ");

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);

        // When
        ResultBean result = myService.customerMessage(validAccessToken, messageVo);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.ERROR_CODE_10001.getErrorCode(), result.getCode());
        assertEquals("失败", result.getMsg());
        assertEquals("留言内容不能为空", result.getResultData());

        // 验证不会保存消息
        verify(baseServiceMessageRepository, never()).save(any(BaseServiceMessage.class));
    }

    @Test
    @DisplayName("测试updateAvatarOrNickName方法 - 成功更新头像")
    void testUpdateAvatarOrNickName_UpdateAvatar_Success() {
        // Given
        UserVo userVo = new UserVo();
        userVo.setHeadPicture("new_avatar_url");

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(wxUserRepository.updateAvatar(mockTokenRedisVo.getOpenid(), userVo.getHeadPicture())).thenReturn(1);

        // When
        ResultBean result = myService.updateAvatarOrNickName(validAccessToken, userVo);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals("修改头像成功", result.getResultData());

        // 验证方法调用
        verify(wxUserRepository, times(1)).updateAvatar(mockTokenRedisVo.getOpenid(), userVo.getHeadPicture());
        verify(wxUserRepository, never()).updateNickname(any(), any());
    }

    @Test
    @DisplayName("测试updateAvatarOrNickName方法 - 成功更新昵称")
    void testUpdateAvatarOrNickName_UpdateNickname_Success() {
        // Given
        UserVo userVo = new UserVo();
        userVo.setNickName("新昵称");

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(wxUserRepository.updateNickname(mockTokenRedisVo.getOpenid(), userVo.getNickName())).thenReturn(1);

        // When
        ResultBean result = myService.updateAvatarOrNickName(validAccessToken, userVo);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals("修改昵称成功", result.getResultData());

        // 验证方法调用
        verify(wxUserRepository, times(1)).updateNickname(mockTokenRedisVo.getOpenid(), userVo.getNickName());
        verify(wxUserRepository, never()).updateAvatar(any(), any());
    }

    @Test
    @DisplayName("测试updateAvatarOrNickName方法 - Token失效")
    void testUpdateAvatarOrNickName_TokenInvalid() {
        // Given
        UserVo userVo = new UserVo();
        userVo.setHeadPicture("new_avatar_url");

        when(redisService.findTokenVo(invalidAccessToken)).thenReturn(null);

        // When
        ResultBean result = myService.updateAvatarOrNickName(invalidAccessToken, userVo);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.ERROR_CODE_10001.getErrorCode(), result.getCode());
        assertEquals("token失效，请重新登录", result.getMsg());

        // 验证不会调用更新方法
        verify(wxUserRepository, never()).updateAvatar(any(), any());
        verify(wxUserRepository, never()).updateNickname(any(), any());
    }

    @Test
    @DisplayName("测试updateAvatarOrNickName方法 - 头像和昵称都为空")
    void testUpdateAvatarOrNickName_BothEmpty() {
        // Given
        UserVo userVo = new UserVo();
        // 头像和昵称都不设置，保持为null

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);

        // When
        ResultBean result = myService.updateAvatarOrNickName(validAccessToken, userVo);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertTrue((Boolean) result.getResultData());

        // 验证不会调用更新方法
        verify(wxUserRepository, never()).updateAvatar(any(), any());
        verify(wxUserRepository, never()).updateNickname(any(), any());
    }

    @Test
    @DisplayName("测试updateAvatarOrNickName方法 - 头像为空字符串")
    void testUpdateAvatarOrNickName_EmptyAvatar() {
        // Given
        UserVo userVo = new UserVo();
        userVo.setHeadPicture("");
        userVo.setNickName("新昵称");

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(wxUserRepository.updateNickname(mockTokenRedisVo.getOpenid(), userVo.getNickName())).thenReturn(1);

        // When
        ResultBean result = myService.updateAvatarOrNickName(validAccessToken, userVo);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals("修改昵称成功", result.getResultData());

        // 验证只调用昵称更新方法
        verify(wxUserRepository, never()).updateAvatar(any(), any());
        verify(wxUserRepository, times(1)).updateNickname(mockTokenRedisVo.getOpenid(), userVo.getNickName());
    }

    @Test
    @DisplayName("测试updateAvatarOrNickName方法 - 昵称为空字符串")
    void testUpdateAvatarOrNickName_EmptyNickname() {
        // Given
        UserVo userVo = new UserVo();
        userVo.setHeadPicture("new_avatar_url");
        userVo.setNickName("");

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(wxUserRepository.updateAvatar(mockTokenRedisVo.getOpenid(), userVo.getHeadPicture())).thenReturn(1);

        // When
        ResultBean result = myService.updateAvatarOrNickName(validAccessToken, userVo);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals("修改头像成功", result.getResultData());

        // 验证只调用头像更新方法
        verify(wxUserRepository, times(1)).updateAvatar(mockTokenRedisVo.getOpenid(), userVo.getHeadPicture());
        verify(wxUserRepository, never()).updateNickname(any(), any());
    }

    @Test
    @DisplayName("测试updateAvatarOrNickName方法 - 同时设置头像和昵称（优先更新头像）")
    void testUpdateAvatarOrNickName_BothSet() {
        // Given
        UserVo userVo = new UserVo();
        userVo.setHeadPicture("new_avatar_url");
        userVo.setNickName("新昵称");

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(wxUserRepository.updateAvatar(mockTokenRedisVo.getOpenid(), userVo.getHeadPicture())).thenReturn(1);

        // When
        ResultBean result = myService.updateAvatarOrNickName(validAccessToken, userVo);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals("修改头像成功", result.getResultData());

        // 验证只调用头像更新方法（因为头像优先级更高）
        verify(wxUserRepository, times(1)).updateAvatar(mockTokenRedisVo.getOpenid(), userVo.getHeadPicture());
        verify(wxUserRepository, never()).updateNickname(any(), any());
    }
}
