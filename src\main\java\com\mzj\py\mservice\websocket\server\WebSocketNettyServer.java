package com.mzj.py.mservice.websocket.server;

import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.logging.LoggingHandler;

import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * netty服务端
 * 实现DisposableBean 在容器销毁前会调用destroy 方法进行线程组的关闭
 * 
 * <AUTHOR>
 */

@Component
public class WebSocketNettyServer implements DisposableBean {

    /**
     * 通道初始化对象
     */
    @Resource
    private WebSocketChannelInit webSocketChannelInit;

    /**
     * boos线程组
     */
    private EventLoopGroup boos;

    /**
     * work线程组
     */
    private EventLoopGroup work;

    /**
     * boos线程组
     */
    private EventLoopGroup boos1;

    /**
     * work线程组
     */
    private EventLoopGroup work1;

    /**
     * 自定义启动方法
     * 
     * @param port
     */
    public void start(int port) {
        // 设置boos线程组
        boos = new NioEventLoopGroup();
        // 初始化work线程组，避免空指针
        work = new NioEventLoopGroup();
        // 创建启动助手
        ServerBootstrap serverBootstrap = new ServerBootstrap();
        serverBootstrap.group(boos, work)
                .channel(NioServerSocketChannel.class)
                .handler(new LoggingHandler())
                .childHandler(webSocketChannelInit);
        // 绑定ip和端口启动服务端
        ChannelFuture sync = null;
        try {
            // 绑定netty的启动端口
            sync = serverBootstrap.bind(port).sync();
        } catch (InterruptedException e) {
            e.printStackTrace();
            close();
        }
        try {
            // 等待直到服务器连接关闭
            sync.channel().closeFuture().sync();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 容器销毁前关闭线程组
     * 
     * @throws Exception
     */
    @Override
    public void destroy() throws Exception {
        close();
    }

    /**
     * 关闭方法
     */
    public void close() {
        if (boos != null) {
            boos.shutdownGracefully();
        }
        if (work != null) {
            work.shutdownGracefully();
        }
    }

    public WebSocketChannelInit getWebSocketChannelInit() {
        return webSocketChannelInit;
    }

    public void setWebSocketChannelInit(WebSocketChannelInit webSocketChannelInit) {
        this.webSocketChannelInit = webSocketChannelInit;
    }

    public EventLoopGroup getBoos() {
        return boos;
    }

    public void setBoos(EventLoopGroup boos) {
        this.boos = boos;
    }

    public EventLoopGroup getWork() {
        return work;
    }

    public void setWork(EventLoopGroup work) {
        this.work = work;
    }
}
