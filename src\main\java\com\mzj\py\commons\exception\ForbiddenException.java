package com.mzj.py.commons.exception;

import org.springframework.http.HttpStatus;

/**
 * 无授权异常
 * <AUTHOR>
 *
 */
public class ForbiddenException extends ServiceException{

	private static final long serialVersionUID = 1L;
		
	public ForbiddenException(String message) {
		super(HttpStatus.FORBIDDEN,ErrorCode.FORBIDDEN.code(), message);
	}
	
	public ForbiddenException(int code,String message) {
		super(HttpStatus.FORBIDDEN,code, message);
	}

}
