package com.mzj.py.mservice.device.vo;

import lombok.Data;

import java.util.List;

/**
 * 设备表
 * @author: duanjinze
 * @date: 2022/11/10 13:35
 * @version: 1.0
 */
@Data
public class DeviceQueryVo {

    /**
     * 分页大小
     */
    private int pageSize = 10;

    /**
     * 页码
     */
    private int pageNumber = 1;

    /**
     * 设备SN 或者 名称
     */
    private String keyword;

    /**
     * 在线状态 0离线 1在线
     */
    private Integer status;

    /**
     * 绑定状态 0未绑定 1已绑定
     */
    private Integer bindStatus;

    /**
     * 门店ids
     */
    private List<Long> shopIds;
    private Long createId;
}
