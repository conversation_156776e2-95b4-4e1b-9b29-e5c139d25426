package com.mzj.py.mservice.shop.vo;

import com.mzj.py.commons.PageBean;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date: 2025/3/20
 * @description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StorePageParams extends PageBean {

    // 门店名称
    private String keyword;
    // 门店类型
    private Integer storeType;

    private Long storeId;
}
