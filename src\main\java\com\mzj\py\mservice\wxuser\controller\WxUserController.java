package com.mzj.py.mservice.wxuser.controller;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.mservice.common.ApiBaseController;
import com.mzj.py.mservice.wxuser.controller.request.*;
import com.mzj.py.mservice.wxuser.service.WxUserService;
import com.mzj.py.mservice.wxuser.vo.UserVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 微信用户
 */
@Controller
@RequestMapping("/wxUser")
public class WxUserController extends ApiBaseController {
    @Autowired
    private WxUserService wxUserService;

    /**
     * 登录(获取openid)
     *
     * @param appId
     * @param code
     * @return
     */
//    @GetMapping(value = "/openid")
//    @ResponseBody
//    public ResultBean<Map<String, Object>> userOpenId(@RequestParam(name = "appId", required = false) String appId,
//                                                      @RequestParam(name = "code", required = false) String code
//    ) {
//        return wxUserService.userOpenId(appId, code);
//    }

    /**
     * 登录注册
     * @param vo
     * @return
     */
    @PostMapping("/register")
    @ResponseBody
    public ResultBean<Object> userLoginRegister(@RequestBody UserRegistReq vo) {
        return wxUserService.userLoginRegister(vo);
    }

    /**
     * 授权获取手机号
     *
     * @param sessionKey
     * @param encryptedData
     * @param iv
     * @return
     */
    @GetMapping(value = "/userPhone")
    @ResponseBody
    public ResultBean<Map<String, Object>> userPhone(@RequestHeader String accessToken, @RequestParam(name = "sessionKey", required = false) String sessionKey,
                                                     @RequestParam(name = "encryptedData", required = false) String encryptedData,
                                                     @RequestParam(name = "iv", required = false) String iv) {
        return wxUserService.userPhone(accessToken, sessionKey, encryptedData, iv);
    }

    /**
     * 修改手机号
     * @return
     */
    @GetMapping(value = "/updatePhone/{phone}")
    @ResponseBody
    public ResultBean<Map<String, Object>> updatePhone(@RequestHeader String accessToken, @PathVariable String phone) {
        return wxUserService.updatePhone(super.getUser(accessToken).getId(), phone);
    }

    /**
     * 获取我的长文本主播
     * @param accessToken
     * @return
     */
    @GetMapping("getMyLongAnchor")
    @ResponseBody
    public  ResultBean<Map<String, Object>>getMyLongAnchor(@RequestHeader String accessToken){
        return wxUserService.getMyLongAnchor(accessToken);
    }

    /**
     * 获取我的主播
     * @param accessToken
     * @return
     */
    @GetMapping("getMyAnchor")
    @ResponseBody
    public  ResultBean<Map<String, Object>>getMyVipAnchor(@RequestHeader String accessToken){
        return wxUserService.getMyVipAnchor(accessToken);
    }
    /**
     * 关注主播
     * @param accessToken
     * @param followAnchorReq
     * @return
     */
    @PostMapping("followAnchor")
    @ResponseBody
    public ResultBean<Object> followAnchor(@RequestHeader String accessToken, @RequestBody FollowAnchorReq followAnchorReq) {
        return wxUserService.followAnchor(accessToken, followAnchorReq);
    }

    /**
     * 关注长文本主播
     * @param accessToken
     * @param followAnchorReq
     * @return
     */
    @PostMapping("followLong")
    @ResponseBody
    public ResultBean<Object> followLongAnchor(@RequestHeader String accessToken, @RequestBody FollowAnchorReq followAnchorReq) {
        return wxUserService.followLongAnchor(accessToken, followAnchorReq);
    }
    /**
     * 关注背景音乐
     * @param accessToken
     * @param followBgmReq
     * @return
     */
    @PostMapping("followBgm")
    @ResponseBody
    public ResultBean<Object> followBgm(@RequestHeader String accessToken, @RequestBody FollowBgmReq followBgmReq) {
        return wxUserService.followBgm(accessToken, followBgmReq);
    }
    /**
     * 获取关注的背景音乐
     * @param accessToken
     * @return
     */
    @GetMapping("getFollowBgm")
    @ResponseBody
    public  ResultBean<Map<String, Object>>getFollowBgm(@RequestHeader String accessToken){
        return wxUserService.getFollowBgm(accessToken);
    }
    /**
     * 添加文本模板
     * @param accessToken
     * @param textTemplateAddReq
     * @return
     */
    @PostMapping("addTextTemplate")
    @ResponseBody
    public  ResultBean<Object>addTextTemplate(@RequestHeader String accessToken,@RequestBody TextTemplateAddReq textTemplateAddReq){
        return wxUserService.addTextTemplate(accessToken,textTemplateAddReq);
    }

    /**
     * 获取文本模板
     * @param accessToken
     * @return
     */
    @GetMapping("getTextTemplate")
    @ResponseBody
    public ResultBean<Map<String, Object>> getTextTemplate(@RequestHeader String accessToken) {
        return wxUserService.getTextTemplate(accessToken);
    }

    /**
     * 删除文本模板
     * @param accessToken
     * @param textTemplateDelReq
     * @return
     */
    @DeleteMapping("delTextTemplate")
    @ResponseBody
    public ResultBean<Object> delTextTemplate(@RequestHeader String accessToken,@RequestBody TextTemplateDelReq textTemplateDelReq) {
        return wxUserService.delTextTemplate(accessToken,textTemplateDelReq);
    }


}
