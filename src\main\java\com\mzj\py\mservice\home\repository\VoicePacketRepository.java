package com.mzj.py.mservice.home.repository;

import com.mzj.py.mservice.home.entity.VoicePacket;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface VoicePacketRepository extends JpaRepository<VoicePacket, Long>, JpaSpecificationExecutor<VoicePacket> {
    VoicePacket findByVoiceWorkId(Long id);


    @Transactional
    @Modifying
    @Query(value = "delete from dub_voice_packet where voice_work_id = ?1", nativeQuery = true)
    void deleteByVoiceWorkId(Long id);
}
