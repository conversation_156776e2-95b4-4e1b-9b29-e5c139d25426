package com.mzj.py.mservice.pay.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mzj.py.mservice.pay.conf.WxpayConfig;
import com.mzj.py.mservice.pay.util.HttpUtils;
import com.mzj.py.mservice.pay.util.WechatUrlConfig;
import com.mzj.py.mservice.pay.util.WeixinchatPayUtils;
import com.mzj.py.mservice.pay.vo.PayVo;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;


@Log4j2
@Service
public class WechatService {


    /**
     * 生成预支付订单
     * @param order
     * @return
     * @throws JsonProcessingException
     */
    public HashMap<String, Object> wxPay(PayVo order)  {

        Map<String, Object> map = new HashMap<>();
        // 支付的产品（小程序或者公众号，主要需要和微信支付绑定哦）
        map.put("appid", WxpayConfig.app_id);
        // 支付的商户号
        map.put("mchid", WxpayConfig.mch_id);
        //临时写死配置
        //应该是酒店名+房间名+开始日期和结束日期
        map.put("description", "测试使用");
        map.put("out_trade_no", order.getOrderNo());//我们的订单号
        map.put("notify_url", WxpayConfig.notify_order_url);


        //金额
        Map<String, Object> amount = new HashMap();
        amount.put("total", order.getActualMoney());
        amount.put("currency", "CNY");
        map.put("amount", amount);

        // 设置小程序所需的opendi
        Map<String, Object> payermap = new HashMap();
        payermap.put("openid", order.getOpenid());
        map.put("payer", payermap);

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String body = objectMapper.writeValueAsString(map);

            //http调用微信生成预支付订单
            Map<String, Object>  stringObjectMap = HttpUtils.doPostWexin(WechatUrlConfig.JSAPIURL, body);
            HashMap<String, Object> dataMap = WeixinchatPayUtils.getTokenJSAPI(WxpayConfig.app_id, String.valueOf(stringObjectMap.get("prepay_id")));
            return dataMap;
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        return null;
    }


    public void cancelOrder(String orderId) {
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> refundParams = new HashMap<>();
            refundParams.put("appid", WxpayConfig.app_id); // 公众号ID
            refundParams.put("mchid", WxpayConfig.mch_id); // 商户号
            refundParams.put("out_trade_no", orderId); // 微信订单号

            ObjectMapper objectMapper = new ObjectMapper();
            String refundBody = "";

            refundBody = objectMapper.writeValueAsString(refundParams);
            // 调用关闭订单API
            Map<String, Object> stringObjectMap = HttpUtils.doPostWexin("https://api.mch.weixin.qq.com/v3/pay/transactions/out-trade-no/" + orderId + "/close", refundBody);
            System.out.println(stringObjectMap);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("code", "fail");
            result.put("message", "系统错误：" + e.getMessage());
        }
    }


}
