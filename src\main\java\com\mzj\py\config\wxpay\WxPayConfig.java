package com.mzj.py.config.wxpay;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-04-15-08:32
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "pay.wechat")
public class WxPayConfig {
    /**
     * 商户号
     */
    private String merchantId;
    /**
     * 商户 API 证书的证书序列号
     */
    private String merchantSerialNumber;
    /**
     * 商户APIV3密钥
     */
    private String apiV3key;

    /**
     * 支付通知地址
     */
    private String notifyUrl;
    /**
     *  商户API私钥路径
     */
    private String privateKeyPath;
    /**
     * 公众号id
     */
    private String appId;

}
