package com.mzj.py.mservice.order.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "dub_order_item")
public class OrderItem {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 订单ID
     */
    @Basic
    @Column(name = "order_id")
    private Long orderId;

    /**
     * '设备ID'
     */
    @Basic
    @Column(name = "device_id")
    private Long deviceId;

    /**
     * '套餐ID'
     */
    @Basic
    @Column(name = "package_id")
    private Long packageId;


    /**
     * 流量数量
     */
    @Basic
    @Column(name = "flow_amount")
    private Integer flowAmount;

    //0未充值 1充值成功 2充值失败
    @Basic
    @Column(name = "status", columnDefinition = "tinyint")
    private Integer status;

    /**
     * '创建时间'
     */
    @Basic
    @Column(name = "create_time")
    private Date createTime;

    /**
     * '完成时间'
     */
    @Basic
    @Column(name = "update_time")
    private Date updateTime;

}
