package com.mzj.py.mservice.home.entity;


import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * 作品表
 * @author: duanjinze
 * @date: 2022/11/10 11:42
 * @version: 1.0
 */
@Data
@Entity
@Table(name = "dub_voice_work")
public class VoiceWork {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 标题
     */
    @Basic
    @Column(name = "title")
    private String title;

    /**
     * 作品文字
     */
    @Basic
    @Column(name = "content",columnDefinition = "text")
    private String content;

    /**
     * 语音包id
     */
    @Basic
    @Column(name = "voice_id")
    private Long voiceId;

    /**
     * 主播id
     */
    @Basic
    @Column(name = "anchor_id")
    private Long anchorId;

    /**
     * 作品时长(秒)
     */
    @Basic
    @Column(name = "voice_time")
    private Integer voiceTime;

    /**
     * 语速
     */
    @Basic
    @Column(name = "speed")
    private Integer speed;

    /**
     * 语调
     */
    @Basic
    @Column(name = "volume")
    private Integer volume;


    /**
     * 音量
     */
    @Basic
    @Column(name = "pitch")
    private Integer pitch;


    /**
     * 背景音乐音量
     */
    @Basic
    @Column(name = "background_music_volume")
    private Integer backgroundMusicVolume;

    /**
     * 背景音乐id
     */
    @Basic
    @Column(name = "background_music_id")
    private Long backgroundMusicId;

    /**
     * 音频采样率
     */
    @Basic
    @Column(name = "sample_rate")
    private Integer sampleRate;

    /**
     * 所属用户id
     */
    @Basic
    @Column(name = "user_id")
    private Long userId;

    /**
     * 所属用户id
     */
    @Basic
    @Column(name = "shop_id")
    private Long shopId;

    /**
     * 0未删除 1已删除
     */
    @Basic
    @Column(name = "del_status",columnDefinition = "smallint")
    private Integer delStatus;



    /**
     * 创建时间
     */
    @Basic
    @Column(name = "create_time")
    private Date createTime;

}
