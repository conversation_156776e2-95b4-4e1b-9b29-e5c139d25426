package com.mzj.py.mservice.home.repository;

import com.mzj.py.mservice.home.dto.AnchorDTO;
import com.mzj.py.mservice.home.entity.DubAnchor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface DubAnchorRepository extends JpaRepository<DubAnchor, Long>, JpaSpecificationExecutor<DubAnchor> {
    @Query(value = "select distinct usage_scenario from dub_anchor order by order_index desc", nativeQuery = true)
    List<String> getDistinctByUsageScenario();

    @Query(value = "select new com.mzj.py.mservice.home.dto.AnchorDTO(v.id,v.name,v.usageScenario,v.typeName,v.url," +
            "v.voiceUrl,v.archorTag) " +
            "from DubAnchor v join FollowAnchor vf on v.id = vf.anchorId where vf.userId=:userId")
    List<AnchorDTO> sailuo(Long userId);
}
