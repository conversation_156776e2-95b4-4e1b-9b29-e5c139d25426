package com.mzj.py.mservice.wxuser.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 微信用户表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "dub_wechat_user")
public class WxUser extends IdEntity {
    @Basic
    @Column(name = "openid", nullable = true)
    private String openid; // 小程序openid

    @Column(name = "nickname")
    private String nickname; // 昵称

    @Basic
    @Column(name = "avatar", nullable = true)
    private String avatar; // 微信头像

    @Basic
    @Column(name = "gender", columnDefinition = "smallint")
    private Integer gender; // 性别 0未知 1女 2男

    @Basic
    @Column(name = "area", nullable = true)
    private String area; // 地区

    @Basic
    @Column(name = "phone", nullable = true)
    private String phone; // 手机

    @Basic
    @Column(name = "auth_time", nullable = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date authTime; // 授权时间

    @Basic
    @Column(name = "unionid", nullable = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String unionid; // 授权时间

    @Transient
    private String nicknameToString;
}
