package com.mzj.py.listener;

import com.mzj.py.commons.model.EventMessage;
import com.mzj.py.mservice.order.service.OrderService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@RabbitListener(queuesToDeclare = {
        @Queue("order.close.queue"),
        @Queue("order.update.queue")
})
public class OrderMQListener {

    private Logger LOG = LoggerFactory.getLogger(OrderMQListener.class);
    @Autowired
    private OrderService orderService;


    @RabbitHandler
    public void productOrderHandler(EventMessage eventMessage, Message message, Channel channel){
        LOG.info("监听到消息ProductOrderMQListener messsage消息内容:{}",message);
        try{
            orderService.handleProductOrderMessage(eventMessage);
        }catch (Exception e){
            LOG.error("消费者失败:{}",eventMessage);
            throw new RuntimeException(e);
        }
        LOG.info("消费成功:{}",eventMessage);
    }


}
