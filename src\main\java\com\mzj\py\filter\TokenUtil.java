package com.mzj.py.filter;

import com.mzj.py.commons.exception.ServiceException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

/**
 * Token工具类
 *
 * @date: 2019-12-04
 */
@Component
public class TokenUtil {
    private static final Logger LOG = LoggerFactory.getLogger(TokenUtil.class);

    @Value("${manager.auth.header.token}")
    private String managerAuthHeaderToken;
    @Value(value = "${redis.token.expire.time}")
    private Integer redisTokenExpireTime;
    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate redisTemplate;

    /**
     * 外部应用调用接口token验证
     *
     * @param accessToken
     * @return
     */
    public boolean externalTokenCheck(String accessToken) {
        boolean flag = false;
        LOG.info("ExternalTokenCheck accessToken = {}", accessToken);
        if (StringUtils.isNotBlank(accessToken)) {
            String redisToken = redisTemplate.opsForValue().get(accessToken);
            LOG.info("ExternalTokenCheck accessToken = {},redisToken = {}", accessToken, redisToken);
            if (StringUtils.isNotBlank(redisToken)) {
                flag = true;
            } else {
                throw new ServiceException(HttpStatus.OK, 40001, "登陆过期");
            }
        }
        return flag;
    }

    /**
     * 后管请求校验
     *
     * @return
     */
    public boolean managerCheck(String requestManagerAuthToken) {
        boolean managerFlag = false;
        LOG.info("ManagerCheck requestManagerAuthToken = {}, managerAuthHeaderToken = {}", requestManagerAuthToken, managerAuthHeaderToken);
        if (StringUtils.isNotBlank(requestManagerAuthToken) && requestManagerAuthToken.equals(managerAuthHeaderToken)) {
            managerFlag = true;
        }
        return managerFlag;
    }

}
