package com.mzj.py.commons.enums;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2025-06-16-15:00
 */
public enum StoreUserTypeEnum {
    ADMIN(1,"管理员"),
    USER(2,"普通用户");
    private Integer code;
    private String name;

    StoreUserTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }
}
