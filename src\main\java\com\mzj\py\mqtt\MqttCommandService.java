package com.mzj.py.mqtt;

import com.alibaba.fastjson.JSONObject;
import com.mzj.py.mservice.deviceOperationLog.service.DeviceOperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MqttCommandService {
    @Autowired
    private DeviceOperationLogService deviceOperationLogService;

    @Value("${ali.oss.bucket.url}")
    private String aliUrl;

    /**
     * 设置音量 1加 0减
     * 1.设置音量加: {“cmd”:”sysVolume”,”parm”:”0”}	     //音量加
     * 2.设置音量减: {“cmd”:”sysVolume”,”parm”:”1”}	     //音量减
     */
    public void setVolume(Long deviceId, String sn, Integer status, Long userId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", "sysVolume");
        jsonObject.put("parm", status);
        deviceOperationLogService.sendSave(userId, deviceId, sn, jsonObject.toJSONString());
    }

    /**
     * 设置音乐
     * 3.设置上一曲：{“cmd”:”playmusic”,”parm”:”0”}	     //上一曲
     * 4.设置下一曲：{“cmd”:”playmusic”,”parm”:”1”}	     //下一曲
     * 5.停止播放：  {“cmd”:”playmusic”,”parm”:”2”}	     //停止播放
     * 6.开关机：  {“cmd”:”playmusic”,”parm”:”3”}	     	//开关机
     */
    public String setMusic(Long deviceId,String sn, Integer status, Long userId) {
        String cmd = "playmusic";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", cmd);
        jsonObject.put("parm", status);

        Long id = deviceOperationLogService.sendSave(userId, deviceId, sn, jsonObject.toJSONString());
        return deviceOperationLogService.getResponse(id);
    }

    /**
     * 7.删除音频:{“cmd”:”deleteRing”,”parm”:”151”}
     */
    public void delAudio(Long deviceId, String sn, String audioName, Long userId) {

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", "deleteRing");
        jsonObject.put("parm", audioName);
        deviceOperationLogService.sendSave(userId, deviceId, sn, jsonObject.toJSONString());
    }

    /**
     * 8.新增铃声方案：
     * {
     * “cmd”:”addCase”,
     * ”parm”: ""
     * ”period":
     * [
     * {
     * "caseDay":"mon,tue,thur",//周几配置 使用英文简写标识或者简单字符0-6也可以
     * "startTime":"09:05",
     * "endTime":"10:10",
     * "ringid","151",
     * “caseid”:”0”
     * }
     * ]
     * }
     * //最对设置3组 caseid对应 0 1 2
     */
    public void addCase(Long deviceId, String sn, CaseParams map, Long userId) {
        deviceOperationLogService.sendSave(userId, deviceId, sn, JSONObject.toJSONString(map));
    }

    /**
     * 9.删除铃声方案{“cmd“:”deleteCase”,”parm”:”0”}
     * 10.编辑铃声方案{“cmd“:”editCase”,”parm”:”同新增铃声方案，直接覆盖”}
     * 11、方案开启 {“cmd”:”activeCase”,”parm”:" caseid ":"0"}
     * 12、方案关闭 {“cmd”:”closeCase”,”parm”:" caseid ":"1"}
     */
    public void editCase(Long deviceId, String sn, CaseParams map, Long userId) {
        deviceOperationLogService.sendSave(userId, deviceId, sn, JSONObject.toJSONString(map));
    }

    public void delCase(Long deviceId, String sn, Integer caseId, Long userId) {

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", "deleteCase");
        jsonObject.put("parm", caseId);

        deviceOperationLogService.sendSave(userId, deviceId, sn, jsonObject.toJSONString());
    }

    public void activeCase(Long deviceId, String sn, Integer caseId, Long userId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", "activeCase");
        jsonObject.put("parm", caseId);
        deviceOperationLogService.sendSave(userId, deviceId, sn, jsonObject.toJSONString());
    }

    public void closeCase(Long deviceId, String sn, Integer caseId, Long userId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", "closeCase");
        jsonObject.put("parm", caseId);

        deviceOperationLogService.sendSave(userId, deviceId, sn, jsonObject.toJSONString());
    }

    /**
     * 13、设置时钟 {“cmd”:”setTime”,”parm”:””}  //获取手机的时间
     * 14、获取本地音频文件列表{“cmd”:”getAudioList”,”parm”:””}
     * 返回内容 {“status”:”0”,”list”:[{”name”:”音频1”,”id”:”151”},
     * {“name”:”音频2”,”id”:”152”}]
     * {“name”:”音频3”,”id”:”153”}}
     * 15、试听本地音频文件{“cmd”:”try”,”parm”:”151”}
     * 16、获取剩余空间{“cmd”:” getDisk”,”parm”:””}
     * 17、获取铃声方案{“cmd”:” getCase”,”parm”:””}
     * 18、发送音频地址{“cmd”:” url_cmd”,”parm”:””}
     */
    public String setTime(Long deviceId, String sn, Long userId) {
        String cmd = "setTime";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", cmd);
        jsonObject.put("parm", "");

        Long id = deviceOperationLogService.sendSave(userId, deviceId, sn, jsonObject.toJSONString());
        return deviceOperationLogService.getResponse(id);
    }

    public String getAudioList(Long deviceId, String sn, Long userId) {
        String cmd = "getAudioList";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", cmd);
        jsonObject.put("parm", "");

        Long id = deviceOperationLogService.sendSave(userId, deviceId, sn, jsonObject.toJSONString());
        return deviceOperationLogService.getResponse(id);
    }

    public String tryAudio(Long deviceId, String sn, String audioId, Long userId) {
        String cmd = "try";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", cmd);
        jsonObject.put("parm", audioId);

        Long id = deviceOperationLogService.sendSave(userId, deviceId, sn, jsonObject.toJSONString());
        return deviceOperationLogService.getResponse(id);
    }

    public String getDisk(Long deviceId, String sn, Long userId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", "getDisk");
        jsonObject.put("parm", "");

        Long id = deviceOperationLogService.sendSave(userId, deviceId, sn, jsonObject.toJSONString());
        return deviceOperationLogService.getResponse(id);
    }

    public void getCase(Long deviceId, String sn, Long userId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", "getCase");
        jsonObject.put("parm", "");
        deviceOperationLogService.sendSave(userId, deviceId, sn, jsonObject.toJSONString());
    }

    public void urlCmd(Long deviceId, String sn, String url, Long userId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", "url_cmd");
        jsonObject.put("parm", aliUrl + url);

        deviceOperationLogService.sendSave(userId, deviceId, sn, jsonObject.toJSONString());
    }

    public String getBlueTooth(Long deviceId, String sn, Long userId) {
        String cmd = "getBlueTooth";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd", cmd);
        jsonObject.put("parm", "");

        Long id = deviceOperationLogService.sendSave(userId, deviceId, sn, jsonObject.toJSONString());
        return deviceOperationLogService.getResponse(id);
    }
}
