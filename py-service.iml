<?xml version="1.0" encoding="UTF-8"?>
<module version="4">
  <component name="FacetManager">
    <facet type="jpa" name="JPA">
      <configuration>
        <setting name="validation-enabled" value="true" />
        <setting name="provider-name" value="Hibernate" />
        <datasource-mapping>
          <factory-entry name="entityManagerFactory" />
          <factory-entry name="entityManagerFactoryPrimary" />
          <factory-entry name="py-service" value="911422e6-5cc5-4b9e-a3b5-6efff9ffd84f" />
        </datasource-mapping>
        <naming-strategy-map />
      </configuration>
    </facet>
  </component>
</module>