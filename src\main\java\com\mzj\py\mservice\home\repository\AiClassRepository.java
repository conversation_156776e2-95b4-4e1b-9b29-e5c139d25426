package com.mzj.py.mservice.home.repository;

import com.mzj.py.mservice.home.entity.AiClass;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-08-08-11:07
 */
public interface AiClassRepository extends JpaRepository<AiClass,Long>, JpaSpecificationExecutor<AiClass> {
    List<AiClass> findByDelStatus(int i);
}
