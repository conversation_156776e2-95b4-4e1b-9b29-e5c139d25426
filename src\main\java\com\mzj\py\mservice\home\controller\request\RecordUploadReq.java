package com.mzj.py.mservice.home.controller.request;

import lombok.Data;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-06-26-17:02
 */
@Data
public class RecordUploadReq {
    private Long anchorId;
    private String name;
    private String bgm;
    private Integer bugRate = 30;

    /**
     * 语调
     */
    private Double pitchRate = 0.0;

    /**
     * 语速
     */
    private Double speechRate = 0.0;

    /**
     * 音量
     */
    private Integer volume = 100;

    private Integer beforeDelay = 0;

    private Integer afterDelay = 0;

    private Double bgmCenterVolum = 1.0;
}
