package com.mzj.py.mservice.token.controller;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.mservice.token.service.TokenService;
import com.mzj.py.mservice.token.vo.TokenVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 鉴权获取token
 * <AUTHOR>
 * @Date 2019-12-4
 *
 */
@Controller
@RequestMapping("/token")
public class TokenController {
	 @Autowired
	 private TokenService tokenService;
	 /**
	  * 获取token
	  * @param vo
	  * @return
	  */
	 @PostMapping(value = "/authToken")
	 @ResponseBody
	 public ResultBean<String> authToken(@RequestBody TokenVo vo){
		 return tokenService.authToken(vo);
	 }
	 
}
