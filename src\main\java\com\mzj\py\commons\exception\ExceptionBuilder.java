package com.mzj.py.commons.exception;

import org.springframework.http.HttpStatus;

/**
 * 统一创建Exception 的入口。
 * <AUTHOR>
 *
 */
public class ExceptionBuilder {

	/**
	 * 无效参数，参数校验失败，无效请求等可以此异常
	 * @param message
	 * @return
	 */
	public static ServiceException  InvalidArgumentException(String message) {
		return new InvalidArgumentException(message);
	}
	
	/**
	 * 无效参数，参数校验失败，无效请求等可以此异常
	 * @param code  通用code使用ErrorCode内定义的。 业务code具体项目中定义
	 * @param message
	 * @return
	 */
	public static ServiceException  InvalidArgumentException(int code,String message) {
		return new InvalidArgumentException(code,message);
	}
	
	/**
	 * 无效参数，参数校验失败，无效请求等可以此异常
	 * @param message
	 * @return
	 */
	public static ServiceException  UnauthorizedException(String message) {
		return new UnauthorizedException(message);
	}
	
	public static ServiceException  UnauthorizedException(int code,String message) {
		return new UnauthorizedException(code,message);
	}
	
	public static ServiceException  ForbiddenException(String message) {
		return new ForbiddenException(message);
	}
	
	public static ServiceException  ForbiddenException(int code,String message) {
		return new ForbiddenException(code,message);
	}
	
	public static ServiceException  NotFoundException(String message) {
		return new NotFoundException(message);
	}
	
	public static ServiceException  NotFoundException(int code,String message) {
		return new NotFoundException(code,message);
	}
	
	public static ServiceException  UnexpectedException(String message) {
		return new UnexpectedException(message);
	}
	
	public static ServiceException  UnexpectedException(int code,String message) {
		return new UnexpectedException(code,message);
	}
	
	public static ServiceException  ServiceException(int code,String message) {
		return new ServiceException(code,message);
	}
	
	public static ServiceException  ServiceException(HttpStatus httpStatus, int code, String message){
		return new ServiceException(httpStatus,code,message);
	}
	
}
