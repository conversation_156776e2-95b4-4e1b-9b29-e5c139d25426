package com.mzj.py.mservice.wxuser.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.StatusCode;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.mservice.home.dto.AnchorDTO;
import com.mzj.py.mservice.home.entity.WxKey;
import com.mzj.py.mservice.home.repository.BackgroundMusicRepository;
import com.mzj.py.mservice.home.repository.DubAnchorRepository;
import com.mzj.py.mservice.home.repository.LongAnchorRepository;
import com.mzj.py.mservice.home.repository.WxKeyRepository;
import com.mzj.py.mservice.home.vo.MusicVo;
import com.mzj.py.mservice.home.vo.TemplateVo;
import com.mzj.py.mservice.redis.RedisService;
import com.mzj.py.mservice.token.service.TokenService;
import com.mzj.py.mservice.wxuser.controller.request.*;
import com.mzj.py.mservice.wxuser.entity.*;
import com.mzj.py.mservice.wxuser.repository.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * WxUserService单元测试类
 * 测试WxUserService中的所有公共方法，包括正面和负面场景
 * 
 * @author: Test Author
 * @date: 2024/12/12
 * @version: 1.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("WxUserService单元测试")
class WxUserServiceTest {

    @Mock
    private TokenService tokenService;

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private WxUserRepository wxUserRepository;

    @Mock
    private RedisService redisService;

    @Mock
    private WxKeyRepository wxKeyRepository;

    @Mock
    private BackgroundMusicRepository backgroundMusicRepository;

    @Mock
    private LongFollowAnchorRepository longFollowAnchorRepository;

    @Mock
    private FollowAnchorRepository followAnchorRepository;

    @Mock
    private UserFollowBgmRepository userFollowBgmRepository;

    @Mock
    private LongAnchorRepository longAnchorRepository;

    @Mock
    private UserTextTemplateRepository userTextTemplateRepository;

    @Mock
    private DubAnchorRepository dubAnchorRepository;

    @InjectMocks
    private WxUserService wxUserService;

    private TokenRedisVo mockTokenRedisVo;
    private WxUser mockWxUser;
    private WxKey mockWxKey;
    private String validAccessToken;
    private String validAppId;
    private String validCode;
    private String validSessionKey;

    @BeforeEach
    void setUp() {
        validAccessToken = "valid_token_123";
        validAppId = "wx123456789";
        validCode = "test_code_123";
        validSessionKey = "test_session_key";

        // 设置配置属性
        ReflectionTestUtils.setField(wxUserService, "appid", "wx123456789");
        ReflectionTestUtils.setField(wxUserService, "secret", "test_secret");

        // 设置模拟的TokenRedisVo
        mockTokenRedisVo = new TokenRedisVo();
        mockTokenRedisVo.setId(1L);
        mockTokenRedisVo.setOpenid("test_openid");
        mockTokenRedisVo.setAvatar("test_avatar");
        mockTokenRedisVo.setGender(1);
        mockTokenRedisVo.setPhone("13800138000");
        mockTokenRedisVo.setArea("北京");
        mockTokenRedisVo.setNicknames("测试用户");

        // 设置模拟的WxUser
        mockWxUser = new WxUser();
        mockWxUser.setId(1L);
        mockWxUser.setOpenid("test_openid");
        mockWxUser.setNickname("测试用户");
        mockWxUser.setAvatar("test_avatar");
        mockWxUser.setGender(1);
        mockWxUser.setPhone("13800138000");
        mockWxUser.setArea("北京");
        mockWxUser.setUnionid("test_unionid");
        mockWxUser.setAuthTime(new Date());

        // 设置模拟的WxKey
        mockWxKey = new WxKey();
        mockWxKey.setId(1L);
        mockWxKey.setAppId(validAppId);
        mockWxKey.setSecret("test_secret");
        mockWxKey.setName("测试小程序");
    }

    @Test
    @DisplayName("测试userOpenId方法 - 成功获取openId")
    void testUserOpenId_成功获取openId() {
        // Given
        String mockResponse = "{\"openid\":\"test_openid\",\"unionid\":\"test_unionid\",\"session_key\":\"test_session_key\"}";
        when(restTemplate.getForObject(anyString(), eq(String.class))).thenReturn(mockResponse);

        // When
        ResultBean<Map<String, Object>> result = wxUserService.userOpenId(validAppId, validCode);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNotNull(result.getResultData());

        Map<String, Object> resultData = result.getResultData();
        assertEquals("test_openid", resultData.get("openId"));
        assertEquals("test_unionid", resultData.get("unionId"));

        // 验证方法调用
        verify(restTemplate, times(1)).getForObject(anyString(), eq(String.class));
    }

    @Test
    @DisplayName("测试userOpenId方法 - code参数为空")
    void testUserOpenId_code参数为空() {
        // When
        ResultBean<Map<String, Object>> result = wxUserService.userOpenId(validAppId, "");

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.ERROR_CODE_10004.getErrorCode(), result.getCode());
        assertEquals("CODE不能为空", result.getMsg());
        assertEquals(Collections.emptyMap(), result.getResultData());

        // 验证不会调用外部接口
        verify(restTemplate, never()).getForObject(anyString(), eq(String.class));
    }

    @Test
    @DisplayName("测试userOpenId方法 - appId参数为空")
    void testUserOpenId_appId参数为空() {
        // When
        ResultBean<Map<String, Object>> result = wxUserService.userOpenId("", validCode);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.ERROR_CODE_10004.getErrorCode(), result.getCode());
        assertEquals("小程序appId不能为空", result.getMsg());
        assertEquals(Collections.emptyMap(), result.getResultData());

        // 验证不会调用外部接口
        verify(restTemplate, never()).getForObject(anyString(), eq(String.class));
    }

    @Test
    @DisplayName("测试userOpenId方法 - 微信接口返回错误")
    void testUserOpenId_微信接口返回错误() {
        // Given
        String mockErrorResponse = "{\"errcode\":40013,\"errmsg\":\"invalid appid\"}";
        when(restTemplate.getForObject(anyString(), eq(String.class))).thenReturn(mockErrorResponse);

        // When
        ResultBean<Map<String, Object>> result = wxUserService.userOpenId(validAppId, validCode);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.ERROR_CODE_10001.getErrorCode(), result.getCode());
        assertEquals("用户openId为空", result.getMsg());

        // 验证方法调用
        verify(restTemplate, times(1)).getForObject(anyString(), eq(String.class));
    }

    @Test
    @DisplayName("测试userOpenId方法 - 网络异常")
    void testUserOpenId_网络异常() {
        // Given
        when(restTemplate.getForObject(anyString(), eq(String.class))).thenThrow(new RuntimeException("网络异常"));

        // When
        ResultBean<Map<String, Object>> result = wxUserService.userOpenId(validAppId, validCode);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.ERROR_CODE_10001.getErrorCode(), result.getCode());
        assertEquals(StatusCode.ERROR_CODE_30001.getErrorMsg(), result.getMsg());

        // 验证方法调用
        verify(restTemplate, times(1)).getForObject(anyString(), eq(String.class));
    }

    @Test
    @DisplayName("测试updatePhone方法 - 成功更新手机号")
    void testUpdatePhone_成功更新手机号() {
        // Given
        Long userId = 1L;
        String newPhone = "13900139000";
        when(wxUserRepository.findById(userId)).thenReturn(Optional.of(mockWxUser));
        when(wxUserRepository.save(any(WxUser.class))).thenReturn(mockWxUser);

        // When
        ResultBean result = wxUserService.updatePhone(userId, newPhone);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertTrue((Boolean) result.getResultData());

        // 验证用户手机号被更新
        ArgumentCaptor<WxUser> userCaptor = ArgumentCaptor.forClass(WxUser.class);
        verify(wxUserRepository, times(1)).save(userCaptor.capture());
        assertEquals(newPhone, userCaptor.getValue().getPhone());
    }

    @Test
    @DisplayName("测试updatePhone方法 - 用户不存在")
    void testUpdatePhone_用户不存在() {
        // Given
        Long userId = 999L;
        String newPhone = "13900139000";
        when(wxUserRepository.findById(userId)).thenReturn(Optional.empty());

        // When
        ResultBean result = wxUserService.updatePhone(userId, newPhone);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.ERROR_CODE_10001.getErrorCode(), result.getCode());
        assertEquals(StatusCode.ERROR_CODE_10001.getErrorMsg(), result.getMsg());
        assertFalse((Boolean) result.getResultData());

        // 验证不会保存用户
        verify(wxUserRepository, never()).save(any(WxUser.class));
    }

    @Test
    @DisplayName("测试getMyLongAnchor方法 - 成功获取长音频主播列表")
    void testGetMyLongAnchor_成功获取长音频主播列表() {
        // Given
        List<AnchorDTO> mockAnchorList = Arrays.asList(
                createMockAnchorDTO(1L, "主播1", "场景1"),
                createMockAnchorDTO(2L, "主播2", "场景2"));

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(longAnchorRepository.taijia(mockTokenRedisVo.getId())).thenReturn(mockAnchorList);

        // When
        ResultBean<Map<String, Object>> result = wxUserService.getMyLongAnchor(validAccessToken);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNotNull(result.getResultData());

        Map<String, Object> resultData = result.getResultData();
        assertEquals(mockAnchorList, resultData.get("result"));

        // 验证方法调用
        verify(redisService, times(1)).findTokenVo(validAccessToken);
        verify(longAnchorRepository, times(1)).taijia(mockTokenRedisVo.getId());
    }

    @Test
    @DisplayName("测试getMyVipAnchor方法 - 成功获取VIP主播列表")
    void testGetMyVipAnchor_成功获取VIP主播列表() {
        // Given
        List<AnchorDTO> mockAnchorList = Arrays.asList(
                createMockAnchorDTO(1L, "VIP主播1", "VIP场景1"),
                createMockAnchorDTO(2L, "VIP主播2", "VIP场景2"));

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(dubAnchorRepository.sailuo(mockTokenRedisVo.getId())).thenReturn(mockAnchorList);

        // When
        ResultBean<Map<String, Object>> result = wxUserService.getMyVipAnchor(validAccessToken);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNotNull(result.getResultData());

        Map<String, Object> resultData = result.getResultData();
        assertEquals(mockAnchorList, resultData.get("result"));

        // 验证方法调用
        verify(redisService, times(1)).findTokenVo(validAccessToken);
        verify(dubAnchorRepository, times(1)).sailuo(mockTokenRedisVo.getId());
    }

    @Test
    @DisplayName("测试followAnchor方法 - 成功关注主播（新关注）")
    void testFollowAnchor_成功关注主播_新关注() {
        // Given
        FollowAnchorReq request = new FollowAnchorReq();
        request.setAnchorId(1L);

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(followAnchorRepository.findByAnchorIdAndUserId(1L, mockTokenRedisVo.getId()))
                .thenReturn(new ArrayList<>());
        when(followAnchorRepository.save(any(FollowAnchor.class))).thenReturn(new FollowAnchor());

        // When
        ResultBean<Object> result = wxUserService.followAnchor(validAccessToken, request);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNull(result.getResultData());

        // 验证保存了新的关注记录
        ArgumentCaptor<FollowAnchor> followCaptor = ArgumentCaptor.forClass(FollowAnchor.class);
        verify(followAnchorRepository, times(1)).save(followCaptor.capture());

        FollowAnchor savedFollow = followCaptor.getValue();
        assertEquals(1L, savedFollow.getAnchorId());
        assertEquals(mockTokenRedisVo.getId(), savedFollow.getUserId());

        // 验证不会删除记录
        verify(followAnchorRepository, never()).delete(any(FollowAnchor.class));
    }

    @Test
    @DisplayName("测试followAnchor方法 - 成功取消关注主播（已关注）")
    void testFollowAnchor_成功取消关注主播_已关注() {
        // Given
        FollowAnchorReq request = new FollowAnchorReq();
        request.setAnchorId(1L);

        FollowAnchor existingFollow = new FollowAnchor();
        existingFollow.setId(1L);
        existingFollow.setAnchorId(1L);
        existingFollow.setUserId(mockTokenRedisVo.getId());

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(followAnchorRepository.findByAnchorIdAndUserId(1L, mockTokenRedisVo.getId()))
                .thenReturn(Arrays.asList(existingFollow));

        // When
        ResultBean<Object> result = wxUserService.followAnchor(validAccessToken, request);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNull(result.getResultData());

        // 验证删除了关注记录
        verify(followAnchorRepository, times(1)).delete(existingFollow);

        // 验证不会保存新记录
        verify(followAnchorRepository, never()).save(any(FollowAnchor.class));
    }

    @Test
    @DisplayName("测试followLongAnchor方法 - 成功关注长音频主播（新关注）")
    void testFollowLongAnchor_成功关注长音频主播_新关注() {
        // Given
        FollowAnchorReq request = new FollowAnchorReq();
        request.setAnchorId(1L);

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(longFollowAnchorRepository.findByAnchorIdAndUserId(1L, mockTokenRedisVo.getId()))
                .thenReturn(new ArrayList<>());
        when(longFollowAnchorRepository.save(any(LongFollowAnchor.class))).thenReturn(new LongFollowAnchor());

        // When
        ResultBean<Object> result = wxUserService.followLongAnchor(validAccessToken, request);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNull(result.getResultData());

        // 验证保存了新的关注记录
        ArgumentCaptor<LongFollowAnchor> followCaptor = ArgumentCaptor.forClass(LongFollowAnchor.class);
        verify(longFollowAnchorRepository, times(1)).save(followCaptor.capture());

        LongFollowAnchor savedFollow = followCaptor.getValue();
        assertEquals(1L, savedFollow.getAnchorId());
        assertEquals(mockTokenRedisVo.getId(), savedFollow.getUserId());

        // 验证不会删除记录
        verify(longFollowAnchorRepository, never()).delete(any(LongFollowAnchor.class));
    }

    @Test
    @DisplayName("测试followLongAnchor方法 - 成功取消关注长音频主播（已关注）")
    void testFollowLongAnchor_成功取消关注长音频主播_已关注() {
        // Given
        FollowAnchorReq request = new FollowAnchorReq();
        request.setAnchorId(1L);

        LongFollowAnchor existingFollow = new LongFollowAnchor();
        existingFollow.setId(1L);
        existingFollow.setAnchorId(1L);
        existingFollow.setUserId(mockTokenRedisVo.getId());

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(longFollowAnchorRepository.findByAnchorIdAndUserId(1L, mockTokenRedisVo.getId()))
                .thenReturn(Arrays.asList(existingFollow));

        // When
        ResultBean<Object> result = wxUserService.followLongAnchor(validAccessToken, request);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNull(result.getResultData());

        // 验证删除了关注记录
        verify(longFollowAnchorRepository, times(1)).delete(existingFollow);

        // 验证不会保存新记录
        verify(longFollowAnchorRepository, never()).save(any(LongFollowAnchor.class));
    }

    @Test
    @DisplayName("测试followBgm方法 - 成功关注背景音乐（新关注）")
    void testFollowBgm_成功关注背景音乐_新关注() {
        // Given
        FollowBgmReq request = new FollowBgmReq();
        request.setBgmId(1L);

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(userFollowBgmRepository.findByBgmIdAndUserId(1L, mockTokenRedisVo.getId()))
                .thenReturn(new ArrayList<>());
        when(userFollowBgmRepository.save(any(UserFollowBgm.class))).thenReturn(new UserFollowBgm());

        // When
        ResultBean<Object> result = wxUserService.followBgm(validAccessToken, request);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNull(result.getResultData());

        // 验证保存了新的关注记录
        ArgumentCaptor<UserFollowBgm> followCaptor = ArgumentCaptor.forClass(UserFollowBgm.class);
        verify(userFollowBgmRepository, times(1)).save(followCaptor.capture());

        UserFollowBgm savedFollow = followCaptor.getValue();
        assertEquals(1L, savedFollow.getBgmId());
        assertEquals(mockTokenRedisVo.getId(), savedFollow.getUserId());

        // 验证不会删除记录
        verify(userFollowBgmRepository, never()).delete(any(UserFollowBgm.class));
    }

    @Test
    @DisplayName("测试followBgm方法 - 成功取消关注背景音乐（已关注）")
    void testFollowBgm_成功取消关注背景音乐_已关注() {
        // Given
        FollowBgmReq request = new FollowBgmReq();
        request.setBgmId(1L);

        UserFollowBgm existingFollow = new UserFollowBgm();
        existingFollow.setUserId(mockTokenRedisVo.getId());
        existingFollow.setBgmId(1L);

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(userFollowBgmRepository.findByBgmIdAndUserId(1L, mockTokenRedisVo.getId()))
                .thenReturn(Arrays.asList(existingFollow));

        // When
        ResultBean<Object> result = wxUserService.followBgm(validAccessToken, request);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNull(result.getResultData());

        // 验证删除了关注记录
        verify(userFollowBgmRepository, times(1)).delete(existingFollow);

        // 验证不会保存新记录
        verify(userFollowBgmRepository, never()).save(any(UserFollowBgm.class));
    }

    @Test
    @DisplayName("测试getFollowBgm方法 - 成功获取关注的背景音乐列表")
    void testGetFollowBgm_成功获取关注的背景音乐列表() {
        // Given
        List<MusicVo> mockMusicList = Arrays.asList(
                createMockMusicVo(1L, "音乐1"),
                createMockMusicVo(2L, "音乐2"));

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(backgroundMusicRepository.findFollowBgm(mockTokenRedisVo.getId())).thenReturn(mockMusicList);

        // When
        ResultBean<Map<String, Object>> result = wxUserService.getFollowBgm(validAccessToken);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNotNull(result.getResultData());

        Map<String, Object> resultData = result.getResultData();
        assertEquals(mockMusicList, resultData.get("result"));

        // 验证方法调用
        verify(redisService, times(1)).findTokenVo(validAccessToken);
        verify(backgroundMusicRepository, times(1)).findFollowBgm(mockTokenRedisVo.getId());
    }

    @Test
    @DisplayName("测试addTextTemplate方法 - 成功添加新文本模板")
    void testAddTextTemplate_成功添加新文本模板() {
        // Given
        TextTemplateAddReq request = new TextTemplateAddReq();
        request.setTextContent("这是一个测试模板");

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(userTextTemplateRepository.save(any(UserTextTemplate.class))).thenReturn(new UserTextTemplate());

        // When
        ResultBean<Object> result = wxUserService.addTextTemplate(validAccessToken, request);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertTrue((Boolean) result.getResultData());

        // 验证保存了新的模板
        ArgumentCaptor<UserTextTemplate> templateCaptor = ArgumentCaptor.forClass(UserTextTemplate.class);
        verify(userTextTemplateRepository, times(1)).save(templateCaptor.capture());

        UserTextTemplate savedTemplate = templateCaptor.getValue();
        assertEquals("这是一个测试模板", savedTemplate.getTextContent());
        assertEquals(mockTokenRedisVo.getId(), savedTemplate.getUserId());
        assertNull(savedTemplate.getId()); // 新模板ID应该为null
    }

    @Test
    @DisplayName("测试addTextTemplate方法 - 成功更新现有文本模板")
    void testAddTextTemplate_成功更新现有文本模板() {
        // Given
        TextTemplateAddReq request = new TextTemplateAddReq();
        request.setId(1L);
        request.setTextContent("这是一个更新的模板");

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(userTextTemplateRepository.save(any(UserTextTemplate.class))).thenReturn(new UserTextTemplate());

        // When
        ResultBean<Object> result = wxUserService.addTextTemplate(validAccessToken, request);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertTrue((Boolean) result.getResultData());

        // 验证保存了更新的模板
        ArgumentCaptor<UserTextTemplate> templateCaptor = ArgumentCaptor.forClass(UserTextTemplate.class);
        verify(userTextTemplateRepository, times(1)).save(templateCaptor.capture());

        UserTextTemplate savedTemplate = templateCaptor.getValue();
        assertEquals("这是一个更新的模板", savedTemplate.getTextContent());
        assertEquals(mockTokenRedisVo.getId(), savedTemplate.getUserId());
        assertEquals(1L, savedTemplate.getId()); // 更新模板应该有ID
    }

    // 辅助方法：创建模拟的AnchorDTO
    private AnchorDTO createMockAnchorDTO(Long id, String name, String usageScenario) {
        AnchorDTO anchorDTO = new AnchorDTO();
        // 注意：这里需要根据实际的AnchorDTO构造函数来调整
        // 假设AnchorDTO有相应的setter方法
        return anchorDTO;
    }

    @Test
    @DisplayName("测试getTextTemplate方法 - 成功获取文本模板列表")
    void testGetTextTemplate_成功获取文本模板列表() {
        // Given
        List<UserTextTemplate> mockTemplateList = Arrays.asList(
                createMockUserTextTemplate(1L, "模板1"),
                createMockUserTextTemplate(2L, "模板2"));

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(userTextTemplateRepository.findByUserId(mockTokenRedisVo.getId())).thenReturn(mockTemplateList);

        // When
        ResultBean<Map<String, Object>> result = wxUserService.getTextTemplate(validAccessToken);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNotNull(result.getResultData());

        Map<String, Object> resultData = result.getResultData();
        @SuppressWarnings("unchecked")
        List<TemplateVo> templateVoList = (List<TemplateVo>) resultData.get("result");
        assertEquals(2, templateVoList.size());

        // 验证方法调用
        verify(redisService, times(1)).findTokenVo(validAccessToken);
        verify(userTextTemplateRepository, times(1)).findByUserId(mockTokenRedisVo.getId());
    }

    @Test
    @DisplayName("测试getTextTemplate方法 - 获取空的文本模板列表")
    void testGetTextTemplate_获取空的文本模板列表() {
        // Given
        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(userTextTemplateRepository.findByUserId(mockTokenRedisVo.getId())).thenReturn(new ArrayList<>());

        // When
        ResultBean<Map<String, Object>> result = wxUserService.getTextTemplate(validAccessToken);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNotNull(result.getResultData());

        Map<String, Object> resultData = result.getResultData();
        @SuppressWarnings("unchecked")
        List<TemplateVo> templateVoList = (List<TemplateVo>) resultData.get("result");
        assertEquals(0, templateVoList.size());
    }

    @Test
    @DisplayName("测试delTextTemplate方法 - 成功删除文本模板")
    void testDelTextTemplate_成功删除文本模板() {
        // Given
        TextTemplateDelReq request = new TextTemplateDelReq();
        request.setId(1L);

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);

        // When
        ResultBean<Object> result = wxUserService.delTextTemplate(validAccessToken, request);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertTrue((Boolean) result.getResultData());

        // 验证删除了模板
        verify(userTextTemplateRepository, times(1)).deleteByIdAndUserId(1L, mockTokenRedisVo.getId());
    }

    @Test
    @DisplayName("测试userPhone方法 - 成功获取手机号（新用户）")
    void testUserPhone_成功获取手机号_新用户() {
        // Given
        String sessionKey = "test_session_key";
        String encryptedData = "encrypted_phone_data";
        String iv = "test_iv";
        String expectedPhone = "13800138000";

        // 模拟解密后的JSON数据
        JSONObject decryptedData = new JSONObject();
        decryptedData.put("phoneNumber", expectedPhone);

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(wxUserRepository.findByOpenid(mockTokenRedisVo.getOpenid())).thenReturn(null);
        when(wxUserRepository.save(any(WxUser.class))).thenReturn(mockWxUser);

        // 使用spy来模拟decryptionWxData方法
        WxUserService spyService = spy(wxUserService);
        doReturn(decryptedData).when(spyService).decryptionWxData(encryptedData, sessionKey, iv);

        // When
        ResultBean<Map<String, Object>> result = spyService.userPhone(validAccessToken, sessionKey, encryptedData, iv);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNotNull(result.getResultData());

        Map<String, Object> resultData = result.getResultData();
        assertEquals(expectedPhone, resultData.get("phone"));

        // 验证保存了新用户
        ArgumentCaptor<WxUser> userCaptor = ArgumentCaptor.forClass(WxUser.class);
        verify(wxUserRepository, times(1)).save(userCaptor.capture());

        WxUser savedUser = userCaptor.getValue();
        assertEquals(mockTokenRedisVo.getOpenid(), savedUser.getOpenid());
        assertEquals(expectedPhone, savedUser.getPhone());

        // 验证更新了token信息
        verify(tokenService, times(1)).updateAuthTokenInfo(eq(validAccessToken), any(TokenRedisVo.class));
    }

    @Test
    @DisplayName("测试userPhone方法 - 成功获取手机号（已存在用户）")
    void testUserPhone_成功获取手机号_已存在用户() {
        // Given
        String sessionKey = "test_session_key";
        String encryptedData = "encrypted_phone_data";
        String iv = "test_iv";
        String expectedPhone = "13800138000";

        // 模拟解密后的JSON数据
        JSONObject decryptedData = new JSONObject();
        decryptedData.put("phoneNumber", expectedPhone);

        when(redisService.findTokenVo(validAccessToken)).thenReturn(mockTokenRedisVo);
        when(wxUserRepository.findByOpenid(mockTokenRedisVo.getOpenid())).thenReturn(mockWxUser);
        when(wxUserRepository.save(any(WxUser.class))).thenReturn(mockWxUser);

        // 使用spy来模拟decryptionWxData方法
        WxUserService spyService = spy(wxUserService);
        doReturn(decryptedData).when(spyService).decryptionWxData(encryptedData, sessionKey, iv);

        // When
        ResultBean<Map<String, Object>> result = spyService.userPhone(validAccessToken, sessionKey, encryptedData, iv);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNotNull(result.getResultData());

        Map<String, Object> resultData = result.getResultData();
        assertEquals(expectedPhone, resultData.get("phone"));

        // 验证更新了现有用户
        ArgumentCaptor<WxUser> userCaptor = ArgumentCaptor.forClass(WxUser.class);
        verify(wxUserRepository, times(1)).save(userCaptor.capture());

        WxUser savedUser = userCaptor.getValue();
        assertEquals(expectedPhone, savedUser.getPhone());

        // 验证更新了token信息
        verify(tokenService, times(1)).updateAuthTokenInfo(eq(validAccessToken), any(TokenRedisVo.class));
    }

    // 辅助方法：创建模拟的UserTextTemplate
    private UserTextTemplate createMockUserTextTemplate(Long id, String content) {
        UserTextTemplate template = new UserTextTemplate();
        template.setId(id);
        template.setTextContent(content);
        template.setUserId(mockTokenRedisVo.getId());
        return template;
    }

    // 辅助方法：创建模拟的MusicVo
    private MusicVo createMockMusicVo(Long id, String name) {
        MusicVo musicVo = new MusicVo();
        // 注意：这里需要根据实际的MusicVo构造函数来调整
        // 假设MusicVo有相应的setter方法
        return musicVo;
    }
}
