package com.mzj.py.mservice.token.service;

import com.alibaba.fastjson.JSON;
import com.mzj.py.commons.*;
import com.mzj.py.mservice.token.vo.TokenVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.TimeUnit;

@Service
public class TokenService {
    private Logger LOG = LoggerFactory.getLogger(TokenService.class);
    @Value(value = "${token.auth.secretKey}")
    private String tokenAuthSecretKey;//秘钥
    @Value(value = "${redis.token.expire.time}")
    private Integer redisTokenExpireTime;//过期时间

    @Autowired
    @Qualifier("stringRedisTemplate")
    public StringRedisTemplate redisTemplate;

    @SuppressWarnings("static-access")
    public ResultBean<String> authToken(TokenVo vo) {
        LOG.info("TokenService authToken parmas = {}", vo.toString());
        ResultBean<String> resultBean = new ResultBean<>();
        try {
            resultBean.setCode(StatusCode.SUCCESS_CODE_10000.getErrorCode());
            resultBean.setMsg(StatusCode.SUCCESS_CODE_10000.getErrorMsg());

            if (StringUtils.isBlank(vo.getCode()) || StringUtils.isBlank(vo.getEncryptCode()) || StringUtils.isBlank(vo.getIvParameter())) {
                resultBean.setCode(StatusCode.ERROR_CODE_10004.getErrorCode());
                resultBean.setMsg(StatusCode.ERROR_CODE_10004.getErrorMsg());
                return resultBean;
            }

            AESUtil aesUtil = AESUtil.getInstance(tokenAuthSecretKey);
            String decryptCode = aesUtil.aesDecrypt(vo.getEncryptCode(), vo.getIvParameter());  //校验加密字符串是否合法
            if (!StringUtils.equals(vo.getCode(), decryptCode)) {
                resultBean.setMsg(StatusCode.ERROR_CODE_10003.getErrorMsg());
                resultBean.setCode(StatusCode.ERROR_CODE_10003.getErrorCode());
                return resultBean;
            }

            String md5Code = MD5Utils.MD5Encode(vo.getEncryptCode(), "UTF-8"); //进行一次md5算法，去除特殊符号
            String tokenKeyValue = RedisKeyConstant.SP_TOKEN + ":" + md5Code;
            if (redisTemplate.opsForValue().get(tokenKeyValue) == null) { //如果为空的时候,插入redis数据库
                redisTemplate.opsForValue().set(tokenKeyValue, tokenKeyValue, redisTokenExpireTime, TimeUnit.MINUTES);
                LOG.info("TokenService authToken redis save success, key = {}, value = {}", tokenKeyValue, tokenKeyValue);
            }

            resultBean.setResultData(redisTemplate.opsForValue().get(tokenKeyValue));
            return resultBean;

        } catch (Exception e) {
            LOG.error("TokenService authToken Exception = {}", e);
            resultBean.setCode(StatusCode.ERROR_CODE_30001.getErrorCode());
            resultBean.setMsg(StatusCode.ERROR_CODE_30001.getErrorMsg());
            return resultBean;
        }

    }

    /**
     * 根据openId生成token
     *
     * @param openid
     * @param vo
     * @return
     */
    public String authToken(String openid, TokenRedisVo vo) {
        LOG.info("TokenService authToken openid={}, vo={}", openid, JSON.toJSONString(vo));
        try {
            String tokenStr = openid + new Date().getTime();
            String md5Token = MD5Utils.MD5Encode(tokenStr, "UTF-8");
            String md5tokenKey = RedisKeyConstant.LOGIN_TOKEN + ":" + md5Token;

            redisTemplate.opsForValue().set(md5tokenKey, JSON.toJSONString(vo), redisTokenExpireTime, TimeUnit.MINUTES);
            // 缓存openid与token关系(用户删除用户时清理token)
            redisTemplate.opsForValue().set("LOGIN_USER_" + openid, md5tokenKey, redisTokenExpireTime, TimeUnit.MINUTES);
            return md5tokenKey;
        } catch (Exception e) {
            LOG.error("TokenService authToken Exception = {}", e);

        }
        return null;
    }

    /**
     * 更新token缓存内容
     *
     * @param accessToken
     * @param vo
     * <AUTHOR>
     * @date 2021年6月24日
     */
    public void updateAuthTokenInfo(String accessToken, TokenRedisVo vo) {
        redisTemplate.opsForValue().set(accessToken, JSON.toJSONString(vo), redisTokenExpireTime, TimeUnit.MINUTES);
    }

    /**
     * 通过token获取用户信息
     *
     * @param token
     * @return
     */
    public TokenRedisVo getAuthToken(String token) {
        Object object = redisTemplate.opsForValue().get(token);
        if (object != null) {
            TokenRedisVo vo = JSON.parseObject(object.toString(), TokenRedisVo.class);
            return vo;
        }

        return null;
    }
}
