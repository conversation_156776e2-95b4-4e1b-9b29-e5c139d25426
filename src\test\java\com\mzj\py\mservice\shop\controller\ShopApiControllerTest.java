package com.mzj.py.mservice.shop.controller;

import com.alibaba.fastjson.JSON;
import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.commons.enums.StoreReviewEnum;
import com.mzj.py.commons.enums.StoreTypeEnum;
import com.mzj.py.commons.enums.StoreUserTypeEnum;
import com.mzj.py.mservice.common.PageResult;
import com.mzj.py.mservice.shop.entity.Shop;
import com.mzj.py.mservice.shop.service.StoreService;
import com.mzj.py.mservice.shop.vo.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * ShopApiController单元测试类
 */
@ExtendWith(MockitoExtension.class)
class ShopApiControllerTest {

        @InjectMocks
        private ShopApiController shopApiController;

        @Mock
        private StoreService storeService;

        private MockMvc mockMvc;
        private static final String ACCESS_TOKEN = "test-access-token";
        private static final String ACCESS_TOKEN_HEADER = "accessToken";

        private StorePageParams mockPageParams;
        private StoreAddVo mockStoreAddVo;
        private StoreBindDeviceParams mockBindDeviceParams;
        private StoreBindUserParams mockBindUserParams;
        private PageResult<StorePageVo> mockPageResult;
        private StorePageVo mockStorePageVo;

        @BeforeEach
        void setUp() {
                shopApiController = spy(new ShopApiController());
                try {
                        java.lang.reflect.Field field = ShopApiController.class.getDeclaredField("storeService");
                        field.setAccessible(true);
                        field.set(shopApiController, storeService);
                } catch (Exception e) {
                        // fallback
                }

                mockMvc = MockMvcBuilders.standaloneSetup(shopApiController).build();

                // Mock ApiBaseController methods - use lenient to avoid unnecessary stubbing
                // exceptions
                TokenRedisVo mockUser = new TokenRedisVo();
                mockUser.setId(1L);
                mockUser.setPhone("13800138000");

                lenient().doReturn(Arrays.asList(1L, 2L)).when(shopApiController).getShopIds(ACCESS_TOKEN);
                lenient().doReturn(mockUser).when(shopApiController).getUser(ACCESS_TOKEN);
                lenient().doReturn(1L).when(shopApiController).getShopId(ACCESS_TOKEN);

                // Initialize mock objects
                mockPageParams = new StorePageParams();
                mockPageParams.setCurrentPage(1);
                mockPageParams.setPageSize(10);

                mockStoreAddVo = new StoreAddVo();
                mockStoreAddVo.setStoreName("测试门店");
                mockStoreAddVo.setParentId(0L);

                mockBindDeviceParams = new StoreBindDeviceParams();
                mockBindDeviceParams.setStoreId(1L);
                mockBindDeviceParams.setDeviceIds(Arrays.asList(1L, 2L));

                mockBindUserParams = new StoreBindUserParams();
                mockBindUserParams.setStoreId(1L);
                mockBindUserParams.setUserId(2L);

                mockStorePageVo = createStorePageVo();
                mockPageResult = new PageResult<>();
                mockPageResult.setTotal(1L);
                mockPageResult.setResult(Arrays.asList(mockStorePageVo));
        }

        /**
         * 门店分页列表-成功场景
         */
        @Test
        @DisplayName("门店分页列表-成功场景")
        void testPageList_Success() throws Exception {
                // Given
                when(storeService.pageList(any(StorePageParams.class)))
                                .thenReturn(ResultBean.successfulResult(mockPageResult));

                // When & Then
                mockMvc.perform(post("/mini/store/list")
                                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(mockPageParams)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData.total").value(1))
                                .andExpect(jsonPath("$.resultData.result[0].id").value(1));

                verify(storeService).pageList(any(StorePageParams.class));
        }

        /**
         * 门店分页列表-空结果
         */
        @Test
        @DisplayName("门店分页列表-空结果")
        void testPageList_EmptyResult() throws Exception {
                // Given
                PageResult<StorePageVo> emptyResult = new PageResult<>();
                emptyResult.setTotal(0L);
                emptyResult.setResult(Collections.emptyList());

                when(storeService.pageList(any(StorePageParams.class)))
                                .thenReturn(ResultBean.successfulResult(emptyResult));

                // When & Then
                mockMvc.perform(post("/mini/store/list")
                                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(mockPageParams)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData.total").value(0))
                                .andExpect(jsonPath("$.resultData.result").isEmpty());
        }

        /**
         * 门店分页列表-服务失败
         */
        @Test
        @DisplayName("门店分页列表-服务失败")
        void testPageList_ServiceFailure() throws Exception {
                // Given
                when(storeService.pageList(any(StorePageParams.class)))
                                .thenReturn(ResultBean.failedResultWithMsg("查询失败"));

                // When & Then
                mockMvc.perform(post("/mini/store/list")
                                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(mockPageParams)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(false))
                                .andExpect(jsonPath("$.msg").value("查询失败"));
        }

        /**
         * 新增门店-成功
         */
        @Test
        @DisplayName("新增门店-成功")
        void testAdd_Success() throws Exception {
                // Given
                when(storeService.add(any(StoreAddVo.class)))
                                .thenReturn(ResultBean.successfulResult("新增成功"));

                // When & Then
                mockMvc.perform(post("/mini/store/add")
                                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(mockStoreAddVo)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData").value("新增成功"));

                verify(storeService).add(argThat(vo -> vo.getCUserId().equals(1L) && // 验证用户ID被设置
                                vo.getStoreName().equals("测试门店")));
        }

        /**
         * 新增门店-校验失败
         */
        @Test
        @DisplayName("新增门店-校验失败")
        void testAdd_ValidationFailure() throws Exception {
                // Given
                when(storeService.add(any(StoreAddVo.class)))
                                .thenReturn(ResultBean.failedResultWithMsg("该用户已有绑定关系，不允许新增门店"));

                // When & Then
                mockMvc.perform(post("/mini/store/add")
                                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(mockStoreAddVo)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(false))
                                .andExpect(jsonPath("$.msg").value("该用户已有绑定关系，不允许新增门店"));
        }

        /**
         * 修改门店-成功
         */
        @Test
        @DisplayName("修改门店-成功")
        void testUpdate_Success() throws Exception {
                // Given
                mockStoreAddVo.setId(1L);
                when(storeService.update(any(StoreAddVo.class)))
                                .thenReturn(ResultBean.successfulResult("修改成功"));

                // When & Then
                mockMvc.perform(post("/mini/store/update")
                                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(mockStoreAddVo)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData").value("修改成功"));

                verify(storeService).update(any(StoreAddVo.class));
        }

        /**
         * 修改门店-店铺不存在
         */
        @Test
        @DisplayName("修改门店-店铺不存在")
        void testUpdate_ShopNotExists() throws Exception {
                // Given
                mockStoreAddVo.setId(999L);
                when(storeService.update(any(StoreAddVo.class)))
                                .thenReturn(ResultBean.failedResultWithMsg("店铺不存在"));

                // When & Then
                mockMvc.perform(post("/mini/store/update")
                                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(mockStoreAddVo)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(false))
                                .andExpect(jsonPath("$.msg").value("店铺不存在"));
        }

        /**
         * 绑定设备-成功
         */
        @Test
        @DisplayName("绑定设备-成功")
        void testBindDevice_Success() throws Exception {
                // Given
                when(storeService.bindDevice(any(StoreBindDeviceParams.class)))
                                .thenReturn(ResultBean.successfulResult("绑定设备成功"));

                // When & Then
                mockMvc.perform(post("/mini/store/bindDevice")
                                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(mockBindDeviceParams)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData").value("绑定设备成功"));

                verify(storeService).bindDevice(argThat(params -> params.getUserId().equals(1L) // 验证用户ID被设置
                ));
        }

        /**
         * 绑定设备-设备不存在
         */
        @Test
        @DisplayName("绑定设备-设备不存在")
        void testBindDevice_DevicesNotExist() throws Exception {
                // Given
                when(storeService.bindDevice(any(StoreBindDeviceParams.class)))
                                .thenReturn(ResultBean.failedResultWithMsg("设备不存在"));

                // When & Then
                mockMvc.perform(post("/mini/store/bindDevice")
                                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(mockBindDeviceParams)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(false))
                                .andExpect(jsonPath("$.msg").value("设备不存在"));
        }

        /**
         * 获取门店用户-成功
         */
        @Test
        @DisplayName("获取门店用户-成功")
        void testGetUsers_Success() throws Exception {
                // Given
                PageResult<StoreUserPageVo> userPageResult = new PageResult<>();
                userPageResult.setTotal(1L);
                userPageResult.setResult(Arrays.asList(createStoreUserPageVo()));

                when(storeService.getUsers(eq(1L), any(StorePageParams.class)))
                                .thenReturn(ResultBean.successfulResult(userPageResult));

                // When & Then
                mockMvc.perform(post("/mini/store/users/1")
                                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(mockPageParams)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData.total").value(1))
                                .andExpect(jsonPath("$.resultData.result[0].id").value(1));
        }

        /**
         * 查询门店详情-成功
         */
        @Test
        @DisplayName("查询门店详情-成功")
        void testDetail_Success() throws Exception {
                // Given
                when(storeService.detail(1L, 1L, Arrays.asList(1L, 2L)))
                                .thenReturn(ResultBean.successfulResult(mockStorePageVo));

                // When & Then
                mockMvc.perform(get("/mini/store/detail/1")
                                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData.id").value(1))
                                .andExpect(jsonPath("$.resultData.storeName").value("测试门店"));
        }

        /**
         * 查询门店详情-用户未绑定
         */
        @Test
        @DisplayName("查询门店详情-用户未绑定")
        void testDetail_UserNotBound() throws Exception {
                // Given
                when(storeService.detail(1L, 1L, Arrays.asList(1L, 2L)))
                                .thenReturn(ResultBean.failedResultWithMsg("用户未绑定该店铺"));

                // When & Then
                mockMvc.perform(get("/mini/store/detail/1")
                                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(false))
                                .andExpect(jsonPath("$.msg").value("用户未绑定该店铺"));
        }

        /**
         * 绑定用户-成功
         */
        @Test
        @DisplayName("绑定用户-成功")
        void testBindUser_Success() throws Exception {
                // Given
                when(storeService.bindUser(any(StoreBindUserParams.class), eq(1L)))
                                .thenReturn(ResultBean.successfulResult("绑定用户成功"));

                // When & Then
                mockMvc.perform(post("/mini/store/bind")
                                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(mockBindUserParams)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData").value("绑定用户成功"));
        }

        /**
         * 解绑用户-成功
         */
        @Test
        @DisplayName("解绑用户-成功")
        void testUnbindUser_Success() throws Exception {
                // Given
                PageResult<StoreUserPageVo> emptyResult = new PageResult<>();
                emptyResult.setTotal(0L);
                emptyResult.setResult(Collections.emptyList());
                when(storeService.unbindUser(any(StoreBindUserParams.class)))
                                .thenReturn(ResultBean.successfulResultWithMsg("解绑用户成功", emptyResult));

                // When & Then
                mockMvc.perform(post("/mini/store/unbind")
                                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(mockBindUserParams)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.msg").value("解绑用户成功"));
        }

        /**
         * 缺少accessToken
         */
        @Test
        @DisplayName("缺少accessToken")
        void testMissingAccessToken() throws Exception {
                // When & Then
                mockMvc.perform(post("/mini/store/list")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(mockPageParams)))
                                .andExpect(status().isBadRequest());
        }

        // Helper methods
        private StorePageVo createStorePageVo() {
                StorePageVo vo = new StorePageVo();
                vo.setId(1L);
                vo.setStoreName("测试门店");
                vo.setStoreType(String.valueOf(StoreTypeEnum.PARENT.getCode()));
                vo.setMerchantCount(1);
                vo.setDeviceCount(0);
                return vo;
        }

        private StoreUserPageVo createStoreUserPageVo() {
                StoreUserPageVo vo = new StoreUserPageVo();
                vo.setId(1L);
                vo.setUsername("测试用户");
                vo.setPhone("13800138000");
                vo.setRole(StoreUserTypeEnum.USER.getCode());
                return vo;
        }

        private Shop createShop() {
                Shop shop = new Shop();
                shop.setId(1L);
                shop.setShopName("测试门店");
                shop.setParentId(0L);
                shop.setType(StoreTypeEnum.PARENT.getCode());
                return shop;
        }

        private ShopUserVo createShopUserVo() {
                ShopUserVo vo = new ShopUserVo();
                vo.setUserId(1L);
                vo.setNickname("测试用户");
                vo.setPhone("13800138000");
                vo.setShopId(1L);
                return vo;
        }
}
