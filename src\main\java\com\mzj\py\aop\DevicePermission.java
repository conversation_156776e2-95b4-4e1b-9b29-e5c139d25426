package com.mzj.py.aop;

import java.lang.annotation.*;

/**
 * 设备访问控制注解。
 * <p>
 * 如果 operate = true，则代表该接口/方法需要门店管理员权限。
 * operate = false 仅校验是否绑定当前门店/父门店即可。
 * </p>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DevicePermission {

    /**
     * 是否需要管理员权限才能执行。
     */
    boolean operate() default false;

    /**
     * 提取设备 ID 时优先匹配的字段名称。为空时使用默认规则（deviceId、id）。
     */
    String key() default "";
}