//package com.mzj.py.commons;
//
//import java.awt.Color;
//import java.awt.image.BufferedImage;
//import java.io.BufferedInputStream;
//import java.io.BufferedOutputStream;
//import java.io.ByteArrayInputStream;
//import java.io.ByteArrayOutputStream;
//import java.io.File;
//import java.io.FileInputStream;
//import java.io.FileOutputStream;
//import java.io.IOException;
//import java.io.InputStream;
//import java.net.URL;
//import java.util.Calendar;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.zip.Adler32;
//import java.util.zip.CheckedOutputStream;
//import java.util.zip.ZipEntry;
//import java.util.zip.ZipOutputStream;
//
//import javax.imageio.ImageIO;
//import javax.servlet.http.HttpServletResponse;
//
//import org.apache.commons.io.FileUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.commons.lang3.time.DateFormatUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.HttpStatus;
//import org.springframework.stereotype.Service;
//import org.springframework.web.multipart.MultipartFile;
//
//import com.aliyun.oss.ClientException;
//import com.aliyun.oss.OSSClient;
//import com.aliyun.oss.OSSException;
//import com.aliyun.oss.model.OSSObject;
//import com.aliyun.oss.model.ObjectMetadata;
//import com.mzj.py.commons.exception.CustomException;
//
//import net.coobird.thumbnailator.Thumbnails;
//
//@Service
//public class OSSService {
//
//	private static final Logger LOG = LoggerFactory.getLogger(OSSService.class);
//
//	/**
//	 * OSS bucket
//	 */
//	@Value("${files.oss.bucket}")
//	private String bucketName;
//	/**
//	 * OSS 域名
//	 */
//	@Value("${files.oss.endpoint}")
//	private String endpoint;
//	/**
//	 * OSS 过期时间
//	 */
//	@Value("${files.oss.expired}")
//	private Integer expired;
//	/**
//	 * OSS accessKeyId
//	 */
//	@Value("${oss.accessKeyId}")
//	private String accessKeyId;
//	/**
//	 * OSS accessKeySecret
//	 */
//	@Value("${oss.accessKeySecret}")
//	private String accessKeySecret;
//	/**
//	 * 项目环境
//	 */
//	@Value("${spring.profiles.active}")
//	private String profiles;
//	/**
//	 * 图片压缩限制，单位：MB
//	 */
//    @Value("${upload.file.compress.size.limit}")
//    private long uploadFileCompressSizeLimit;
//
//	/**
//	 * 上传MultipartFile文件
//	 *
//	 * @param type  文件类型
//	 * @param orgId 上传人部门ID
//	 * @param mfile 文件
//	 * @return
//	 * @throws Exception
//	 * <AUTHOR>
//	 */
//	public String uploadMfile(OSSUploadFileType type, Long orgId, MultipartFile mfile) throws Exception {
//		StringBuilder fileKeyB = new StringBuilder();
//
//		fileKeyB.append(profiles).append("/").append(type).append("/").append(orgId).append("/")
//				.append(this.getNewName(mfile.getOriginalFilename()));
//
//		String fileKey = fileKeyB.toString();
//
//		OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
//
//		InputStream inputStream = null;
//		ByteArrayOutputStream bos = null;
//        try {
//            ObjectMetadata objectMeta = new ObjectMetadata();
//            objectMeta.setContentType("image/*");// 在metadata中标记文件类型
//            bos = new ByteArrayOutputStream();// 存储图片文件byte数组
//
//            // 如果图片超过限制大小，则压缩后再上传OSS
//            if (mfile.getSize() > uploadFileCompressSizeLimit * 1024 * 1024) {
//                InputStream tempInputStream = mfile.getInputStream();
//                BufferedImage bufferedImage = ImageIO.read(tempInputStream);
//                bufferedImage = Thumbnails.of(mfile.getInputStream()).scale(0.8f).outputQuality(0.6f).asBufferedImage();
//
//                // 防止图片变红
//                BufferedImage newBufferedImage = new BufferedImage(bufferedImage.getWidth(), bufferedImage.getHeight(), BufferedImage.TYPE_INT_RGB);
//                newBufferedImage.createGraphics().drawImage(bufferedImage, 0, 0, Color.WHITE, null);
//
//                ImageIO.write(newBufferedImage, "jpg", bos);
//                inputStream = new ByteArrayInputStream(bos.toByteArray());
//            } else {
//                inputStream = mfile.getInputStream();
//            }
//            objectMeta.setContentLength(inputStream.available());
//            ossClient.putObject(bucketName, fileKey, inputStream, objectMeta);
//
//            return endpoint + "/" + fileKey;
//        } catch (Exception e) {
//            LOG.error(e.getMessage(), e);
//            throw new Exception("上传失败，请重试");
//        } finally {
//            ossClient.shutdown();
//            if (bos != null) {
//                bos.close();
//            }
//            if (inputStream != null) {
//                inputStream.close();
//            }
//        }
//	}
//
////	public String uploadMfile(OSSUploadFileType type, Long orgId, MultipartFile mfile) throws CustomException {
////	    StringBuilder fileKeyB = new StringBuilder();
////
////	    fileKeyB.append(profiles).append("/").append(type).append("/").append(orgId).append("/")
////	    .append(this.getNewName(mfile.getOriginalFilename()));
////
////	    String fileKey = fileKeyB.toString();
////
////	    OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
////
////	    File temp = new File("temp_" + this.getNewName(mfile.getOriginalFilename()));
////
////	    try (FileOutputStream fos = new FileOutputStream(temp);
////	            BufferedOutputStream bos = new BufferedOutputStream(fos)) {
////	        bos.write(mfile.getBytes());
////	        bos.flush();
////
////	        ossClient.putObject(bucketName, fileKey, temp);
////
////	        return fileKey;
////	    } catch (Exception e) {
////	        LOG.error(e.getMessage(), e);
////	        throw new CustomException(HttpStatus.BAD_REQUEST, StatusCode.ERROR_CODE_10004.getErrorCode(), "上传失败，请重试");
////	    } finally {
////	        ossClient.shutdown();
////	        if (temp.exists()) {
////	            FileUtils.deleteQuietly(temp);
////	        }
////	    }
////	}
//
//	/**
//	 * 上传File文件
//	 *
//	 * @param type  文件类型
//	 * @param orgId 上传人部门ID
//	 * @param file  文件
//	 * @return
//	 * @throws CustomException
//	 * <AUTHOR>
//	 */
//	public String uploadFile(String type, Long orgId, File file) throws CustomException {
//		StringBuilder fileKeyB = new StringBuilder();
//
//		fileKeyB.append(profiles).append("/").append(type).append("/").append(orgId).append("/")
//				.append(this.getNewName(file.getName()));
//
//		String fileKey = fileKeyB.toString();
//
//		OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
//		try {
//			ossClient.putObject(bucketName, fileKey, file);
//			return endpoint + "/" + fileKey;
//		} catch (Exception e) {
//			LOG.error(e.getMessage(), e);
//			throw new CustomException(HttpStatus.BAD_REQUEST, StatusCode.ERROR_CODE_10004.getErrorCode(), "上传失败，请重试");
//		} finally {
//			ossClient.shutdown();
//		}
//	}
//
//	/**
//	 * 上传二维码图片
//	 *
//	 * @param type     文件类型
//	 * @param orgId    上传人部门ID
//	 * @param byteFile byte[]文件
//	 * @return
//	 * @throws Exception
//	 * <AUTHOR>
//	 */
//	public String uploadQr(OSSUploadFileType type, Long orgId, byte[] byteFile) throws Exception {
//		String fileName = "qrcode.jpg";
//
//		StringBuilder fileKeyB = new StringBuilder();
//		fileKeyB.append(profiles).append("/").append(type).append("/").append(orgId).append("/")
//				.append(this.getNewName(fileName));
//
//		String fileKey = fileKeyB.toString();
//
//		OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
//		File temp = new File("temp_" + this.getNewName(fileName));
//
//		try (FileOutputStream fos = new FileOutputStream(temp);
//				BufferedOutputStream bos = new BufferedOutputStream(fos)) {
//			bos.write(byteFile);
//			bos.flush();
//
//			ossClient.putObject(bucketName, fileKey, temp);
//
//			return endpoint + "/" + fileKey;
//		} catch (Exception e) {
//			LOG.error(e.getMessage(), e);
//			throw new Exception("上传失败，请重试");
//		} finally {
//			ossClient.shutdown();
//			if (temp.exists()) {
//				FileUtils.deleteQuietly(temp);
//			}
//		}
//	}
//
//	/**
//	 * 获取文件完整路径
//	 * @param fileKeys
//	 * @return
//	 */
//	public Map<String, String> getFileUrls(List<String> fileKeys) {
//		Map<String, String> map = new HashMap<>(fileKeys.size());
//
//		Calendar cal = Calendar.getInstance();
//		cal.add(Calendar.DAY_OF_MONTH, expired);
//		OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
//		try {
//			for (String fileKey : fileKeys) {
//				if (StringUtils.isBlank(fileKey)) {
//					continue;
//				}
//				URL ossUrl = ossClient.generatePresignedUrl(bucketName, fileKey, cal.getTime());
//				map.put(fileKey, ossUrl.toString());
//			}
//		} catch (Exception e) {
//			LOG.error(e.getMessage(), e);
//		} finally {
//			ossClient.shutdown();
//		}
//
//		return map;
//	}
//
//	/**
//	 * 删除指定文件
//	 *
//	 * @param fileKey 文件相对路径
//	 * @return
//	 * @throws CustomException
//	 * <AUTHOR>
//	 */
//	public void deleteFile(String fileKey) throws CustomException {
//		OSSClient ossClient = null;
//		try {
//			ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
//			ossClient.calculatePostSignature(accessKeyId);
//
//			ossClient.deleteObject(bucketName, fileKey);
//		} catch (OSSException e) {
//			LOG.error(e.getMessage(), e);
//		} catch (ClientException e) {
//			LOG.error(e.getMessage(), e);
//		} finally {
//			if (ossClient != null) {
//				ossClient.shutdown();
//			}
//		}
//	}
//
//	/**
//	 * 批量下载文件,打成zip压缩包
//	 *
//	 * @param fileKeyList
//	 * @param response
//	 * @throws CustomException
//	 * @throws IOException
//	 * <AUTHOR>
//	 */
//	public void downloadZip(List<String> fileKeyList, HttpServletResponse response) throws CustomException, IOException {
//		OSSClient ossClient = null;
//		FileInputStream fis = null;
//		BufferedInputStream buff = null;
//		BufferedOutputStream out = null;
//		File zipFile = null;
//		try {
//			// 初始化
//			ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
//
//			// 压缩包名称
//			String fileName = "qrcode.zip";
//			// 创建临时文件
//			zipFile = File.createTempFile("qrcode", ".zip");
//			FileOutputStream f = new FileOutputStream(zipFile);
//
//			/**
//			 * 作用是为任何OutputStream产生校验和 第一个参数是制定产生校验和的输出流，第二个参数是指定Checksum的类型
//			 * （Adler32（较快）和CRC32两种）
//			 */
//			CheckedOutputStream csum = new CheckedOutputStream(f, new Adler32());
//			// 用于将数据压缩成Zip文件格式
//			ZipOutputStream zos = new ZipOutputStream(csum);
//
//			for (String ossfile : fileKeyList) {
//				// 获取Object，返回结果为OSSObject对象
//				OSSObject ossObject = ossClient.getObject(bucketName, ossfile);
//				// 读取Object内容返回
//				InputStream inputStream = ossObject.getObjectContent();
//				// 对于每一个要被存放到压缩包的文件，都必须调用ZipOutputStream对象的putNextEntry()方法，确保压缩包里面文件不同名
//				zos.putNextEntry(new ZipEntry(ossfile.substring(ossfile.lastIndexOf("/") + 1)));
//				int bytesRead = 0;
//				// 向压缩文件中输出数据
//				while ((bytesRead = inputStream.read()) != -1) {
//					zos.write(bytesRead);
//				}
//				inputStream.close();
//				zos.closeEntry(); // 当前文件写完，定位为写入下一条项目
//			}
//			zos.close();
//
//			response.reset();
//			response.setContentType("text/plain");
//			response.setContentType("application/octet-stream; charset=utf-8");
//			response.setHeader("Location", fileName);
//			response.setHeader("Cache-Control", "max-age=0");
//			response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
//
//			fis = new FileInputStream(zipFile);
//			buff = new BufferedInputStream(fis);
//			out = new BufferedOutputStream(response.getOutputStream());
//			byte[] car = new byte[1024];
//			int l = 0;
//			while (l < zipFile.length()) {
//				int j = buff.read(car, 0, 1024);
//				l += j;
//				out.write(car, 0, j);
//			}
//		} catch (Exception e) {
//			LOG.error(e.getMessage(), e);
//			throw new CustomException(HttpStatus.BAD_REQUEST, StatusCode.ERROR_CODE_10004.getErrorCode(), "下载失败，请重试");
//		} finally {
//			// 关闭流
//			if (ossClient != null) {
//				ossClient.shutdown();
//			}
//			if (fis != null) {
//				fis.close();
//			}
//			if (buff != null) {
//				buff.close();
//			}
//			if (out != null) {
//				out.close();
//			}
//
//			// 删除临时文件
//			if (zipFile != null && zipFile.exists()) {
//				zipFile.delete();
//			}
//		}
//	}
//
//	/**
//	 * 以当前日期+毫秒值重命名
//	 *
//	 * @param filename
//	 * @return
//	 * <AUTHOR>
//	 */
//	private String getNewName(String filename) {
//		int indx = filename.lastIndexOf(".");
//		String suffix = filename.substring(indx, filename.length());
//		return (getDate() + System.currentTimeMillis() + suffix);
//	}
//
//	/**
//	 * 获取当前日期(yyyyMMdd)
//	 *
//	 * @return
//	 * <AUTHOR>
//	 */
//	private String getDate() {
//		return DateFormatUtils.format(new Date(), "yyyyMMdd");
//	}
//
//}
