package com.mzj.py.config.sms;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.teaopenapi.models.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SmsConfig {
	/**
	 * SMS配置
	 */
	@Value("${ali.sms.endpoint}")
	private String endpoint;
	@Value("${ali.sms.accessKeyId}")
	private String accessKeyId;
	@Value("${ali.sms.accessKeySecret}")
	private String accessKeySecret;

	/**
	 * 登录短信模板
	 */
	@Value("${ali.sms.login.template.code}")
	private String loginTemplateCode;

	/**
	 * 签名名称
	 */
	@Value("${ali.sms.sign.name}")
	private String signName;

	/**
	 * 使用AK&SK初始化账号Client
	 * 
	 * @param accessKeyId
	 * @param accessKeySecret
	 * @return Client
	 * @throws Exception
	 */
	@Bean
	public Client createClient() throws Exception {
		Config config = new Config()
				// 您的AccessKey ID
				.setAccessKeyId(accessKeyId)
				// 您的AccessKey Secret
				.setAccessKeySecret(accessKeySecret);
		// 访问的域名
		config.endpoint = endpoint;
		return new Client(config);
	}

	public String getLoginTemplateCode() {
		return loginTemplateCode;
	}

	public void setLoginTemplateCode(String loginTemplateCode) {
		this.loginTemplateCode = loginTemplateCode;
	}

	public String getSignName() {
		return signName;
	}

	public void setSignName(String signName) {
		this.signName = signName;
	}

}