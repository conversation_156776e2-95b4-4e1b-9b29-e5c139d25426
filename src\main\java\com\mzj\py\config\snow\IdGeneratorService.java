package com.mzj.py.config.snow;

import cn.hutool.core.lang.Snowflake;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class IdGeneratorService {

    private final Snowflake snowflake;

    // 通过构造函数注入 Snowflake Bean
    @Autowired
    public IdGeneratorService(Snowflake snowflake) {
        this.snowflake = snowflake;
    }

    /**
     * 使用雪花算法生成一个带 "2-" 前缀的全局唯一ID字符串。
     * 这个方法是线程安全的，并且性能极高。
     *
     * @return 格式为 "2-唯一ID" 的字符串
     */
    public String getTimestamp() {
        // 1. 调用雪花算法生成一个唯一的 long 类型 ID
        long uniqueId = snowflake.nextId();
        
        // 2. 拼接上前缀 "2-" 并返回字符串
        return "1-" + uniqueId;
    }
}