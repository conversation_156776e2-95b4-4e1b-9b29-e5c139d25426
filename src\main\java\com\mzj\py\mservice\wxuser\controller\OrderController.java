/*
package com.mzj.cc.mservice.wxuser.controller;

import com.mzj.cc.commons.util.ApiData;
import com.mzj.cc.commons.util.ResultBean;
import com.mzj.cc.mservice.wxuser.service.OrderService;
import com.mzj.cc.mservice.wxuser.vo.OrderPayInfoReqVo;
import com.mzj.cc.mservice.wxuser.vo.OrderReqVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

*/
/**
 * 订单对外接口
 * 
 * <AUTHOR>
 * @Date 2019-12-16
 *
 *//*

@Controller
@RequestMapping("/order")
public class OrderController {
	@Autowired
	private OrderService OrderService;	


	*/
/**
	 * 查询用户未支付的订单
	 * @param userId
	 * @param deviceId
	 * @return
	 *//*

	@GetMapping(value = "/userNoPayOrder")
	@ResponseBody
	public ResultBean<Map<String, Object>> userNoPayOrder(@RequestParam(name = "userId", required = false) Long userId,
			                                              @RequestParam(name = "deviceId", required = false) Long deviceId
			                                             ) {
	         return OrderService.userNoPayOrder(userId,deviceId);
	}
	
	*/
/**
	 * 创建订单
	 * @param vo
	 * @return
	 *//*

	@PostMapping(value = "/createOrder")
	@ResponseBody
	public ResultBean<Object> createOrder(@RequestBody OrderReqVo vo) {
	     return OrderService.createOrder(vo);
		
	}
	
	*/
/**
	 * 统一下单
	 * @param vo
	 * @return
	 *//*

	@PostMapping(value = "/unifiedorder")
	@ResponseBody
	public ResultBean<Map<Object, Object>> unifiedorder(@RequestBody OrderPayInfoReqVo vo) {
	       return OrderService.unifiedorder(vo);
	}
	
	*/
/**
	 * 用户车辆订单分页
	 * @param userId
	 * @param pageSize
	 * @param pageNumber
	 * @return
	 *//*

	@GetMapping(value = "/userOrder")
	@ResponseBody
	ResultBean<ApiData<Map<String, Object>>>  userOrder(@RequestParam(value = "userId",required = false) Long userId,
			                                            @RequestParam(value = "pageSize",required = false) Integer pageSize,
			                                            @RequestParam(value = "pageNumber",required = false) Integer pageNumber){
		return OrderService.userOrder(userId, pageSize, pageNumber);
	}

}
*/
