package com.mzj.py.mservice.order.controller;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.enums.ProductOrderPayTypeEnum;
import com.mzj.py.mservice.common.ApiBaseController;
import com.mzj.py.mservice.order.service.OrderService;
import com.mzj.py.mservice.order.utils.PayUtils;
import com.mzj.py.mservice.order.vo.dto.OrderPay;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.service.payments.model.Transaction;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * 订单管理
 */
@Log4j2
@Controller
@RequestMapping("/mini/order")
public class OrderController  extends ApiBaseController {

    @Autowired
    private OrderService orderService;

    @Autowired
    private NotificationParser wechatNotifyParser;
    /**
     * 订单列表
     * @param accessToken
     * @param pageSize
     * @param pageNumber
     * @return
     */
    @GetMapping
    @ResponseBody
    public ResultBean list(@RequestHeader String accessToken,
                           @RequestParam(name = "status",defaultValue = "-1") Integer status,
                           @RequestParam(name = "pageSize",defaultValue = "10") Integer pageSize,
                           @RequestParam(name = "pageNumber",defaultValue = "0") Integer pageNumber
    ) {

        return orderService.list(accessToken, pageSize, pageNumber,status);
    }


    /**
     * 创建订单
     * @param accessToken
     * @param vo
     * @return
     */
    @PostMapping("/createOrder")
    @ResponseBody
    public ResultBean createOrder(@RequestHeader String accessToken, @RequestBody OrderPay vo){
        vo.setShopId(getShopId(accessToken));
        return orderService.createOrder(accessToken,vo);
    }
    /**
     * 取消订单
     * @param accessToken
     * @param outTradeNo
     * @return
     */
    @RequestMapping("cancelOrder")
    @ResponseBody
    public ResultBean<Map<String, Object>> cancelOrder(@RequestHeader String accessToken,@RequestParam("outTradeNo") String outTradeNo){
        return orderService.cancelOrder(super.getUser(accessToken).getOpenid(),outTradeNo);
    }

    /**
     * 微信支付回调
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("/callback")
    public ResultBean<Map<String, Object>>wechatPayCallback(HttpServletRequest request, HttpServletResponse response) {
        String wechatSignature=request.getHeader("Wechatpay-Signature");
        String wechatPaySerial=request.getHeader("Wechatpay-Serial");
        String wechatpayNonce=request.getHeader("Wechatpay-Nonce");
        String wechatTimestamp=request.getHeader("Wechatpay-Timestamp");
        ResultBean<Map<String, Object>> resultBean = new ResultBean<>();
        Map<String, Object> map = new HashMap<>(2);
        try {
            com.wechat.pay.java.core.notification.RequestParam requestParam = new com.wechat.pay.java.core.notification.RequestParam.Builder()
                    .serialNumber(wechatPaySerial)
                    .nonce(wechatpayNonce)
                    .signature(wechatSignature)
                    .timestamp(wechatTimestamp)
                    .body(PayUtils.getRequestBody(request))
                    .build();
            Transaction transaction = wechatNotifyParser.parse(requestParam, Transaction.class);
            Map<String, Object> paramsMap = PayUtils.convertWechatPayMsgToMap(transaction);
            orderService.processOrderCallbackMsg(ProductOrderPayTypeEnum.WECHAT_PAY,paramsMap);
            //响应微信
            map.put("code", "SUCCESS");
            map.put("message", "成功");
            resultBean.setResultData(map);
            return resultBean;
        } catch (Exception e) {
            log.error("微信支付回调异常:{}", e);
            throw new RuntimeException(e);
        }
    }

//    /**
//     * 小程序支付回调地址
//     * @param body
//     * @param request
//     */
//    @PostMapping("/callback")
//    public void orderPayCallback(@RequestBody Map body, HttpServletRequest request) {
//        log.info("1----------->微信支付回调开始");
//        Map<String, Object> result = new HashMap();
//        //1：获取微信支付回调的获取签名信息
//        String timestamp = request.getHeader("Wechatpay-Timestamp");
//        String nonce = request.getHeader("Wechatpay-Nonce");
//        ObjectMapper objectMapper = new ObjectMapper();
//        try {
//            // 2: 开始解析报文体
//            String data = objectMapper.writeValueAsString(body);
//            String message = timestamp + "\n" + nonce + "\n" + data + "\n";
//            //3：获取应答签名
//            String sign = request.getHeader("Wechatpay-Signature");
//            //4：获取平台对应的证书
//            String serialNo = request.getHeader("Wechatpay-Serial");
//            if (!WxpayConfig.certificateMap.containsKey(serialNo)) {
//                WxpayConfig.certificateMap = WechatPayUtils.refreshCertificate();
//            }
//            X509Certificate x509Certificate = WxpayConfig.certificateMap.get(serialNo);
//            if (!WechatPayUtils.verify(x509Certificate, message.getBytes(), sign)) {
//                throw new IllegalArgumentException("微信支付签名验证失败:" + message);
//            }
//            log.info("签名验证成功");
//            Map<String, String> resource = (Map) body.get("resource");
//            // 5：回调报文解密
//            EncryptUtils encryptUtils = new EncryptUtils(WxpayConfig.v3Key.getBytes());
//            //解密后json字符串
//            String decryptToString = encryptUtils.decryptToString(
//                    resource.get("associated_data").getBytes(),
//                    resource.get("nonce").getBytes(),
//                    resource.get("ciphertext"));
//            log.info("2------------->decryptToString====>{}", decryptToString);
//
//            //6：获取微信支付返回的信息
//            Map<String, Object> jsonData = objectMapper.readValue(decryptToString, Map.class);
//            //7: 支付状态的判断 如果是success就代表支付成功
//            if ("SUCCESS".equals(jsonData.get("trade_state"))) {
//                // 8：获取支付的交易单号，流水号，和附属参数
//                String out_trade_no = jsonData.get("out_trade_no").toString();
//                String transaction_id = jsonData.get("transaction_id").toString();//微信支付侧订单的唯一标识
//                String success_time = jsonData.get("success_time").toString();// 支付完成时间
//               // String attach = jsonData.get("attach").toString();//【商户数据包】商户下单时传入的自定义数据包，用户不可见，长度不超过128字符，若下单传入该参数，则订单支付成功后此接口和查询订单接口以及交易账单中会原样返回；若下单未传该参数，则不会返回。
//                //TODO 根据订单号查询支付状态，如果未支付，更新支付状态 为已支付
//                //   log.info("3----------->微信支付成功,支付流水号是：{},附属参数是：{}", out_trade_no, attach);
//                //   log.info("4----------->微信支付成功,支付流水号是：{}", transaction_id);
//                // 转换附属参数
//               // HashMap<String, Object> map = JsonUtils.string2Obj(attach, HashMap.class);
//                // 9：保存用户支付信息
//                orderService.orderCallback(out_trade_no,transaction_id,success_time);
//            }
//            result.put("code", "SUCCESS");
//            result.put("message", "成功");
//        } catch (Exception e) {
//            result.put("code", "fail");
//            result.put("message", "系统错误");
//            e.printStackTrace();
//        }
//    }






}
