package com.mzj.py.aop;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.commons.enums.StoreUserTypeEnum;
import com.mzj.py.commons.exception.CustomException;
import com.mzj.py.mservice.common.ApiBaseController;
import com.mzj.py.mservice.home.repository.DeviceRepository;
import com.mzj.py.mservice.home.entity.Device;
import com.mzj.py.mservice.redis.RedisService;
import com.mzj.py.mservice.shop.entity.Shop;
import com.mzj.py.mservice.shop.entity.ShopUserRef;
import com.mzj.py.mservice.shop.repository.ShopRepository;
import com.mzj.py.mservice.shop.repository.ShopUserRefRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

/**
 * 基于 {@link DevicePermission} 的设备访问控制切面。
 * 该切面对比现有 {@link StorePermissionAspect}，
 * 先根据方法参数提取设备 id，再解析对应门店 id，进而复用门店权限校验逻辑。
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class DevicePermissionAspect extends ApiBaseController {

    @Resource
    private RedisService redisService;
    @Resource
    private DeviceRepository deviceRepository;
    @Resource
    private ShopUserRefRepository shopUserRefRepository;
    @Resource
    private ShopRepository shopRepository;

    @Pointcut("@annotation(DevicePermission)")
    public void permissionPointCut() {
    }

    @Around("permissionPointCut() && @annotation(devicePermission)")
    public Object around(ProceedingJoinPoint joinPoint, DevicePermission devicePermission) throws Throwable {
        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attrs == null) {
            return joinPoint.proceed();
        }
        HttpServletRequest request = attrs.getRequest();
        String accessToken = request.getHeader("accessToken");
        if (StringUtils.isBlank(accessToken)) {
            log.warn("accessToken missing in request, uri={}", request.getRequestURI());
            return ResultBean.failedResultOfToken();
        }
        TokenRedisVo userVo = redisService.findTokenVo(accessToken);
        if (userVo == null) {
            log.warn("token invalid or expired, token={}", accessToken);
            return ResultBean.failedResultOfToken();
        }
        Long userId = userVo.getId();
        List<Long> allowShopIds = getShopIds(accessToken);
        // 提取 deviceId，只依据设备进行权限判断
        Set<Long> deviceIds = new HashSet<>();
        MethodSignature methodSig = (MethodSignature) joinPoint.getSignature();
        String[] paramNames = methodSig.getParameterNames();
        Object[] args = joinPoint.getArgs();
        String idParam = devicePermission.key();
        for (int i = 0; i < args.length; i++) {
            Object arg = args[i];
            if (arg == null) {
                continue;
            }

            String paramName = (paramNames != null && i < paramNames.length) ? paramNames[i] : "";

            boolean paramExplicitMatch = StringUtils.isNotBlank(idParam) && idParam.equals(paramName);

            // 如果参数名直接匹配
            if (paramExplicitMatch) {
                // 直接是 Long 主键
                if (arg instanceof Long) {
                    deviceIds.add((Long) arg);
                    continue;
                }

                // 可能是 List<Long>
                if (arg instanceof Collection) {
                    for (Object o : (Collection<?>) arg) {
                        if (o instanceof Long) {
                            deviceIds.add((Long) o);
                        }
                    }
                    continue;
                }
            }

            // 如果参数名不匹配，但指定了idParam，则尝试从对象的字段中提取
            if (StringUtils.isNotBlank(idParam) && !paramExplicitMatch) {
                Object fieldValue = extractFieldValue(arg, idParam);
                if (fieldValue != null) {
                    if (fieldValue instanceof Long) {
                        deviceIds.add((Long) fieldValue);
                        continue;
                    }

                    if (fieldValue instanceof Collection) {
                        for (Object o : (Collection<?>) fieldValue) {
                            if (o instanceof Long) {
                                deviceIds.add((Long) o);
                            }
                        }
                        continue;
                    }
                }
            }
        }
        if (deviceIds.isEmpty()) {
            return joinPoint.proceed(); // 无主键可校验，直接放行
        }
        for (Long deviceId : deviceIds) {
            Object result = checkDeviceAccess(joinPoint, deviceId, userId, allowShopIds);
            if (result instanceof ResultBean) {
                return result;
            }
        }
        // 微信端用户（type = 2）使用 siteId 作为用户标识，通过 ShopUserRef 判断其在允许门店中的角色。
        boolean isAdmin = false;
        if (userId != null) {
            isAdmin = shopUserRefRepository.existsByUserIdAndRoleAndShopIdIn(
                    userId,
                    StoreUserTypeEnum.ADMIN.getCode(),
                    allowShopIds);
        }
        // 非管理员做写操作直接拒绝
        if (devicePermission.operate() && !isAdmin) {
            throw new CustomException("非门店管理员，无权执行该操作");
        }
        return joinPoint.proceed();
    }

    public Object checkDeviceAccess(ProceedingJoinPoint joinPoint, Long deviceId, Long userId, List<Long> allowShopIds) throws Throwable {
        Device device = deviceRepository.findById(deviceId).orElse(null);
        if (device == null) {
            return ResultBean.failedResultWithMsg("设备不存在");
        }

        Long storeId = device.getShopId();
        if (storeId == null) {
            // 设备未绑定门店，仅创建者可访问
            if (!Objects.equals(device.getCreateId(), userId)) {
                return ResultBean.failedResultWithMsg("权限不足，无法操作他人设备");
            }
            return joinPoint.proceed();
        }
        if (!hasStoreAccess(allowShopIds, storeId)) {
            return ResultBean.failedResultWithMsg("用户未绑定该门店或其下属门店");
        }
        return null;
    }

    /**
     * 判断用户是否绑定了 storeId 店铺或其父店铺。
     */
    private boolean hasStoreAccess(List<Long> allowShopIds, Long storeId) {
        // 若访问的是分店且用户绑定的是总店
        return allowShopIds.contains(storeId);
    }


    /**
     * 从对象中提取指定字段名的值
     *
     * @param obj       目标对象
     * @param fieldName 字段名
     * @return 字段值，如果提取失败返回null
     */
    private Object extractFieldValue(Object obj, String fieldName) {
        if (obj == null || StringUtils.isBlank(fieldName)) {
            return null;
        }

        try {
            Class<?> clazz = obj.getClass();

            // 首先尝试通过getter方法获取
            String getterName = "get" + StringUtils.capitalize(fieldName);
            try {
                Method getter = clazz.getMethod(getterName);
                return getter.invoke(obj);
            } catch (Exception e) {
                // getter方法不存在，继续尝试字段直接访问
            }

            // 尝试直接访问字段
            try {
                Field field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);
                return field.get(obj);
            } catch (Exception e) {
                // 字段不存在，尝试父类
                Class<?> superClass = clazz.getSuperclass();
                while (superClass != null && superClass != Object.class) {
                    try {
                        Field field = superClass.getDeclaredField(fieldName);
                        field.setAccessible(true);
                        return field.get(obj);
                    } catch (Exception ex) {
                        superClass = superClass.getSuperclass();
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取类名的字段失败: fieldName={}, objectClass={}, error={}",
                    fieldName, obj.getClass().getSimpleName(), e.getMessage());
        }
        return null;
    }
}