package com.mzj.py.mservice.home.repository;

import com.mzj.py.mservice.home.entity.VoiceWork;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface VoiceWorkRepository extends JpaRepository<VoiceWork,Long>, JpaSpecificationExecutor<VoiceWork> {
    @Transactional
    @Modifying
    @Query(value = "update dub_voice_work set del_status = 1 where id = ?1 and shop_id in (?2) or user_id = ?3",nativeQuery = true)
    void editById(Long id,List<Long> shopIds,Long userId);


    List<VoiceWork> findByShopId(Long shopId);
}
