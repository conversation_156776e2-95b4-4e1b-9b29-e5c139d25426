package com.mzj.py.mservice.deviceOperationLog.service;

import com.mzj.py.mservice.deviceOperationLog.entity.DeviceOperationLog;
import com.mzj.py.mservice.deviceOperationLog.repository.DeviceOperationLogRepository;
import com.mzj.py.mservice.home.entity.Device;
import com.mzj.py.mservice.home.repository.DeviceRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import com.mzj.py.commons.RedisKeyConstant;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date: 2025/4/21
 * @description:
 */
@Slf4j
@Scope("prototype")
@Service
public class DeviceOperationLogService {

    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate redisTemplate;
    @Autowired
    private DeviceOperationLogRepository deviceOperationLogRepository;
    @Autowired
    private DeviceRepository deviceRepository;

    public Long sendSave(Long userId, Long deviceId, String deviceSn, String content) {
        Device device = deviceRepository.findBySn(deviceSn);
        if (device != null) {
            DeviceOperationLog deviceOperationLog = new DeviceOperationLog();
            deviceOperationLog.setUserId(userId);
            deviceOperationLog.setSendTime(new Date());
            deviceOperationLog.setDeviceId(device.getId());
            deviceOperationLog.setShopId(device.getShopId());
            deviceOperationLog.setContent(content);
            deviceOperationLog.setStatus(4);
            deviceOperationLogRepository.save(deviceOperationLog);

            Long id = deviceOperationLog.getId();
            redisTemplate.opsForValue().set(String.format(RedisKeyConstant.DEVICE_OP_LOG_ID, id), String.valueOf(id),
                    10, TimeUnit.SECONDS);
            return id;
        }

        return null;
    }

    public void update(Long id, String responseContent) {

        Optional<DeviceOperationLog> operationLog = deviceOperationLogRepository.findById(id);

        if (!operationLog.isPresent()) {
            log.error("没有找到存储的操作数据 id:{},responseContent:{}", id, responseContent);
            return;
        }

        DeviceOperationLog entity = operationLog.get();
        entity.setResponseContent(responseContent);
        entity.setResponseTime(new Date());
        entity.setStatus(1);
        deviceOperationLogRepository.save(entity);
    }

    public String getResponse(Long id) {

        int index = 0;
        while (true) {
            Object obj = redisTemplate.opsForValue().get(String.format(RedisKeyConstant.DEVICE_OP_LOG_ID, id));
            if (obj == null) {
                break;
            }
            if (index >= 10) {
                break;
            }

            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }
            index++;
        }

        Optional<DeviceOperationLog> operationLog = deviceOperationLogRepository.findById(id);

        if (!operationLog.isPresent()) {
            log.error("没有找到存储的操作数据 id:{},responseContent:{}", id);
            return null;
        }

        DeviceOperationLog entity = operationLog.get();
        return entity.getResponseContent();
    }
}
