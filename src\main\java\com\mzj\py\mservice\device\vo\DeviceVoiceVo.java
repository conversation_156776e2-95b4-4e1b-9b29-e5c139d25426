package com.mzj.py.mservice.device.vo;

import lombok.Data;

/**
 * 设备与语音包表
 * @author: duanjinze
 * @date: 2022/11/10 13:40
 * @version: 1.0
 */
@Data
public class DeviceVoiceVo {
    private Long id;

    /**
     * 类型 1作品 2设备自带 3上传
     */
    private Integer type;

    /**
     *设备id
     */
    private Long deviceId;

    /**
     *原作品id
     */
    private Long voiceWorkId;

    /**
     *设备音频id
     */
    private Long deviceVoiceId;

    /**
     * 标题
     */
    private String title;

    /**
     * 作品文字
     */
    private String content;

    /**
     * 语音包url
     */
    private String voiceUrl;

    /**
     *发音id
     */
    private Long voiceId;

    /**
     * 作品时长(秒)
     */
    private Integer voiceTime;

    /**
     * 语速
     */
    private Integer speed;

    /**
     * 语调
     */
    private Integer volume;


    /**
     * 音量
     */
    private Integer pitch;

    /**
     * 背景音乐音量
     */
    private Integer backgroundMusicVolume;

    /**
     * 背景音乐id
     */
    private Long backgroundMusicId;

    /**
     * 音频采样率
     */
    private Integer sampleRate;

    /**
     * 顺序
     */
    private Integer sortby;

    /**
     * 删除状态 0未删除 1已删除
     */
    private Integer delStatus;

}
