package com.mzj.py.mservice.order.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class PayInfoVO {
    private String outTradeNo;

    private String transactionId;

    /**
     * 订单总金额 单位是分
     */
    private BigDecimal payAmount;

    /**
     *支付类型，微信、支付宝
     */
    private Integer payType;


    /**
     * 端类型，App/h5/pc
     */
    private Integer clientType;


    /**
     * 标题
     */
    private String title;

    /**
     * 详情
     */
    private String description;

    /**
     * 订单支付超时，毫秒
     */
    private Long orderPayTimeoutMills;


    private String openid;


    private Long userId;

    private String unionid;
}
