package com.mzj.py.mservice.work.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class WorkVo {

    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 文件地址
     */
    private String fileUrl;


    /**
     * 时长
     */
    private Integer voiceTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     *音量
     */
    private Integer pitch;

    /**
     *背景音乐名称
     */
    private String musicName;

    private String anchorName;


    /**
     * 作品文字
     */
    private String content;

    /**
     * 语音包id
     */
    private Long voiceId;

    /**
     * 主播id
     */
    private Long anchorId;


    /**
     * 语速
     */
    private Integer speed;

    /**
     * 语调
     */
    private Integer volume;




    /**
     * 背景音乐音量
     */
    private Integer backgroundMusicVolume;

    /**
     * 背景音乐id
     */
    private Long backgroundMusicId;

    /**
     * 音频采样率
     */
    private Integer sampleRate;

    /**
     * 所属用户id
     */
    private Long userId;

    private Long shopId;

}
