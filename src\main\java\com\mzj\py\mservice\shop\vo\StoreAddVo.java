package com.mzj.py.mservice.shop.vo;

import lombok.Data;

/**
 * 新增
 * <AUTHOR>
 * @date: 2025/3/20
 * @description:
 * add
 * {"storeName":"","storeType":"","address":"","parentId":0}
 * update {"id":0,"storeName":"","address":"","contactPerson":"","contactPhone":""}
 */
@Data
public class StoreAddVo {

    // 店铺id
    private Long id;

    // 店铺名称
    private String storeName;

    // 店铺类型
    private Integer storeType = 1;

    // 店铺地址
    private String address;

    // 父级店铺id
    private Long parentId = 0L;

    private Long cUserId;

}
