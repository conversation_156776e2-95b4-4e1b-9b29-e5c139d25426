package com.mzj.py.mservice.installationPackage.entity;


import javax.persistence.*;
import java.util.Date;


@Entity
@Table(name = "dub_installation_package_log")
public class InstallationPackageLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Basic
    @Column(name = "device_id")
    private Long deviceId;

    @Basic
    @Column(name = "package_id")
    private Long packageId;

    @Basic
    @Column(name = "create_time")
    private Date createTime;

    @Basic
    @Column(name = "response_time")
    private Date responseTime;

    @Basic
    @Column(name = "status")
    private Integer status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public Long getPackageId() {
        return packageId;
    }

    public void setPackageId(Long packageId) {
        this.packageId = packageId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(Date responseTime) {
        this.responseTime = responseTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
