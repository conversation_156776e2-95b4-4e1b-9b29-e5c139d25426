package com.mzj.py.listener;

import com.mzj.py.commons.model.EventMessage;
import com.mzj.py.mservice.paidPackages.service.PaidPackagesService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 小滴课堂,愿景：让技术不再难学
 *
 * @Description
 * <AUTHOR>
 * @Remark 有问题直接联系我，源码-笔记-技术交流群
 * @Version 1.0
 **/

@Component
@RabbitListener(queuesToDeclare = {
        @Queue("order.vip.queue"),
        @Queue("vip.free_init.queue")
})
@Slf4j
public class VipMQListener {

    @Autowired
    private PaidPackagesService paidPackagesService;


    @RabbitHandler
    public void trafficHandler(EventMessage eventMessage, Message message, Channel channel){

        log.info("监听到消息trafficHandler:{}",eventMessage);

        try{

            paidPackagesService.handleVipMessage(eventMessage);

        }catch (Exception e){
            log.error("消费者失败:{}",eventMessage);
            throw new RuntimeException(e);
        }

        log.info("消费成功:{}",eventMessage);

    }

}
