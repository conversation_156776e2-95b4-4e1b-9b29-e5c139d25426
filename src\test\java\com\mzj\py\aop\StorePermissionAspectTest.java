package com.mzj.py.aop;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.commons.enums.StoreUserTypeEnum;
import com.mzj.py.mservice.redis.RedisService;
import com.mzj.py.mservice.shop.entity.Shop;
import com.mzj.py.mservice.shop.entity.ShopUserRef;
import com.mzj.py.mservice.shop.repository.ShopRepository;
import com.mzj.py.mservice.shop.repository.ShopUserRefRepository;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * StorePermissionAspect 测试类
 * 测试从对象字段中提取店铺ID的功能
 */
@ExtendWith(MockitoExtension.class)
class StorePermissionAspectTest {

    @Mock
    private RedisService redisService;

    @Mock
    private ShopUserRefRepository shopUserRefRepository;

    @Mock
    private ShopRepository shopRepository;

    @Mock
    private ProceedingJoinPoint joinPoint;

    @Mock
    private MethodSignature methodSignature;

    @Mock
    private ServletRequestAttributes servletRequestAttributes;

    @Mock
    private HttpServletRequest httpServletRequest;

    @Mock
    private StorePermission storePermission;

    @InjectMocks
    private StorePermissionAspect storePermissionAspect;

    private TokenRedisVo tokenRedisVo;
    private ShopUserRef shopUserRef;

    @BeforeEach
    void setUp() {
        // 设置基础的mock数据
        tokenRedisVo = new TokenRedisVo();
        tokenRedisVo.setId(1L);

        shopUserRef = new ShopUserRef();
        shopUserRef.setUserId(1L);
        shopUserRef.setShopId(100L);
        shopUserRef.setRole(StoreUserTypeEnum.ADMIN.getCode());

        // 设置RequestContextHolder
        RequestContextHolder.setRequestAttributes(servletRequestAttributes);
        lenient().when(servletRequestAttributes.getRequest()).thenReturn(httpServletRequest);
        lenient().when(httpServletRequest.getHeader("accessToken")).thenReturn("test-token");
        lenient().when(httpServletRequest.getRequestURI()).thenReturn("/test");

        // 设置Redis和用户验证
        lenient().when(redisService.findTokenVo("test-token")).thenReturn(tokenRedisVo);

        // 设置方法签名
        lenient().when(joinPoint.getSignature()).thenReturn(methodSignature);
        lenient().when(storePermission.operate()).thenReturn(false);
    }

    /**
     * 测试从简单对象中提取字段值的功能
     */
    @Test
    @DisplayName("应该能够从对象的字段中提取店铺ID")
    void 应该能够从对象的字段中提取店铺ID() throws Throwable {
        // 创建测试DTO对象
        TestRequestDto requestDto = new TestRequestDto();
        requestDto.setShopId(100L);
        requestDto.setName("测试店铺");

        // 设置方法参数
        String[] paramNames = { "requestDto" };
        Object[] args = { requestDto };
        when(methodSignature.getParameterNames()).thenReturn(paramNames);
        when(joinPoint.getArgs()).thenReturn(args);
        when(storePermission.key()).thenReturn("shopId"); // 指定要提取的字段名

        // 设置权限验证通过
        when(shopUserRefRepository.findByShopIdAndUserId(100L, 1L)).thenReturn(shopUserRef);
        when(joinPoint.proceed()).thenReturn("success");

        // 执行测试
        Object result = storePermissionAspect.around(joinPoint, storePermission);

        // 验证结果
        assertEquals("success", result);
        verify(shopUserRefRepository).findByShopIdAndUserId(100L, 1L);
        verify(joinPoint).proceed();
    }

    /**
     * 测试从嵌套对象中提取字段值的功能
     */
    @Test
    @DisplayName("应该能够从嵌套对象中提取店铺ID")
    void 应该能够从嵌套对象中提取店铺ID() throws Throwable {
        // 创建嵌套的测试对象
        TestNestedDto nestedDto = new TestNestedDto();
        nestedDto.setStoreId(200L);
        nestedDto.setDescription("嵌套对象测试");

        // 设置方法参数
        String[] paramNames = { "nestedDto" };
        Object[] args = { nestedDto };
        when(methodSignature.getParameterNames()).thenReturn(paramNames);
        when(joinPoint.getArgs()).thenReturn(args);
        when(storePermission.key()).thenReturn("storeId"); // 指定要提取的字段名

        // 设置权限验证通过
        ShopUserRef nestedShopUserRef = new ShopUserRef();
        nestedShopUserRef.setUserId(1L);
        nestedShopUserRef.setShopId(200L);
        nestedShopUserRef.setRole(StoreUserTypeEnum.ADMIN.getCode());
        when(shopUserRefRepository.findByShopIdAndUserId(200L, 1L)).thenReturn(nestedShopUserRef);
        when(joinPoint.proceed()).thenReturn("nested success");

        // 执行测试
        Object result = storePermissionAspect.around(joinPoint, storePermission);

        // 验证结果
        assertEquals("nested success", result);
        verify(shopUserRefRepository).findByShopIdAndUserId(200L, 1L);
        verify(joinPoint).proceed();
    }

    /**
     * 测试字段不存在时的处理
     */
    @Test
    @DisplayName("当指定字段不存在时应该直接放行")
    void 当指定字段不存在时应该直接放行() throws Throwable {
        // 创建测试对象，但不包含指定的字段
        TestRequestDto requestDto = new TestRequestDto();
        requestDto.setName("测试店铺");

        // 设置方法参数
        String[] paramNames = { "requestDto" };
        Object[] args = { requestDto };
        when(methodSignature.getParameterNames()).thenReturn(paramNames);
        when(joinPoint.getArgs()).thenReturn(args);
        when(storePermission.key()).thenReturn("nonExistentField"); // 指定不存在的字段名

        when(joinPoint.proceed()).thenReturn("no field success");

        // 执行测试
        Object result = storePermissionAspect.around(joinPoint, storePermission);

        // 验证结果 - 应该直接放行，因为没有找到可校验的店铺ID
        assertEquals("no field success", result);
        verify(shopUserRefRepository, never()).findByShopIdAndUserId(anyLong(), anyLong());
        verify(joinPoint).proceed();
    }

    // 测试用的DTO类
    public static class TestRequestDto {
        private Long shopId;
        private String name;

        public Long getShopId() {
            return shopId;
        }

        public void setShopId(Long shopId) {
            this.shopId = shopId;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    // 测试用的嵌套DTO类
    public static class TestNestedDto {
        private Long storeId;
        private String description;

        public Long getStoreId() {
            return storeId;
        }

        public void setStoreId(Long storeId) {
            this.storeId = storeId;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }
    }
}
