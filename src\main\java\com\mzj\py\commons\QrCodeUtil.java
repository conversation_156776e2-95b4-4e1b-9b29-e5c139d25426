package com.mzj.py.commons;

import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

import javax.imageio.ImageIO;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

/**
 * 二维码工具类
 * 
 * <AUTHOR>
 * @date 2019-03-28
 */
public class QrCodeUtil {

	private static final Logger LOG = LoggerFactory.getLogger(QrCodeUtil.class);

	private static final int QRCOLOR = 0xFF000000; // 默认是黑色
	private static final int BGWHITE = 0xFFFFFFFF; // 背景颜色

	private static final int WIDTH = 430; // 二维码宽
	private static final int HEIGHT = 430; // 二维码高

	/**
	 * 用于设置QR二维码参数
	 */
	private static Map<EncodeHintType, Object> hints = new HashMap<EncodeHintType, Object>() {
		private static final long serialVersionUID = 1L;
		{
			put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);// 设置QR二维码的纠错级别（H为最高级别）具体级别信息
			put(EncodeHintType.CHARACTER_SET, "utf-8");// 设置编码方式
			put(EncodeHintType.MARGIN, 2);
		}
	};

	/**
	 * 绘制简单的二维码
	 * 
	 * @param content  二维码内容
	 * @return
	 * <AUTHOR>
	 */
	public static byte[] drawSimpleQRCode(String content) {
		return drawQRCode(null, content, null);
	}
	
	/**
	 * 绘制带logo的二维码
	 * 
	 * @param content  二维码内容
	 * @param logoFile logo文件流
	 * @return
	 * <AUTHOR>
	 */
	public static byte[] drawQRCodeWithLogo(String content, InputStream logoFile) {
		return drawQRCode(logoFile, content, null);
	}
	
	/**
	 * 绘制底下带自定义文本的二维码
	 * 
	 * @param content  二维码内容
	 * @param text     二维码底下自定义文本
	 * @return
	 * <AUTHOR>
	 */
	public static byte[] drawQRCodeWithText(String content, String text) {
		return drawQRCode(null, content, text);
	}

	/**
	 * 绘制底下带自定义文本的二维码
	 *
	 * @param content  二维码内容
	 * @param text     二维码底下自定义文本
	 * @return
	 * <AUTHOR>
	 */
	public static String drawQRCodeWithTextToFile(String content, String text) {
		return drawQRCodeToFile(null, content, text);
	}
	
	/**
	 * 绘制带logo和底下添加自定义文本的二维码
	 * 
	 * @param content  二维码内容
	 * @param logoFile logo文件流
	 * @param text     二维码底下自定义文本
	 * @return
	 * <AUTHOR>
	 */
	public static byte[] drawQRCodeWithLogoAndText( String content, InputStream logoFile, String text) {
		return drawQRCode(logoFile, content, text);
	}

	/**
	 * 绘制二维码图片(可带logo/底下加自定义文本)
	 *
	 * @param logoFile
	 * @param content
	 * @param note
	 * @return
	 * <AUTHOR>
	 */
	private static byte[] drawQRCode(InputStream logoFile, String content, String note) {
		try {
			MultiFormatWriter multiFormatWriter = new MultiFormatWriter();
			// 参数顺序分别为：编码内容，编码类型，生成图片宽度，生成图片高度，设置参数
			BitMatrix bm = multiFormatWriter.encode(content, BarcodeFormat.QR_CODE, WIDTH, HEIGHT, hints);

			BufferedImage image = new BufferedImage(WIDTH, HEIGHT, BufferedImage.TYPE_INT_RGB);

			// 开始利用二维码数据创建Bitmap图片，分别设为黑（0xFFFFFFFF）白（0xFF000000）两色
			for (int x = 0; x < WIDTH; x++) {
				for (int y = 0; y < HEIGHT; y++) {
					image.setRGB(x, y, bm.get(x, y) ? QRCOLOR : BGWHITE);
				}
			}

			int height = image.getHeight();

			// 绘制logo图
			if (logoFile != null) {
				int width = image.getWidth();
				// 构建绘图对象
				Graphics2D g = image.createGraphics();
				// 读取Logo图片
				BufferedImage logo = ImageIO.read(logoFile);
				// 开始绘制logo图片
				g.drawImage(logo, width * 2 / 5, height * 2 / 5, width * 2 / 10, height * 2 / 10, null);
				g.dispose();
				logo.flush();
			}

			// 自定义文本描述
			if (StringUtils.isNotEmpty(note)) {
				// 新的图片，把带logo的二维码下面加上文字
				BufferedImage outImage = new BufferedImage(WIDTH, HEIGHT, BufferedImage.TYPE_4BYTE_ABGR);
				Graphics2D outg = outImage.createGraphics();
				// 画二维码到新的面板
				outg.drawImage(image, 0, 0, image.getWidth(), image.getHeight(), null);

				// 画文字到新的面板
				outg.setColor(Color.BLACK);
				Font font = new Font("宋体", Font.BOLD, 22);
				outg.setFont(font); // 字体、字型、字号
				int strWidth = outg.getFontMetrics().stringWidth(note);
				int startX = 220 - strWidth / 2;// x开始的位置
				int startY = HEIGHT - 10;// y开始的位置
				// 画文字
				outg.drawString(note, startX, startY);
				
				outg.dispose();
				outImage.flush();
				image = outImage;
			}

			image.flush();
			ByteArrayOutputStream os = new ByteArrayOutputStream();// 新建流。
			ImageIO.write(image, "png", os);// 利用ImageIO类提供的write方法，将bi以png图片的数据模式写入流。
			byte b[] = os.toByteArray();// 从流中获取数据数组。

			return b;
		} catch (Exception e) {
			LOG.error(e.getMessage(), e);
			return null;
		}
	}

	/**
	 * 绘制二维码图片(可带logo/底下加自定义文本)
	 *
	 * @param logoFile
	 * @param content
	 * @param note
	 * @return
	 * <AUTHOR>
	 */
	private static String drawQRCodeToFile(InputStream logoFile, String content, String note) {
		try {
			MultiFormatWriter multiFormatWriter = new MultiFormatWriter();
			// 参数顺序分别为：编码内容，编码类型，生成图片宽度，生成图片高度，设置参数
			BitMatrix bm = multiFormatWriter.encode(content, BarcodeFormat.QR_CODE, WIDTH, HEIGHT, hints);

			BufferedImage image = new BufferedImage(WIDTH, HEIGHT, BufferedImage.TYPE_INT_RGB);

			// 开始利用二维码数据创建Bitmap图片，分别设为黑（0xFFFFFFFF）白（0xFF000000）两色
			for (int x = 0; x < WIDTH; x++) {
				for (int y = 0; y < HEIGHT; y++) {
					image.setRGB(x, y, bm.get(x, y) ? QRCOLOR : BGWHITE);
				}
			}

			int height = image.getHeight();

			// 绘制logo图
			if (logoFile != null) {
				int width = image.getWidth();
				// 构建绘图对象
				Graphics2D g = image.createGraphics();
				// 读取Logo图片
				BufferedImage logo = ImageIO.read(logoFile);
				// 开始绘制logo图片
				g.drawImage(logo, width * 2 / 5, height * 2 / 5, width * 2 / 10, height * 2 / 10, null);
				g.dispose();
				logo.flush();
			}

			// 自定义文本描述
			if (StringUtils.isNotEmpty(note)) {
				// 新的图片，把带logo的二维码下面加上文字
				BufferedImage outImage = new BufferedImage(WIDTH, HEIGHT, BufferedImage.TYPE_4BYTE_ABGR);
				Graphics2D outg = outImage.createGraphics();
				// 画二维码到新的面板
				outg.drawImage(image, 0, 0, image.getWidth(), image.getHeight(), null);

				// 画文字到新的面板
				outg.setColor(Color.BLACK);
				outg.setFont(new Font("宋体", Font.BOLD, 22)); // 字体、字型、字号
				int strWidth = outg.getFontMetrics().stringWidth(note);
				int startX = 210 - strWidth / 2;// x开始的位置
				int startY = HEIGHT - 10;// y开始的位置
				// 画文字
				outg.drawString(note, startX, startY);

				outg.dispose();
				outImage.flush();
				image = outImage;
			}

			image.flush();
			String filePath = "temp_" + System.currentTimeMillis() + note;
			File temp = new File(filePath);
			ImageIO.write(image, "png", temp);// 利用ImageIO类提供的write方法，将bi以png图片的数据模式写文件。

			return filePath;
		} catch (Exception e) {
			LOG.error(e.getMessage(), e);
			return null;
		}
	}

	/**
	 * 给二维码添加文字
	 * 
	 * @param addText    添加的文字
	 * @param qrcodeByte 二维码Byte[]
	 * @return
	 * @throws IOException
	 * <AUTHOR>
	 */
	public static byte[] addTextForQrcode(String addText, byte[] qrcodeByte) throws IOException {
		return addTextForQrcodeByte(addText, qrcodeByte);
	}

	/**
	 * 给二维码添加文字
	 * 
	 * @param addText   添加的文字
	 * @param qrcodeUrl 二维码URL
	 * @return
	 * @throws IOException
	 * <AUTHOR>
	 */
	public static byte[] addTextForQrcode(String addText, String qrcodeUrl) throws IOException {
		return addTextForQrcodeByte(addText, getFileStream(qrcodeUrl));
	}

	/**
	 * 给二维码添加文字
	 * 
	 * @param addText    添加的文字
	 * @param qrcodeByte 二维码Byte[]
	 * @return
	 * @throws IOException
	 * <AUTHOR>
	 */
	private static byte[] addTextForQrcodeByte(String addText, byte[] qrcodeByte) throws IOException {
		ByteArrayInputStream in = null;
		ByteArrayOutputStream os = null;
		try {
			in = new ByteArrayInputStream(qrcodeByte);
			BufferedImage image = ImageIO.read(in);

			int imageW = image.getWidth();// 原图片宽度
			int imageH = image.getHeight();// 原图片高度
			
			BufferedImage outImage = new BufferedImage(imageW, imageH, BufferedImage.TYPE_4BYTE_ABGR);
			// 构建绘制对象
			Graphics2D g = outImage.createGraphics();
			// 画二维码到新的画板
			g.drawImage(image, 0, 0, imageW, imageH, null);
			g.setColor(Color.BLACK);
			g.setBackground(Color.WHITE);
			g.setFont(new Font("宋体", Font.BOLD, 20));
			
			int strWidth = g.getFontMetrics().stringWidth(addText);
			// 计算文字开始的位置
			int startX = 210 - strWidth / 2;// x开始的位置
			int startY = imageH - 8;// y开始的位置   
			// 画文字
			g.drawString(addText, startX, startY);
			g.dispose();
			outImage.flush();
			
			os = new ByteArrayOutputStream();
			ImageIO.write(outImage, "png", os);// 利用ImageIO类提供的write方法，将图片以png图片格式的数据模式写入流。
			byte b[] = os.toByteArray();// 从流中获取数据数组。
			
			return b;
		} catch (Exception e) {
			LOG.error(e.getMessage(), e);
			return null;
		} finally {
			if (in != null) {
				in.close();
			}
			if (os != null) {
				os.close();
			}
		}
	}

	/**
	 * 得到文件流
	 * 
	 * @param url
	 * @return
	 * <AUTHOR>
	 */
	private static byte[] getFileStream(String url) {
		try {
			URL httpUrl = new URL(url);
			HttpURLConnection conn = (HttpURLConnection) httpUrl.openConnection();
			conn.setRequestMethod("GET");
			conn.setConnectTimeout(5 * 1000);
			InputStream inStream = conn.getInputStream();// 通过输入流获取图片数据
			byte[] btImg = readInputStream(inStream);// 得到图片的二进制数据
			return btImg;
		} catch (Exception e) {
			LOG.error(e.getMessage(), e);
		}
		return null;
	}

	/**
	 * 从输入流中获取数据
	 * 
	 * @param inStream
	 * @return
	 * @throws Exception
	 * <AUTHOR>
	 */
	private static byte[] readInputStream(InputStream inStream) throws Exception {
		ByteArrayOutputStream outStream = new ByteArrayOutputStream();
		byte[] buffer = new byte[1024];
		int len = 0;
		while ((len = inStream.read(buffer)) != -1) {
			outStream.write(buffer, 0, len);
		}
		inStream.close();
		return outStream.toByteArray();
	}

	/**
	 * byte转file
	 * 
	 * @param bytes
	 * @return
	 * <AUTHOR>
	 */
	public static File byte2File(byte[] bytes) {
		OutputStream ous = null;
		try {
			File file = File.createTempFile("temp_file", "." + "jpg");;
			ous = new FileOutputStream(file);
			ous.write(bytes);
			return file;
		} catch (Exception e) {
			LOG.error(e.getMessage(), e);
			return null;
		} finally {
			if (ous != null) {
				try {
					ous.close();
				} catch (IOException e) {
					LOG.error(e.getMessage(), e);
				}
			}
		}
	}
	
}
