<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.mzj.cc.microservice</groupId>
	<artifactId>py-service</artifactId>
	<version>prod</version>
	<packaging>jar</packaging>

	<name>py-service</name>
	<description>py-service project for Spring Boot</description>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.5.2</version>
		<relativePath /> <!-- lookup parent from repository -->
	</parent>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<dependencies>
		<!-- spring boot -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-amqp</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
		</dependency>
		<!-- redis 缓存操作 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
			<exclusions>
				<exclusion>
					<groupId>io.lettuce</groupId>
					<artifactId>lettuce-core</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-integration</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-stream</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-mqtt</artifactId>
		</dependency>


		<!-- mysql connector -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>

		<!-- druid -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
			<version>1.2.16</version>
		</dependency>

		<!-- 音频合成 -->
		<dependency>
			<groupId>com.alibaba.nls</groupId>
			<artifactId>nls-sdk-tts</artifactId>
			<version>2.2.14</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba.nls</groupId>
			<artifactId>nls-sdk-transcriber</artifactId>
			<version>2.2.1</version>
		</dependency>

		<!-- apache commons -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.14.0</version>
		</dependency>

		<dependency>
			<groupId>org</groupId>
			<artifactId>jaudiotagger</artifactId>
			<version>2.0.1</version>
		</dependency>

		<!-- -->
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcpkix-jdk15on</artifactId>
			<version>1.55</version>
		</dependency>

		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
			<version>2.8.1</version>
		</dependency>
		<!-- aliyun sms -->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>dysmsapi20170525</artifactId>
			<version>2.0.5</version>
		</dependency>
		<dependency>
			<groupId>com.thoughtworks.xstream</groupId>
			<artifactId>xstream</artifactId>
			<version>1.4.21</version>
		</dependency>
		<dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>0.4.8</version>
        </dependency>
		<!-- alipay-sdk-java -->
		<!-- <dependency>
			<groupId>com.alipay.sdk</groupId>
			<artifactId>alipay-sdk-java</artifactId>
			<version>3.7.73.ALL</version>
		</dependency> -->
		<!-- fastjson -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.70</version>
		</dependency>
		<dependency>
			<groupId>net.imagej</groupId>
			<artifactId>ij</artifactId>
			<version>1.51q</version>
		</dependency>

		<dependency>
			<groupId>commons-validator</groupId>
			<artifactId>commons-validator</artifactId>
			<version>1.5.1</version>
		</dependency>
		<dependency>
			<groupId>com.googlecode.libphonenumber</groupId>
			<artifactId>libphonenumber</artifactId>
			<version>8.8.1</version>
		</dependency>
		<!-- barcode -->
		<dependency>
			<groupId>net.sf.barcode4j</groupId>
			<artifactId>barcode4j-light</artifactId>
			<version>2.0</version>
		</dependency>
		<!-- qrcode -->
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>core</artifactId>
			<version>3.2.1</version>
		</dependency>
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>javase</artifactId>
			<version>3.2.1</version>
		</dependency>

		<dependency>
			<groupId>org.eclipse.paho</groupId>
			<artifactId>org.eclipse.paho.client.mqttv3</artifactId>
			<version>1.2.5</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.apache.ant/ant -->
		<dependency>
			<groupId>org.apache.ant</groupId>
			<artifactId>ant</artifactId>
			<version>1.10.11</version>
			<exclusions>
				<exclusion>  <!-- declare the exclusion here -->
					<groupId>com.sun</groupId>
					<artifactId>tools</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.35</version>
            <scope>compile</scope>
        </dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.30</version>
			<optional>true</optional>
		</dependency>

		<!-- Old Jackson 1.x dependency -->
		<dependency>
			<groupId>org.codehaus.jackson</groupId>
			<artifactId>jackson-mapper-asl</artifactId>
			<version>1.9.13</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.mockito</groupId>
					<artifactId>mockito-core</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-inline</artifactId>
			<version>3.9.0</version>
			<scope>test</scope>
		</dependency>
		<!-- keep junit dependency -->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.github.wechatpay-apiv3</groupId>
			<artifactId>wechatpay-java</artifactId>
			<version>0.2.12</version>
		</dependency>

		<dependency>
			<groupId>com.microsoft.cognitiveservices.speech</groupId>
			<artifactId>client-sdk</artifactId>
			<version>1.40.0</version>
		</dependency>

		<dependency>
			<groupId>com.huaweicloud.sdk</groupId>
			<artifactId>huaweicloud-sdk-sis</artifactId>
			<version>3.1.112</version>
		</dependency>

		<dependency>
			<groupId>com.tencentcloudapi</groupId>
			<artifactId>tencentcloud-speech-sdk-java</artifactId>
			<version>1.0.48</version>
		</dependency>


		<dependency>
			<groupId>it.sauronsoftware</groupId>
			<artifactId>jave</artifactId>
			<version>1.0.2</version> <!-- 替换为您想要的版本号 -->
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>dashscope-sdk-java</artifactId>
			<version>2.18.0</version>
		</dependency>

		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>broadscope-bailian-sdk-java</artifactId>
			<version>1.3.0</version>
		</dependency>

		<dependency>
			<groupId>io.netty</groupId>
			<artifactId>netty-all</artifactId>
			<version>4.1.25.Final</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
					<archive>
						<manifest>
							<addClasspath>true</addClasspath>
							<classpathPrefix>lib/</classpathPrefix>
							<mainClass>com.mzj.py.AppServiceApplication</mainClass>
						</manifest>
					</archive>
				</configuration>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>jar</goal>
						</goals>
						<configuration>
							<classifier>bak</classifier>
							<classesDirectory>${basedir}/target/classes</classesDirectory>
							<finalName>${project.artifactId}-${project.version}</finalName>
							<outputDirectory>${basedir}/target/maven-archiver</outputDirectory>
							<excludes>
								<exclude>*.conf</exclude>
							</excludes>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-dependencies</id>
						<phase>package</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<outputDirectory>${basedir}/target/lib</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<!-- 生成漂亮的单元测试 HTML 报告 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-report-plugin</artifactId>
				<version>3.1.2</version>
			</plugin>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>0.8.11</version>
				<executions>
					<!-- 在测试前为 JVM 附加 agent -->
					<execution>
						<id>prepare-agent</id>
						<goals><goal>prepare-agent</goal></goals>
					</execution>
					<!-- 测试跑完后生成 HTML/CSV 报告 -->
					<execution>
						<id>report</id>
						<phase>test</phase>
						<goals><goal>report</goal></goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
