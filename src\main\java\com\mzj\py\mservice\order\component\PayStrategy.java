package com.mzj.py.mservice.order.component;


import com.mzj.py.mservice.order.vo.PayInfoVO;

public interface PayStrategy {

    /**
     * 统一下单接口
     * @param payInfoVO
     * @return
     */
    String unifiedOrder(PayInfoVO payInfoVO);


    /**
     * 退款接口
     * @param payInfoVO
     * @return
     */
    default String refund(PayInfoVO payInfoVO){ return ""; }


    /**
     * 查询支付状态
     * @param payInfoVO
     * @return
     */
    String queryPayStatus(PayInfoVO payInfoVO);


    /**
     * 关闭订单
     * @param payInfoVO
     * @return
     */
    String closeOrder(PayInfoVO payInfoVO);
}
