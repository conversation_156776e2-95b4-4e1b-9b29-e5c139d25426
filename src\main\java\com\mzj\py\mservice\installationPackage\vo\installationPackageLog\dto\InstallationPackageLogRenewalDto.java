package com.mzj.py.mservice.installationPackage.vo.installationPackageLog.dto;

import java.util.List;

public class InstallationPackageLogRenewalDto {

    private Long installationPackageId;

    private List<Long> deviceIdList;

    public Long getInstallationPackageId() {
        return installationPackageId;
    }

    public void setInstallationPackageId(Long installationPackageId) {
        this.installationPackageId = installationPackageId;
    }

    public List<Long> getDeviceIdList() {
        return deviceIdList;
    }

    public void setDeviceIdList(List<Long> deviceIdList) {
        this.deviceIdList = deviceIdList;
    }
}
