package com.mzj.py.mservice.compound.compound;

import com.alibaba.nls.client.protocol.SampleRateEnum;
import com.mzj.py.commons.StringUtils;
import com.mzj.py.commons.enums.PlatFormStatusEnum;
import com.mzj.py.commons.enums.PlatFormTypeEnum;
import com.mzj.py.mservice.compound.entity.SpeechSynthesizerDto;
import com.mzj.py.mservice.compound.utils.FfmpegUtil;
import com.mzj.py.mservice.compound.utils.FileUtil;
import com.mzj.py.mservice.compound.utils.LineInsertUtils;
import com.mzj.py.mservice.home.entity.PlatformConfig;
import com.mzj.py.mservice.home.repository.PlatformConfigRepository;
import com.mzj.py.mservice.redis.RedisService;
import com.tencent.SpeechClient;
import com.tencent.tts.model.SpeechSynthesisRequest;
import com.tencent.tts.model.SpeechSynthesisResponse;
import com.tencent.tts.service.SpeechSynthesisListener;
import com.tencent.tts.service.SpeechSynthesizer;
import com.tencent.tts.utils.Ttsutils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-03-09-13:49
 */
@Service
public class TxDubbingStrategy implements DubbingStrategy{

    @Autowired
    private ThreadPoolTaskExecutor longVoiceTaskExecutor;

    @Autowired
    private PlatformConfigRepository platformConfigRepository;
    @Override
    public String process(SpeechSynthesizerDto dto, RedisService redisService) throws Exception {
        List<PlatformConfig> platformConfigs = platformConfigRepository.findByPlatformTypeAndStatus(PlatFormTypeEnum.TX_DUBBING.name(),
                PlatFormStatusEnum.ENABLED.ordinal());
        PlatformConfig platformConfig = platformConfigs.get(0);
        String appId = platformConfig.getAppId();
        String secretId = platformConfig.getAccessKey();
        String secretKey = platformConfig.getSecretKey();
        Double volume = LineInsertUtils.mapRange(dto.getVolume(), 0,
                100, 0, 10);
        if(StringUtils.isNotBlank(dto.getBgm())){
            volume = LineInsertUtils.mapRange(dto.getVolume()-60, 0,
                    100, 0, 10);
        }

        Double speechRate = LineInsertUtils.mapRange(dto.getSpeechRate()-250, -500,
                500, -2, 6);
        SpeechSynthesisRequest request = SpeechSynthesisRequest.initialize();
        request.setCodec("mp3");
        request.setSampleRate(SampleRateEnum.SAMPLE_RATE_16K.value);
        request.setVolume(Integer.parseInt(String.valueOf(Math.round(volume))));
        request.setSpeed(Float.parseFloat(String.valueOf(speechRate)));
        request.setVoiceType(Integer.valueOf(dto.getVoice()));
        //使用客户端client创建语音合成实例
        SpeechClient client = SpeechClient.newInstance(appId, secretId, secretKey);
        // 设定每次循环需要获取的字符数
        int chunkSize = 500;
        // 开始位置
        int startIndex = 0;
        String originalString = dto.getText();
        int i=0;
        File templateFile = new File(  dto.getUrl() + FileUtil.getFileNewName(".mp3"));
        List<CompletableFuture<File>> futures=new ArrayList();
        // 当剩余字符数大于等于chunkSize时，继续循环
        while (startIndex < originalString.length()) {
            File tempFile = new File(dto.getUrl()+FileUtil.getFileNewName(".mp3"));
            // 计算本次循环需要截取的结束位置
            int endIndex = Math.min(startIndex + chunkSize, originalString.length());
            // 使用substring获取子串
            String substring = originalString.substring(startIndex, endIndex);
            // 更新开始位置为本次循环的结束位置加1
            startIndex = endIndex;
            CompletableFuture<File> future = CompletableFuture.supplyAsync(
                    () -> {
                        SpeechSynthesizer speechSynthesizer=client.newSpeechSynthesizer(request, getSynthesizerListener(tempFile));
                        speechSynthesizer.synthesis(substring);
                        return tempFile;
                    },longVoiceTaskExecutor);
            futures.add(future);
            i++;
        }
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        allFutures.join(); // 等待所有任务完成
        futures.forEach(future -> {
            try {
                File file = future.get();
                FileUtil.combine(templateFile,file);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        if(!StringUtils.isEmpty(dto.getBgm())){
            String bgmFileName = dto.getUrl() +FileUtil.getFileNewName(".wav");
            File bgmFile=FileUtil.taiseng(dto.getBgm(),bgmFileName,dto.getBugRate());
            File outFile=new File(dto.getUrl() +FileUtil.getFileNewName(".wav"));
            FfmpegUtil.mixBgm(templateFile, bgmFile, outFile,dto.getBeforeDelay(),dto.getAfterDelay(),dto.getBgmCenterVolum());
            File file = new File(  dto.getUrl()+FileUtil.getFileNewName(".mp3"));
            FileUtil.coverToMp3(outFile,file);
            return file.getAbsolutePath();
        }else{
            File file = new File(  dto.getUrl()+FileUtil.getFileNewName(".mp3"));
            FileUtil.coverToMp3(templateFile,file);
            return file.getAbsolutePath();
        }
    }

    public  SpeechSynthesisListener getSynthesizerListener(File tempFile)  {
        SpeechSynthesisListener listener = null;
        try {
            listener=new SpeechSynthesisListener() {
                private AtomicInteger sessionId = new AtomicInteger(0);
                @Override
                public void onComplete(SpeechSynthesisResponse response) {
                    if (response.getSuccess()) {
                        try {
                            Ttsutils.saveResponseToFile(response.getAudio(), tempFile.getPath());
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                }


                //语音合成的语音二进制数据
                @Override
                public void onMessage(byte[] data) {
                    sessionId.incrementAndGet();
                }


                @Override
                public void onFail(SpeechSynthesisResponse response) {

                }
            };
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return listener;
    }

}
