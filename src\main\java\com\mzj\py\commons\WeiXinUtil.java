package com.mzj.py.commons;

import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.io.xml.DomDriver;
import com.thoughtworks.xstream.io.xml.XmlFriendlyNameCoder;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.JDOMException;
import org.jdom.input.SAXBuilder;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * @Author: wangrl
 * @Description: 微信支付工具类
 * @Date: 11:29 2018/5/3
 * @Version: 1.0
 */
public class WeiXinUtil {

	public static String readInputStream(InputStream inStream) throws Exception {
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		try {
			int len = 0;
			byte[] buffer = new byte[512];
			while ((len = inStream.read(buffer)) != -1) {
				outputStream.write(buffer, 0, len);
			}
			return new String(outputStream.toByteArray(), 0, outputStream.size(), "utf-8");
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (outputStream != null) {
				outputStream.close();
			}
			if (outputStream != null) {
				inStream.close();
			}
		}
		return "";
	}

	/**
	 * 签名算法
	 * 
	 * @param parameters 要参与签名的数据对象
	 * @return 签名
	 */
	public static String getSign(String characterEncoding, SortedMap<Object, Object> parameters, String key) {
		StringBuffer sb = new StringBuffer();
		Set<Map.Entry<Object, Object>> es = parameters.entrySet();
		Iterator<Map.Entry<Object, Object>> it = es.iterator();
		while (it.hasNext()) {
			Map.Entry<Object, Object> entry = (Map.Entry<Object, Object>) it.next();
			String k = (String) entry.getKey();
			Object v = entry.getValue();
			if (null != v && !"".equals(v) && !"sign".equals(k) && !"key".equals(k)) {
				sb.append(k + "=" + v + "&");
			}
		}
		/** 支付密钥必须参与加密，放在字符串最后面 */
		sb.append("key=" + key);
		String sign = MD5Util.MD5Encode(sb.toString(), characterEncoding).toUpperCase();
		return sign;
	}

	/**
	 * 是否签名正确,规则是:按参数名称a-z排序,遇到空值的参数不参加签名。
	 * 
	 * @return boolean
	 */
	@SuppressWarnings("rawtypes")
	public static boolean isTenPaySign(SortedMap<Object, Object> packageParams, String API_KEY) {
		StringBuffer sb = new StringBuffer();
		Set es = packageParams.entrySet();
		Iterator it = es.iterator();
		while (it.hasNext()) {
			Map.Entry entry = (Map.Entry) it.next();
			String k = (String) entry.getKey();
			String v = (String) entry.getValue();
			if (!"sign".equals(k) && null != v && !"".equals(v)) {
				sb.append(k + "=" + v + "&");
			}
		}
		sb.append("key=" + API_KEY);
		// 算出摘要
		String mySign = MD5Util.MD5Encode(sb.toString(), "UTF-8").toLowerCase();
		String tenPaySign = ((String) packageParams.get("sign")).toLowerCase();
		return tenPaySign.equals(mySign);
	}

	/**
	 * 解析xml,返回第一级元素键值对。如果第一级元素有子节点，则此节点的值是子节点的xml数据。
	 * 
	 * @param strXml
	 * @return
	 * @throws JDOMException
	 * @throws IOException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static Map doXMLParse(String strXml) throws JDOMException, IOException {
		strXml = strXml.replaceFirst("encoding=\".*\"", "encoding=\"UTF-8\"");

		if (null == strXml || "".equals(strXml)) {
			return null;
		}
		Map m = new HashMap();
		InputStream in = new ByteArrayInputStream(strXml.getBytes("UTF-8"));
		SAXBuilder builder = new SAXBuilder();
		Document doc = builder.build(in);
		Element root = doc.getRootElement();
		List list = root.getChildren();
		Iterator it = list.iterator();
		while (it.hasNext()) {
			Element e = (Element) it.next();
			String k = e.getName();
			String v = "";
			List children = e.getChildren();
			if (children.isEmpty()) {
				v = e.getTextNormalize();
			} else {
				v = WeiXinUtil.getChildrenText(children);
			}
			m.put(k, v);
		}
		// 关闭流
		in.close();

		return m;
	}

	/**
	 * 获取子结点的xml
	 * 
	 * @param children
	 * @return String
	 */
	@SuppressWarnings("rawtypes")
	public static String getChildrenText(List children) {
		StringBuffer sb = new StringBuffer();
		if (!children.isEmpty()) {
			Iterator it = children.iterator();
			while (it.hasNext()) {
				Element e = (Element) it.next();
				String name = e.getName();
				String value = e.getTextNormalize();
				List list = e.getChildren();
				sb.append("<" + name + ">");
				if (!list.isEmpty()) {
					sb.append(WeiXinUtil.getChildrenText(list));
				}
				sb.append(value);
				sb.append("</" + name + ">");
			}
		}
		return sb.toString();
	}

	public static String objectToXml(Object xmlObj) {
		String resultStr = "";
		try {
			// 解决XStream对出现双下划线的bug
			XStream xStreamForRequestPostData = new XStream(new DomDriver("UTF-8", new XmlFriendlyNameCoder("-_", "_")));
			xStreamForRequestPostData.alias("xml", xmlObj.getClass());
			// 将要提交给API的数据对象转换成XML格式数据Post给API
			resultStr = xStreamForRequestPostData.toXML(xmlObj);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return resultStr;
	}

	/**
	 * 将请求参数转换为xml格式的string字符串，微信服务器接收的是xml格式的字符串
	 * 
	 * @param parameters
	 * @return
	 */
	public static String mapToXml(Map<Object, Object> parameters) {
		StringBuffer sb = new StringBuffer();
		sb.append("<xml>");
		for (Map.Entry<Object, Object> entry : parameters.entrySet()) {
			String k = (String) entry.getKey();
			Object value = entry.getValue();
			if ("attach".equalsIgnoreCase(k) || "body".equalsIgnoreCase(k) || "sign".equalsIgnoreCase(k)) {
				sb.append("<" + k + ">" + "<![CDATA[" + value + "]]></" + k + ">");
			} else {
				sb.append("<" + k + ">" + value + "</" + k + ">");
			}
		}
		sb.append("</xml>");
		return sb.toString();
	}

	/**
	 * JS-SDK使用权限签名<br/>
	 * 请参照JS-SDK使用权限签名算法中的<a href=
	 * "http://mp.weixin.qq.com/wiki/11/74ad127cc054f6b80759c40f77ec03db.html#.E9.99.84.E5.BD.951-JS-SDK.E4.BD.BF.E7.94.A8.E6.9D.83.E9.99.90.E7.AD.BE.E5.90.8D.E7.AE.97.E6.B3.95">签名算法</a>
	 * 
	 * @param jsapiTicket 调用微信JS接口的临时票据
	 * @param timeStamp   时间戳
	 * @param nonceStr    随机字符串
	 * @param url         当前网页的url地址
	 * @return
	 */
	public static String createJSSDKSignature(String jsapiTicket, long timeStamp, String nonceStr, String url) {
		StringBuffer sb = new StringBuffer();
		try {
			Map<String, String> map = new HashMap<String, String>();
			map.put("jsapi_ticket", jsapiTicket);
			map.put("timestamp", "" + timeStamp);
			map.put("noncestr", nonceStr);
			map.put("url", url);

			List<Map.Entry<String, String>> mappingList = new ArrayList<Map.Entry<String, String>>(map.entrySet());
			Collections.sort(mappingList, new Comparator<Map.Entry<String, String>>() {
				@Override
				public int compare(Map.Entry<String, String> o1, Map.Entry<String, String> o2) {
					return o1.getKey().compareTo(o2.getKey());
				}
			});

			StringBuffer buffer = new StringBuffer();
			for (Map.Entry<String, String> entry : mappingList) {
				buffer.append(entry.getKey() + "=" + entry.getValue());
				buffer.append("&");
			}
			String encodeStr = buffer.toString().substring(0, buffer.length() - 1);

			MessageDigest digest = MessageDigest.getInstance("SHA-1");
			byte[] bytes = digest.digest(encodeStr.getBytes());
			for (byte b : bytes) {
				String tmp = Integer.toHexString(b & 0xff);
				sb.append(tmp.length() == 1 ? "0" : "");
				sb.append(tmp);
			}
			return sb.toString();
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		}
		return null;
	}

}
