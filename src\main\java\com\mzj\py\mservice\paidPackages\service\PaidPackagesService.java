package com.mzj.py.mservice.paidPackages.service;

import com.mzj.py.commons.JsonUtil;
import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.commons.enums.EventMessageType;
import com.mzj.py.commons.model.EventMessage;
import com.mzj.py.mservice.paidPackages.entity.PaidPackages;
import com.mzj.py.mservice.paidPackages.repository.PaidPackagesRepository;
import com.mzj.py.mservice.paidPackages.vo.PaidPackagesVo;
import com.mzj.py.mservice.paidPackages.vo.dto.PaidPackagesAddDto;
import com.mzj.py.mservice.redis.RedisService;
import com.mzj.py.mservice.wxuser.entity.WxUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

@Service
@Slf4j
public class PaidPackagesService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private RedisService redisService;

    @Autowired
    private PaidPackagesRepository paidPackagesRepository;

    public ResultBean list(String accessToken, Integer pageSize, Integer pageNumber){

        TokenRedisVo tokenVo = redisService.findTokenVo(accessToken);
        if (tokenVo == null) {
            return ResultBean.failedResultWithMsg("用户不存在");
        }

        StringBuilder where = new StringBuilder();
        List<Object> args = new ArrayList<>();

        String sql = "SELECT\n" +
                "p.id, \n" +
                "p.name ,\n" +
                "p.data_num as amount,\n" +
                "p.effect_day as validDays,\n" +
                "p.selling_price as price,\n" +
                "p.create_time createdAt \n" +
                "FROM dub_paid_packages p\n" +
                "where 1=1 order by p.create_time desc ";



        Map<String, Object> map = new HashMap<>();

        Long count = jdbcTemplate.queryForObject("select count(1) from(" + sql + where + ")t ", Long.class, args.toArray());
        if (count == 0) {
            map.put("count", 0);
            map.put("result", null);
            return ResultBean.successfulResult(map);
        }
        where.append(" limit ? offset ? ");
        args.add(pageSize);
        args.add(pageSize * pageNumber);
        List<PaidPackagesVo> devices = jdbcTemplate.query(sql + where, new BeanPropertyRowMapper<>(PaidPackagesVo.class), args.toArray());
        map.put("count", count);
        map.put("result", devices);
        return ResultBean.successfulResult(map);
    }



    public ResultBean addOrUpdate(PaidPackagesAddDto vo, String accessToken) {

        TokenRedisVo tokenVo = redisService.findTokenVo(accessToken);
        if (tokenVo == null) {
            return ResultBean.failedResultWithMsg("用户不存在");
        }

        if (vo == null){
            return ResultBean.failedResultWithMsg("数据不能为空");
        }
        if (StringUtils.isBlank(vo.getName())){
            return ResultBean.failedResultWithMsg("套餐名称不能为空");
        }
        if (vo.getAmount()==null){
            return ResultBean.failedResultWithMsg("套餐流量不能为空");
        }
        if (vo.getValidDays()==null){
            return ResultBean.failedResultWithMsg("有效天数不能为空");
        }
        if (vo.getPrice()==null){
            return ResultBean.failedResultWithMsg("售价不能为空");
        }
        Date date = new Date();
        if (vo.getId() == null){
            //新增
            PaidPackages device = new PaidPackages();
            device.setName(vo.getName());
            device.setCreateUserId(tokenVo.getId());
            device.setEffectDay(vo.getValidDays());
            device.setDataNum(vo.getAmount());
            device.setSellingPrice(vo.getPrice());
            device.setCreateTime(date);
            paidPackagesRepository.save(device);
        }else {
            Optional<PaidPackages> byId = paidPackagesRepository.findById(vo.getId());
            if (byId.isPresent()) {
                //修改
                PaidPackages device = byId.get();
                device.setName(vo.getName());
                device.setEffectDay(vo.getValidDays());
                device.setDataNum(vo.getAmount());
                device.setSellingPrice(vo.getPrice());
                paidPackagesRepository.save(device);
            } else {
                //新增
                PaidPackages device = new PaidPackages();
                device.setName(vo.getName());
                device.setCreateUserId(tokenVo.getId());
                device.setEffectDay(vo.getValidDays());
                device.setDataNum(vo.getAmount());
                device.setSellingPrice(vo.getPrice());
                device.setCreateTime(date);
                paidPackagesRepository.save(device);
            }
        }
        return ResultBean.successfulResult(true);
    }


    public ResultBean delete(Long id){
        if (id == null){
            return ResultBean.failedResultWithMsg("流量管理id不能为空");
        }
        paidPackagesRepository.deleteById(id);
        return ResultBean.successfulResult(true);
    }


    public ResultBean getInfo(Long id) {
        PaidPackages paidPackages = paidPackagesRepository.getOne(id);
        PaidPackagesVo vo=new PaidPackagesVo();
        vo.setId(paidPackages.getId());
        vo.setName(paidPackages.getName());
        vo.setAmount(paidPackages.getDataNum());
        vo.setValidDays(paidPackages.getEffectDay());
        vo.setPrice(paidPackages.getSellingPrice());
        vo.setCreatedAt(paidPackages.getCreateTime());
        return ResultBean.successfulResult(vo);
    }

    public void handleVipMessage(EventMessage eventMessage) {
        String openId = eventMessage.getOpenId();
        String messageType = eventMessage.getEventMessageType();
        if(EventMessageType.PRODUCT_ORDER_PAY.name().equalsIgnoreCase(messageType)){
            String content = eventMessage.getContent();
            Map<String, Object> orderInfoMap = JsonUtil.json2Obj(content,Map.class);
        }
    }
}
