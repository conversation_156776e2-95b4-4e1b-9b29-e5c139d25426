package com.mzj.py.mservice.sms.controller;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.exception.CustomException;
import com.mzj.py.mservice.sms.service.SmsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/*
 * 短信验证码
 * <AUTHOR>
 * @createDate: 2021年3月19日 下午7:24:51
 * @version: 1.0
 */

@Controller
@RequestMapping("/sms")
public class SmsController {
    private Logger LOG = LoggerFactory.getLogger(SmsController.class);
    @Autowired
    private SmsService smsService;

    /*
     * 发送模板短信
     *
     * @param mobile
     * @param type 		1登录
     * @param code
     * @return
     * <AUTHOR>
     * @date 2021年3月19日
     */

    @PostMapping("/code")
    @ResponseBody
    public ResultBean<Object> code(@RequestParam(name = "mobile") String mobile, @RequestParam(name = "type") Integer type, @RequestParam(name = "code", required = false) String code) {
        String result = null;
        try {
            result = smsService.sendSms(mobile, null, null, null);
        } catch (CustomException e) {
            LOG.error(e.getMessage(), e);
        }
        if (result == null) {
            return ResultBean.failedResultOfParamWithMsg("发送失败");
        }
        return ResultBean.successfulResult(result);
    }
}
