package com.mzj.py.mservice.compound.entity;

public class CustomAudioRequest {
    private String tts_text;
    private String instruct_text;
    private String prompt_wav_path;
    private Boolean stream;
    private Double speed;

    public String getTts_text() {
        return tts_text;
    }

    public void setTts_text(String tts_text) {
        this.tts_text = tts_text;
    }

    public String getInstruct_text() {
        return instruct_text;
    }

    public void setInstruct_text(String instruct_text) {
        this.instruct_text = instruct_text;
    }

    public String getPrompt_wav_path() {
        return prompt_wav_path;
    }

    public void setPrompt_wav_path(String prompt_wav_path) {
        this.prompt_wav_path = prompt_wav_path;
    }

    public Boolean getStream() {
        return stream;
    }

    public void setStream(Boolean stream) {
        this.stream = stream;
    }

    public Double getSpeed() {
        return speed;
    }

    public void setSpeed(Double speed) {
        this.speed = speed;
    }
}