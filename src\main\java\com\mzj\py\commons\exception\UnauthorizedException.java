package com.mzj.py.commons.exception;

import org.springframework.http.HttpStatus;

/**
 * 没有权限异常
 * <AUTHOR>
 *
 */
public class UnauthorizedException extends ServiceException{

	private static final long serialVersionUID = 1L;
	
	public UnauthorizedException(String message) {
		super(HttpStatus.UNAUTHORIZED,ErrorCode.UNAUTHORIZED.code(), message);
	}
	
	public UnauthorizedException(int code,String message) {
		super(HttpStatus.UNAUTHORIZED,code, message);
	}
}
