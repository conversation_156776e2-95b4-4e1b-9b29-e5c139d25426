package com.mzj.py.mservice.order.utils;

import com.alibaba.fastjson.JSONObject;
import com.wechat.pay.java.service.payments.model.Transaction;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2025-06-16-14:01
 */
public class PayUtils {
    public static String getRequestBody(HttpServletRequest request) {

        StringBuffer sb = new StringBuffer();

        try (ServletInputStream inputStream = request.getInputStream();
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
        ) {
            String line;

            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return sb.toString();
    }
    public  static Map<String, Object> convertWechatPayMsgToMap(Transaction transaction) {
        Map<String,Object> paramsMap = new HashMap<>(8);
        JSONObject jsonObject=JSONObject.parseObject(transaction.getAttach());
        paramsMap.put("out_trade_no",transaction.getOutTradeNo());
        paramsMap.put("trade_state",transaction.getTradeState().name());
        paramsMap.put("trade_type",transaction.getTradeType().name());
        paramsMap.put("success_time",transaction.getSuccessTime());
        paramsMap.put("transaction_id",transaction.getTransactionId());
        paramsMap.put("open_id", transaction.getPayer().getOpenid());
        paramsMap.put("user_id", jsonObject.get("user_id"));
        return paramsMap;
    }
}
