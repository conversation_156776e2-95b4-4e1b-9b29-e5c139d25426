package com.mzj.py.commons;

import java.util.Timer;
import java.util.TimerTask;

public class CachedTimer
{
	private static final Timer TIMER = new Timer( "CachedTimer", true ) ;
	public static long INTERVAL = 100 ;
	
	static
	{
		TIMER.scheduleAtFixedRate( new TimerTask(){
			@Override
			public void run()
			{
				currentTimeMillis = System.currentTimeMillis() ;
			}
		}, 0, INTERVAL) ;
	}
	
	public static volatile long currentTimeMillis = System.currentTimeMillis() ;
}
