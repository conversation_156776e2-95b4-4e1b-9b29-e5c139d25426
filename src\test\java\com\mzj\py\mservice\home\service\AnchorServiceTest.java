package com.mzj.py.mservice.home.service;

import com.alibaba.fastjson.JSON;
import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.StatusCode;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.commons.exception.CustomException;
import com.mzj.py.mservice.compound.entity.SpeechSynthesizerDto;
import com.mzj.py.mservice.home.controller.request.RecordUploadReq;
import com.mzj.py.mservice.compound.compound.DubbingFactory;
import com.mzj.py.mservice.home.entity.*;
import com.mzj.py.mservice.home.repository.*;
import com.mzj.py.mservice.home.vo.AnchorVo;
import com.mzj.py.mservice.home.vo.MergeVo;
import com.mzj.py.mservice.home.vo.VoiceWorkVo;
import com.mzj.py.mservice.oss.service.OSSService;
import com.mzj.py.mservice.redis.RedisService;
import com.mzj.py.mservice.wxuser.entity.WxUser;
import com.mzj.py.mservice.wxuser.repository.WxUserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import org.springframework.beans.BeanUtils;
import com.mzj.py.mservice.compound.utils.FfmpegUtil;
import org.jaudiotagger.audio.AudioFileIO;
import org.jaudiotagger.audio.mp3.MP3AudioHeader;
import org.jaudiotagger.audio.mp3.MP3File;
import org.mockito.MockedStatic;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import com.mzj.py.mservice.compound.utils.FileUtil;

/**
 * AnchorService单元测试类
 * 测试AnchorService中所有公共方法的业务逻辑
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AnchorService单元测试")
class AnchorServiceTest {

    @Mock
    private RedisService redisService;

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private DubAnchorRepository anchorRepository;

    @Mock
    private DubTemplateTypeRepository templateTypeRepository;

    @Mock
    private DubBackgroundMusicTypeRepository musicTypeRepository;

    @Mock
    private VoiceWorkRepository workRepository;

    @Mock
    private VoicePacketRepository packetRepository;

    @Mock
    private OSSService ossService;

    @Mock
    private DubAnchorRepository dubAnchorRepository;

    @Mock
    private DubbingFactory dubbingFactory;

    @Mock
    private WxUserRepository wxUserRepository;

    @Mock
    private LongAnchorRepository longAnchorRepository;

    @Mock
    private UserRecordRepository userRecordRepository;

    @Mock
    private AiStyleRepository aiStyleRepository;

    @Mock
    private AiClassRepository aiClassRepository;

    @Mock
    private AiLanguageRepository aiLanguageRepository;

    @Mock
    private UserBackgroundMusicRepository userBackgroundMusicRepository;

    @InjectMocks
    private AnchorService anchorService;

    private TokenRedisVo mockTokenRedisVo;
    private DubAnchor mockDubAnchor;
    private LongAnchor mockLongAnchor;
    private SpeechSynthesizerDto mockSpeechDto;

    @BeforeEach
    void setUp() {
        // 设置配置属性
        ReflectionTestUtils.setField(anchorService, "appUrl", "/tmp/test/");
        ReflectionTestUtils.setField(anchorService, "ossUrl", "https://test-oss.com/");

        // 初始化测试数据
        mockTokenRedisVo = new TokenRedisVo();
        mockTokenRedisVo.setId(1L);
        mockTokenRedisVo.setOpenid("test-openid");

        mockDubAnchor = new DubAnchor();
        mockDubAnchor.setId(1L);
        mockDubAnchor.setName("测试主播");
        mockDubAnchor.setVoiceName("test-voice");
        mockDubAnchor.setType("ALI_DUBBING");
        mockDubAnchor.setIsEmotion(0);
        mockDubAnchor.setEmotion("neutral");

        mockLongAnchor = new LongAnchor();
        mockLongAnchor.setId(1L);
        mockLongAnchor.setName("长音频主播");
        mockLongAnchor.setVoiceName("long-voice");
        mockLongAnchor.setType("LONG_DUBBING");

        mockSpeechDto = new SpeechSynthesizerDto();
        mockSpeechDto.setUserId(1L);
        mockSpeechDto.setAnchorId(1L);
        mockSpeechDto.setText("测试文本");
        mockSpeechDto.setVolume(50);
        mockSpeechDto.setSpeechRate(0);
        mockSpeechDto.setPitchRate(0);
    }

    @Test
    @DisplayName("测试获取主播列表 - 成功场景")
    void testList_Success() {
        // Given
        List<AnchorVo> mockAnchorList = Arrays.asList(
                createMockAnchorVo(1L, "促销男声"),
                createMockAnchorVo(2L, "知米"),
                createMockAnchorVo(3L, "促销女声"),
                createMockAnchorVo(4L, "亚群"));

        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                .thenReturn(mockAnchorList);

        // When
        ResultBean<List<AnchorVo>> result = anchorService.list(null);

        // Then
        assertNotNull(result);
        assertTrue(result.isOk());
        assertEquals(4, result.getResultData().size());
        assertEquals("促销男声", result.getResultData().get(0).getName());

        verify(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class));
    }

    @Test
    @DisplayName("测试获取长音频主播列表 - 成功场景")
    void testLongList_Success() {
        // Given
        List<AnchorVo> mockLongAnchorList = Arrays.asList(
                createMockAnchorVo(1L, "智逍遥"),
                createMockAnchorVo(2L, "智瑜"),
                createMockAnchorVo(3L, "智聆"),
                createMockAnchorVo(4L, "智美"),
                createMockAnchorVo(5L, "智云"));

        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                .thenReturn(mockLongAnchorList);

        // When
        ResultBean<List<AnchorVo>> result = anchorService.longList(null);

        // Then
        assertNotNull(result);
        assertTrue(result.isOk());
        assertEquals(5, result.getResultData().size());

        // 验证备注设置逻辑
        assertEquals("标准女声", result.getResultData().get(0).getRemark());
        assertEquals("标准女声", result.getResultData().get(1).getRemark());
        assertEquals("激昂解说", result.getResultData().get(2).getRemark());
        assertEquals("标准男声", result.getResultData().get(3).getRemark());
        assertEquals("标准女声", result.getResultData().get(4).getRemark());

        verify(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class));
    }

    @Test
    @DisplayName("测试获取主播分类列表 - 成功场景")
    void testTypeList_Success() {
        // Given
        List<String> mockScenarios = Arrays.asList("广告配音", "新闻播报", "有声读物");
        when(anchorRepository.getDistinctByUsageScenario()).thenReturn(mockScenarios);

        // When
        ResultBean<List<Map<String, String>>> result = anchorService.typeList();

        // Then
        assertNotNull(result);
        assertTrue(result.isOk());
        assertEquals(3, result.getResultData().size());
        assertEquals("广告配音", result.getResultData().get(0).get("usageScenario"));

        verify(anchorRepository).getDistinctByUsageScenario();
    }

    @Test
    @DisplayName("测试根据类型名称获取主播 - 成功场景")
    void testAnchorByTypeName_Success() {
        // Given
        String accessToken = "test-token";
        String typeName = "广告配音";
        Integer pageNumber = 0;
        Integer pageSize = 10;

        ResultBean<TokenRedisVo> tokenResult = ResultBean.successfulResult(mockTokenRedisVo);
        when(redisService.findTokenVo(accessToken)).thenReturn(mockTokenRedisVo);

        Map<String, Object> countMap = new HashMap<>();
        countMap.put("total", 5L);
        // queryForMap 需要匹配两个占位参数（userId 与 usageScenario）
        when(jdbcTemplate.queryForMap(anyString(), any())).thenReturn(countMap);

        List<AnchorVo> anchorVoList = Arrays.asList(
                createMockAnchorVo(1L, "测试主播1"),
                createMockAnchorVo(2L, "测试主播2"));
        // query(String, RowMapper, Object...) 执行数据查询
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(anchorVoList);

        // When
        ResultBean<Map<String, Object>> result = anchorService.anchorByTypeName(accessToken, typeName, pageNumber,
                pageSize);

        // Then
        assertNotNull(result);
        assertTrue(result.isOk());
        assertNotNull(result.getResultData());
        assertEquals(5L, result.getResultData().get("total"));

        verify(redisService).findTokenVo(accessToken);
        verify(jdbcTemplate).queryForMap(anyString(), any());
    }

    @Test
    @DisplayName("测试根据类型名称获取主播 - Token无效")
    void testAnchorByTypeName_InvalidToken() {
        // Given
        String accessToken = "invalid-token";
        when(redisService.findTokenVo(accessToken)).thenReturn(null);

        // When & Then
        assertThrows(NullPointerException.class, () -> {
            anchorService.anchorByTypeName(accessToken, "广告配音", 0, 10);
        });

        verify(redisService).findTokenVo(accessToken);
    }

    private AnchorVo createMockAnchorVo(Long id, String name) {
        AnchorVo vo = new AnchorVo();
        vo.setId(id);
        vo.setName(name);
        vo.setUsageScenario("测试场景");
        vo.setTypeName("测试类型");
        vo.setUrl("http://test.com/avatar.jpg");
        vo.setVoiceUrl("http://test.com/voice.mp3");
        return vo;
    }

    private Map<String, Object> createMockAnchorMap(Long id, String name) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        map.put("name", name);
        map.put("usage_scenario", "测试场景");
        map.put("type_name", "测试类型");
        map.put("url", "http://test.com/avatar.jpg");
        map.put("voice_url", "http://test.com/voice.mp3");
        map.put("is_favorited", 0);
        return map;
    }

    @Test
    @DisplayName("测试长音频主播查询 - 成功场景")
    void testLongAchor_Success() {
        // Given
        String accessToken = "test-token";
        String name = "广告配音";
        Integer pageNumber = 0;
        Integer pageSize = 10;

        when(redisService.findTokenVo(accessToken)).thenReturn(mockTokenRedisVo);

        Map<String, Object> countMap = new HashMap<>();
        countMap.put("total", 3L);
        // 与 service 中调用保持一致，需要两个占位参数
        when(jdbcTemplate.queryForMap(anyString(), any())).thenReturn(countMap);

        List<AnchorVo> anchorVoList = Arrays.asList(
                createMockAnchorVo(1L, "智逍遥"),
                createMockAnchorVo(2L, "智瑜"));
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(anchorVoList);

        // When
        ResultBean<Map<String, Object>> result = anchorService.longAchor(accessToken, name, pageNumber, pageSize);

        // Then
        assertNotNull(result);
        assertTrue(result.isOk());
        assertNotNull(result.getResultData());
        assertEquals(3L, result.getResultData().get("total"));

        verify(redisService).findTokenVo(accessToken);
        verify(jdbcTemplate).queryForMap(anyString(), any());
    }

    @Test
    @DisplayName("测试获取用户信息 - 成功场景")
    void testGetUser_Success() {
        // Given
        String accessToken = "valid-token";
        when(redisService.findTokenVo(accessToken)).thenReturn(mockTokenRedisVo);

        // When
        ResultBean<TokenRedisVo> result = anchorService.getUser(accessToken);

        // Then
        assertNotNull(result);
        assertTrue(result.isOk());
        assertEquals(mockTokenRedisVo, result.getResultData());

        verify(redisService).findTokenVo(accessToken);
    }

    @Test
    @DisplayName("测试获取用户信息 - Token无效")
    void testGetUser_InvalidToken() {
        // Given
        String accessToken = "invalid-token";
        when(redisService.findTokenVo(accessToken)).thenReturn(null);

        // When
        ResultBean<TokenRedisVo> result = anchorService.getUser(accessToken);

        // Then
        assertNotNull(result);
        assertFalse(result.isOk());
        assertEquals(StatusCode.ERROR_CODE_40001.getErrorCode(), result.getCode());

        verify(redisService).findTokenVo(accessToken);
    }

    @Test
    @DisplayName("测试试听功能 - 成功场景")
    void testAudition_Success() throws Exception {
        // Given
        Path fakePath = Paths.get("/tmp/test/audio.mp3");
        Files.createDirectories(fakePath.getParent());
        Files.write(fakePath, new byte[0]);

        try (MockedStatic<FfmpegUtil> ffmpegMock = mockStatic(FfmpegUtil.class);
                MockedStatic<AudioFileIO> audioMock = mockStatic(AudioFileIO.class)) {

            ffmpegMock.when(() -> FfmpegUtil.voloumVipAudio(any(File.class), any(File.class), anyInt()))
                    .thenAnswer(inv -> null);

            MP3AudioHeader header = mock(MP3AudioHeader.class);
            when(header.getTrackLength()).thenReturn(6);
            MP3File mp3File = mock(MP3File.class);
            when(mp3File.getAudioHeader()).thenReturn(header);
            audioMock.when(() -> AudioFileIO.read(any(File.class))).thenReturn(mp3File);

            when(dubAnchorRepository.findById(1L)).thenReturn(Optional.of(mockDubAnchor));
            when(redisService.getvoiceRepeat(1L)).thenReturn(null);
            when(redisService.getVoiceNumber(1L)).thenReturn(1);
            when(dubbingFactory.process(any(SpeechSynthesizerDto.class), any(RedisService.class)))
                    .thenReturn(fakePath.toString());

            // When
            ResultBean<Map<String, Object>> result = anchorService.audition(mockSpeechDto);

            // Then
            assertNotNull(result);
            assertTrue(result.isOk());
            assertNotNull(result.getResultData());
            assertTrue(result.getResultData().containsKey("url"));
        }

        verify(dubAnchorRepository).findById(1L);
        verify(redisService).getvoiceRepeat(1L);
        verify(redisService).getVoiceNumber(1L);
        verify(dubbingFactory).process(any(SpeechSynthesizerDto.class), any(RedisService.class));
    }

    @Test
    @DisplayName("测试试听功能 - 文本为空")
    void testAudition_EmptyText() {
        // Given
        mockSpeechDto.setText("");

        // When
        ResultBean<Map<String, Object>> result = anchorService.audition(mockSpeechDto);

        // Then
        assertNotNull(result);
        assertFalse(result.isOk());
        assertEquals(StatusCode.ERROR_CODE_10001.getErrorCode(), result.getCode());
        assertEquals("请填写需合成录音文本", result.getMsg());
    }

    @Test
    @DisplayName("测试试听功能 - 文本过长")
    void testAudition_TextTooLong() {
        // Given
        StringBuilder longText = new StringBuilder();
        for (int i = 0; i < 301; i++) {
            longText.append("a");
        }
        mockSpeechDto.setText(longText.toString());

        // When
        ResultBean<Map<String, Object>> result = anchorService.audition(mockSpeechDto);

        // Then
        assertNotNull(result);
        assertFalse(result.isOk());
        assertEquals(StatusCode.ERROR_CODE_10001.getErrorCode(), result.getCode());
        assertEquals("字数不能超过300字", result.getMsg());
    }

    @Test
    @DisplayName("测试试听功能 - 主播不存在")
    void testAudition_AnchorNotFound() {
        // Given
        when(dubAnchorRepository.findById(1L)).thenReturn(Optional.empty());

        // When
        ResultBean<Map<String, Object>> result = anchorService.audition(mockSpeechDto);

        // Then
        assertNotNull(result);
        assertFalse(result.isOk());
        assertEquals(StatusCode.ERROR_CODE_10001.getErrorCode(), result.getCode());
        assertEquals("请选择需合成录音主播", result.getMsg());

        verify(dubAnchorRepository).findById(1L);
    }

    @Test
    @DisplayName("测试试听功能 - 重复请求")
    void testAudition_DuplicateRequest() {
        // Given
        when(dubAnchorRepository.findById(1L)).thenReturn(Optional.of(mockDubAnchor));
        // 构造与方法内部处理后完全一致的 JSON 字符串，确保重复校验生效
        SpeechSynthesizerDto duplicateDtoObj = new SpeechSynthesizerDto();
        BeanUtils.copyProperties(mockSpeechDto, duplicateDtoObj);
        duplicateDtoObj.setVoice(mockDubAnchor.getVoiceName());
        duplicateDtoObj.setIsEmotion(mockDubAnchor.getIsEmotion());
        duplicateDtoObj.setEmotion(mockDubAnchor.getEmotion());
        String duplicateDto = JSON.toJSONString(duplicateDtoObj);
        when(redisService.getvoiceRepeat(1L)).thenReturn(duplicateDto);
        // 试听重复请求场景不会调用 getVoiceNumber，因此不再进行多余的 stub，避免 UnnecessaryStubbingException

        // When
        ResultBean<Map<String, Object>> result = anchorService.audition(mockSpeechDto);

        // Then
        assertNotNull(result);
        assertFalse(result.isOk());
        assertEquals(StatusCode.ERROR_CODE_20010.getErrorCode(), result.getCode());
        assertEquals(StatusCode.ERROR_CODE_20010.getErrorMsg(), result.getMsg());

        verify(dubAnchorRepository).findById(1L);
        verify(redisService).getvoiceRepeat(1L);
    }

    private Map<String, Object> createMockLongAnchorMap(Long id, String name) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        map.put("name", name);
        map.put("usage_scenario", "长音频场景");
        map.put("type_name", "长音频类型");
        map.put("url", "http://test.com/long-avatar.jpg");
        map.put("voice_url", "http://test.com/long-voice.mp3");
        map.put("is_favorited", 0);
        return map;
    }

    @Test
    @DisplayName("测试长音频合成 - 成功场景")
    void testLongCompound_Success() throws Exception {
        // Given
        when(longAnchorRepository.findById(1L)).thenReturn(Optional.of(mockLongAnchor));
        when(redisService.getvoiceRepeat(1L)).thenReturn(null);
        when(redisService.getVoiceNumber(1L)).thenReturn(1);
        when(dubbingFactory.process(any(SpeechSynthesizerDto.class), any(RedisService.class)))
                .thenReturn("/tmp/test/long-audio.mp3");

        // When
        ResultBean<Map<String, Object>> result = anchorService.longCompound(mockSpeechDto);

        // Then
        assertNotNull(result);
        assertTrue(result.isOk());
        assertNotNull(result.getResultData());
        assertTrue(result.getResultData().containsKey("url"));

        verify(longAnchorRepository).findById(1L);
        verify(redisService).getvoiceRepeat(1L);
        verify(redisService).getVoiceNumber(1L);
        verify(dubbingFactory).process(any(SpeechSynthesizerDto.class), any(RedisService.class));
    }

    @Test
    @DisplayName("测试长音频合成 - 文本过长")
    void testLongCompound_TextTooLong() {
        // Given
        StringBuilder longText = new StringBuilder();
        for (int i = 0; i < 2001; i++) {
            longText.append("a");
        }
        mockSpeechDto.setText(longText.toString());
        when(longAnchorRepository.findById(1L)).thenReturn(Optional.of(mockLongAnchor));

        // When
        ResultBean<Map<String, Object>> result = anchorService.longCompound(mockSpeechDto);

        // Then
        assertNotNull(result);
        assertFalse(result.isOk());
        assertEquals(StatusCode.ERROR_CODE_10001.getErrorCode(), result.getCode());
        assertEquals("字数不能超过2000字", result.getMsg());

        verify(longAnchorRepository).findById(1L);
    }

    @Test
    @DisplayName("测试对话合成 - 成功场景")
    void testDialog_Success() throws Exception {
        // Given
        String accessToken = "test-token";
        when(redisService.findTokenVo(accessToken)).thenReturn(mockTokenRedisVo);
        when(anchorRepository.findById(1L)).thenReturn(Optional.of(mockDubAnchor));
        when(redisService.getVoiceNumber(1L)).thenReturn(1);
        when(dubbingFactory.process(any(SpeechSynthesizerDto.class), any(RedisService.class)))
                .thenReturn("/tmp/test/dialog-audio.mp3");

        // When
        ResultBean<Map<String, Object>> result = anchorService.dialog(mockSpeechDto, accessToken);

        // Then
        assertNotNull(result);
        assertTrue(result.isOk());
        assertNotNull(result.getResultData());
        assertTrue(result.getResultData().containsKey("url"));

        verify(redisService).findTokenVo(accessToken);
        verify(anchorRepository).findById(1L);
        verify(redisService).getVoiceNumber(1L);
        verify(dubbingFactory).process(any(SpeechSynthesizerDto.class), any(RedisService.class));
    }

    @Test
    @DisplayName("测试保存作品 - 成功场景")
    void testSave_Success() throws Exception {
        // Given
        String accessToken = "test-token";
        VoiceWorkVo workVo = new VoiceWorkVo();
        workVo.setUrl("temp/test-audio.mp3");
        workVo.setTitle("测试作品");
        workVo.setAnchorId(1L);
        workVo.setText("测试文本");

        when(redisService.findTokenVo(accessToken)).thenReturn(mockTokenRedisVo);
        // 使用真实的临时文件而非 Mockito mock，避免 File.delete() 引发 NPE
        File realTempFile = new File(System.getProperty("java.io.tmpdir"), "test-audio.mp3");
        // create temp file safely
        try {
            realTempFile.createNewFile();
        } catch (IOException e) {
            fail("Failed to create temp file for test");
        }
        when(ossService.getObjectFile(null, workVo.getUrl())).thenReturn(realTempFile);
        when(ossService.putFileToName(eq(null), eq(realTempFile), eq("voice"), anyString()))
                .thenReturn("voice/1/test-audio.mp3");
        when(workRepository.save(any(VoiceWork.class))).thenAnswer(invocation -> {
            VoiceWork w = invocation.getArgument(0);
            if (w.getId() == null) {
                w.setId(1L);
            }
            return w;
        });
        when(packetRepository.save(any(VoicePacket.class))).thenAnswer(invocation -> {
            VoicePacket p = invocation.getArgument(0);
            p.setId(10L);
            return p;
        });

        // When
        ResultBean<Map<String, Object>> result = anchorService.save(workVo, accessToken);

        // Then
        assertNotNull(result);
        assertTrue(result.isOk());
        assertNotNull(result.getResultData());
        assertTrue(result.getResultData().containsKey("url"));
        assertEquals("voice/1/test-audio.mp3", result.getResultData().get("url"));

        verify(redisService).findTokenVo(accessToken);
        verify(ossService).getObjectFile(null, workVo.getUrl());
        verify(ossService).putFileToName(eq(null), eq(realTempFile), eq("voice"), anyString());
        verify(workRepository, atLeastOnce()).save(any(VoiceWork.class));
        verify(packetRepository).save(any(VoicePacket.class));
    }

    @Test
    @DisplayName("测试保存作品 - URL为空")
    void testSave_EmptyUrl() {
        // Given
        String accessToken = "test-token";
        VoiceWorkVo workVo = new VoiceWorkVo();
        workVo.setUrl("");

        // When & Then
        assertThrows(CustomException.class, () -> {
            anchorService.save(workVo, accessToken);
        });
    }

    @Test
    @DisplayName("测试AI分类列表 - 成功场景")
    void testAiClassList_Success() {
        // Given
        List<AiClass> mockAiClassList = Arrays.asList(
                createMockAiClass(1L, "商业广告", "商业广告内容"),
                createMockAiClass(2L, "新闻播报", "新闻播报内容"));
        when(aiClassRepository.findByDelStatus(0)).thenReturn(mockAiClassList);

        // When
        ResultBean<List<AiClass>> result = anchorService.aiClassList();

        // Then
        assertNotNull(result);
        assertTrue(result.isOk());
        assertEquals(2, result.getResultData().size());
        assertEquals("商业广告", result.getResultData().get(0).getTitle());

        verify(aiClassRepository).findByDelStatus(0);
    }

    @Test
    @DisplayName("测试AI风格列表 - 成功场景")
    void testAiStyleList_Success() {
        // Given
        List<AiStyle> mockAiStyleList = Arrays.asList(
                createMockAiStyle(1L, "正式", "正式风格"),
                createMockAiStyle(2L, "轻松", "轻松风格"));
        when(aiStyleRepository.findByDelStatus(0)).thenReturn(mockAiStyleList);

        // When
        ResultBean<List<AiStyle>> result = anchorService.aiStyleList();

        // Then
        assertNotNull(result);
        assertTrue(result.isOk());
        assertEquals(2, result.getResultData().size());
        assertEquals("正式", result.getResultData().get(0).getTitle());

        verify(aiStyleRepository).findByDelStatus(0);
    }

    private AiClass createMockAiClass(Long id, String title, String content) {
        AiClass aiClass = new AiClass();
        aiClass.setId(id);
        aiClass.setTitle(title);
        aiClass.setContent(content);
        aiClass.setDelStatus(0);
        aiClass.setOrderIndex(1);
        return aiClass;
    }

    private AiStyle createMockAiStyle(Long id, String title, String content) {
        AiStyle aiStyle = new AiStyle();
        aiStyle.setId(id);
        aiStyle.setTitle(title);
        aiStyle.setContent(content);
        aiStyle.setDelStatus(0);
        aiStyle.setOrderIndex(1);
        return aiStyle;
    }

    @Test
    @DisplayName("测试多人配音合并 - 成功场景")
    void testMergeAudio_Success() throws Exception {
        // Given
        String accessToken = "test-token";
        MergeVo mergeVo = new MergeVo();
        mergeVo.setText("多人配音测试");
        mergeVo.setAudio(Arrays.asList("audio1.mp3", "audio2.mp3"));
        mergeVo.setBgm("bgm.mp3");
        mergeVo.setBugRate(30);
        mergeVo.setBeforeDelay(0);
        mergeVo.setAfterDelay(0);
        mergeVo.setBgmCenterVolum(1.0);

        AnchorService spyService = spy(anchorService);
        File fakeMp3 = File.createTempFile("merge-", ".mp3");
        doReturn(fakeMp3).when(spyService).combine(anyString(), anyList(), anyString());

        when(redisService.findTokenVo(accessToken)).thenReturn(mockTokenRedisVo);
        when(redisService.getVoiceNumber(1L)).thenReturn(1);
        // 调整 stub，允许任意 File 参数以避免严格参数匹配导致的 PotentialStubbingProblem
        when(ossService.putFileOld(eq(null), any(File.class), eq("temp/" + mockTokenRedisVo.getId())))
                .thenReturn("temp/1/dummy.mp3");

        try (MockedStatic<FileUtil> fileUtilMock = mockStatic(FileUtil.class);
                MockedStatic<FfmpegUtil> ffmpegMock = mockStatic(FfmpegUtil.class)) {
            // Stub static methods that will be invoked inside mergeAudio
            fileUtilMock.when(() -> FileUtil.coverToWav(any(File.class), any(File.class))).thenAnswer(inv -> null);
            fileUtilMock.when(() -> FileUtil.coverToMp3(any(File.class), any(File.class))).thenAnswer(inv -> null);
            fileUtilMock.when(() -> FileUtil.taiseng(anyString(), anyString(), anyInt()))
                    .thenReturn(File.createTempFile("bgm-", ".wav"));
            ffmpegMock
                    .when(() -> FfmpegUtil.mixBgm(any(File.class), any(File.class), any(File.class), anyInt(), anyInt(),
                            anyDouble()))
                    .thenAnswer(inv -> null);

            // When
            ResultBean<Map<String, Object>> result = spyService.mergeAudio(mergeVo, accessToken);

            // Then
            assertNotNull(result);
            assertTrue(result.isOk());
            assertTrue(result.getResultData().containsKey("url"));
        }

        verify(redisService).findTokenVo(accessToken);
        verify(redisService).getVoiceNumber(1L);
    }

    @Test
    @DisplayName("测试上传录音 - 成功场景")
    void testUploadRecordFree_Success() throws Exception {
        // Given
        String accessToken = "test-token";
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test.wav",
                "audio/wav",
                "test audio content".getBytes());
        RecordUploadReq uploadReq = new RecordUploadReq();
        uploadReq.setName("测试录音.wav");
        uploadReq.setVolume(100);
        uploadReq.setBgm("bgm.mp3");
        uploadReq.setBugRate(30);
        uploadReq.setBeforeDelay(0);
        uploadReq.setAfterDelay(0);
        uploadReq.setBgmCenterVolum(1.0);

        when(redisService.findTokenVo(accessToken)).thenReturn(mockTokenRedisVo);
        when(userRecordRepository.findByUserIdAndArchorTypeOnToday(eq(1L), anyString()))
                .thenReturn(new ArrayList<>());

        try (MockedStatic<FileUtil> fileUtilMock = mockStatic(FileUtil.class);
                MockedStatic<FfmpegUtil> ffmpegMock = mockStatic(FfmpegUtil.class);
                MockedStatic<AudioFileIO> audioMock = mockStatic(AudioFileIO.class)) {
            fileUtilMock.when(() -> FileUtil.taiseng(anyString(), anyString(), anyInt()))
                    .thenReturn(File.createTempFile("bgm-", ".wav"));
            fileUtilMock.when(() -> FileUtil.coverToMp3Heigh(any(File.class), any(File.class))).thenAnswer(inv -> null);
            ffmpegMock.when(() -> FfmpegUtil.processAudio(any(File.class), any(File.class), any(), any(), anyInt()))
                    .thenAnswer(inv -> null);
            ffmpegMock
                    .when(() -> FfmpegUtil.mixBgm(any(File.class), any(File.class), any(File.class), anyInt(), anyInt(),
                            anyDouble()))
                    .thenAnswer(inv -> null);

            MP3AudioHeader header = mock(MP3AudioHeader.class);
            when(header.getTrackLength()).thenReturn(6);
            MP3File mp3File = mock(MP3File.class);
            when(mp3File.getAudioHeader()).thenReturn(header);
            audioMock.when(() -> AudioFileIO.read(any(File.class))).thenReturn(mp3File);

            when(ossService.putFileOld(eq(null), any(File.class), anyString())).thenReturn("temp/1/upload.mp3");
            when(userRecordRepository.save(any(UserRecord.class))).thenReturn(new UserRecord());

            // When
            ResultBean<Map<String, Object>> result = anchorService.uploadRecordFree(file, accessToken, uploadReq);

            // Then
            assertNotNull(result);
            assertTrue(result.isOk());
        }

        verify(redisService).findTokenVo(accessToken);
    }

    @Test
    @DisplayName("测试上传录音 - 文件过大")
    void testUploadRecordFree_FileTooLarge() throws Exception {
        // Given
        String accessToken = "test-token";
        // 创建一个超过7MB的文件
        byte[] largeContent = new byte[8 * 1024 * 1024]; // 8MB
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "large.wav",
                "audio/wav",
                largeContent);
        RecordUploadReq uploadReq = new RecordUploadReq();

        // When
        ResultBean<Map<String, Object>> result = anchorService.uploadRecordFree(file, accessToken, uploadReq);

        // Then
        assertNotNull(result);
        assertFalse(result.isOk());
        assertEquals("录音文件不能超过7M", result.getMsg());
    }

    @Test
    @DisplayName("测试模板分类查询 - 成功场景")
    void testTemplateType_Success() {
        // Given
        List<DubTemplateType> mockTemplateTypes = Arrays.asList(
                createMockTemplateType(1L, "商业广告"),
                createMockTemplateType(2L, "新闻播报"));
        when(templateTypeRepository.findAllByDelStatus(0)).thenReturn(mockTemplateTypes);

        // When
        ResultBean<List<DubTemplateType>> result = anchorService.templateType();

        // Then
        assertNotNull(result);
        assertTrue(result.isOk());
        assertEquals(2, result.getResultData().size());
        assertEquals("商业广告", result.getResultData().get(0).getName());

        verify(templateTypeRepository).findAllByDelStatus(0);
    }

    @Test
    @DisplayName("测试试听功能 - 合成异常")
    void testAudition_ProcessException() throws Exception {
        // Given
        when(dubAnchorRepository.findById(1L)).thenReturn(Optional.of(mockDubAnchor));
        when(redisService.getvoiceRepeat(1L)).thenReturn(null);
        when(redisService.getVoiceNumber(1L)).thenReturn(1);
        when(dubbingFactory.process(any(SpeechSynthesizerDto.class), any(RedisService.class)))
                .thenThrow(new RuntimeException("合成失败"));

        // When
        ResultBean<Map<String, Object>> result = anchorService.audition(mockSpeechDto);

        // Then
        assertNotNull(result);
        assertFalse(result.isOk());
        assertEquals(StatusCode.ERROR_CODE_10001.getErrorCode(), result.getCode());
        assertEquals("合成失败, 请更换主播尝试", result.getMsg());

        verify(dubAnchorRepository).findById(1L);
        verify(redisService).setvoiceRepeat(eq(1L), eq(""), eq(20000L));
    }

    @Test
    @DisplayName("测试长音频合成 - 合成异常")
    void testLongCompound_ProcessException() throws Exception {
        // Given
        when(longAnchorRepository.findById(1L)).thenReturn(Optional.of(mockLongAnchor));
        when(redisService.getvoiceRepeat(1L)).thenReturn(null);
        when(redisService.getVoiceNumber(1L)).thenReturn(1);
        when(dubbingFactory.process(any(SpeechSynthesizerDto.class), any(RedisService.class)))
                .thenThrow(new RuntimeException("长音频合成失败"));

        // When
        ResultBean<Map<String, Object>> result = anchorService.longCompound(mockSpeechDto);

        // Then
        assertNotNull(result);
        assertFalse(result.isOk());
        assertEquals(StatusCode.ERROR_CODE_10001.getErrorCode(), result.getCode());
        assertEquals("合成失败, 请更换主播尝试", result.getMsg());

        verify(longAnchorRepository).findById(1L);
        verify(redisService).setvoiceRepeat(eq(1L), eq(""), eq(20000L));
    }

    private DubTemplateType createMockTemplateType(Long id, String name) {
        DubTemplateType templateType = new DubTemplateType();
        templateType.setId(id);
        templateType.setName(name);
        templateType.setDelStatus(0);
        return templateType;
    }
}
