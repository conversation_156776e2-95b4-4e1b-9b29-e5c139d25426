package com.mzj.py.mservice.my.service;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.StatusCode;
import com.mzj.py.commons.StringUtils;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.mservice.my.entity.BaseAbout;
import com.mzj.py.mservice.my.entity.BaseService;
import com.mzj.py.mservice.my.entity.BaseServiceMessage;
import com.mzj.py.mservice.my.repository.BaseServiceMessageRepository;
import com.mzj.py.mservice.my.vo.BaseServiceMessageVo;
import com.mzj.py.mservice.redis.RedisService;
import com.mzj.py.mservice.wxuser.entity.WxUser;
import com.mzj.py.mservice.wxuser.repository.WxUserRepository;
import com.mzj.py.mservice.wxuser.vo.UserVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;

/**
 * 我的
 * @author: duanjinze
 * @date: 2022/11/15 15:05
 * @version: 1.0
 */
@Service
public class MyService {
    private static final Logger log = LoggerFactory.getLogger(MyService.class);

    @Autowired
    private RedisService redisService;
    @Autowired
    private WxUserRepository wxUserRepository;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private BaseServiceMessageRepository baseServiceMessageRepository;

    public ResultBean<WxUser> myInfo(String accessToken){
        TokenRedisVo tokenRedisVo = redisService.findTokenVo(accessToken);
        if (tokenRedisVo == null) {
            return ResultBean.failedResultWithMsg("token失效，请重新登录");
        }
        WxUser user = wxUserRepository.findByOpenid(tokenRedisVo.getOpenid());
        if(user.getNickname() != null){
            user.setNicknameToString(user.getNickname());
        }
        return ResultBean.successfulResult(user);
    }

    /**
     * 关于我们
     * @return
     */
    public ResultBean<BaseAbout> aboutUs(){
        String baseAboutSql = "select * from dub_base_about limit 1 ";
        List<BaseAbout> baseAbouts = jdbcTemplate.query(baseAboutSql, new BeanPropertyRowMapper<>(BaseAbout.class));
        BaseAbout baseAbout = null;
        if (!baseAbouts.isEmpty()){
            baseAbout = baseAbouts.get(0);
        }
        return ResultBean.successfulResult(baseAbout);
    }

    /**
     * 在线客服、联系方式
     * @return
     */
    public ResultBean customerService(){
        String baseServiceSql = "select * from dub_base_service limit 1 ";
        List<BaseService> baseServices = jdbcTemplate.query(baseServiceSql, new BeanPropertyRowMapper<>(BaseService.class));
        BaseService baseService = null;
        if (!baseServices.isEmpty()){
            baseService = baseServices.get(0);
        }
        return ResultBean.successfulResult(baseService);
    }

    /**
     * 客服留言
     * @param vo
     * @return
     */
    public ResultBean customerMessage(String accessToken,BaseServiceMessageVo vo){
        TokenRedisVo tokenRedisVo = redisService.findTokenVo(accessToken);
        if (tokenRedisVo == null) {
            return ResultBean.failedResultWithMsg("token失效，请重新登录");
        }
        if (StringUtils.isBlank(vo.getContent())){
            return ResultBean.failedResult("留言内容不能为空");
        }
        BaseServiceMessage baseServiceMessage = new BaseServiceMessage();
        baseServiceMessage.setUserId(tokenRedisVo.getId());
        baseServiceMessage.setContent(vo.getContent());
        baseServiceMessage.setMessageTime(new Date());
        baseServiceMessageRepository.save(baseServiceMessage);
        return ResultBean.successfulResult(true);
    }

    public ResultBean updateAvatarOrNickName(String accessToken,UserVo vo){
        TokenRedisVo tokenRedisVo = redisService.findTokenVo(accessToken);
        if (tokenRedisVo == null) {
            return ResultBean.failedResultWithMsg("token失效，请重新登录");
        }
        if (StringUtils.isNotBlank(vo.getHeadPicture())){
            //修改头像
            wxUserRepository.updateAvatar(tokenRedisVo.getOpenid(),vo.getHeadPicture());
            return ResultBean.successfulResult("修改头像成功");
        }
        if (StringUtils.isNotBlank(vo.getNickName())){
            wxUserRepository.updateNickname(tokenRedisVo.getOpenid(),vo.getNickName());
            return ResultBean.successfulResult("修改昵称成功");
        }
        return ResultBean.successfulResult(true);
    }

    @Transactional
    public ResultBean updatePhone(String accessToken, UserVo vo){
        TokenRedisVo tokenVo = redisService.findTokenVo(accessToken);
        if (tokenVo == null){
            return ResultBean.failedResultWithMsg("用户不存在");
        }

        ResultBean<Object> resultBean = new ResultBean<>();
        try {
            if (org.apache.commons.lang3.StringUtils.isBlank(vo.getPhone())) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("手机号码为空");
                return resultBean;
            }
            if (org.apache.commons.lang3.StringUtils.isBlank(vo.getCode())) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("验证码不能为空");
                return resultBean;
            }

            // 验证验证码
            String phone = redisService.getSmsCodeMobile(vo.getCode());
            if (org.apache.commons.lang3.StringUtils.isBlank(phone)) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("验证码不存在或已过期");
                return resultBean;
            }

            if (!vo.getPhone().equals(phone)) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("手机与验证码不匹配");
                return resultBean;
            }

            //查询数据库中是否已有此新手机号
            WxUser wxUser = wxUserRepository.findByPhone(vo.getPhone());
            if (wxUser != null){
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("手机号已被使用");
                return resultBean;
            }

            //根据原来的手机号修改手机号
            wxUserRepository.updatePhone(tokenVo.getOpenid(),vo.getPhone());
            //删除token
            redisTemplate.delete(accessToken);
            resultBean.setCode(StatusCode.SUCCESS_CODE_10000.getErrorCode());
            resultBean.setMsg("修改手机号成功");
        }catch (Exception e) {
            log.error(" Exception = {}", e);
            resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
            resultBean.setMsg(StatusCode.ERROR_CODE_10001.getErrorMsg());
        }
        return resultBean;
    }

}
