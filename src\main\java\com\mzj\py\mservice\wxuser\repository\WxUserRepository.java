package com.mzj.py.mservice.wxuser.repository;


import com.mzj.py.mservice.wxuser.entity.WxUser;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.transaction.annotation.Transactional;

public interface WxUserRepository extends PagingAndSortingRepository<WxUser,Long>, JpaSpecificationExecutor<WxUser> {
	/**
	 * 通过小程序openid查询用户信息
	 * @param openid
	 * @return
	 */
	WxUser findByOpenid(String openid);

	WxUser findByPhone(String phone);

	/**
	 * 修改头像
	 */
	@Transactional
	@Modifying
	@Query(value = "update dub_wechat_user set avatar = ?2 where openid = ?1 ",nativeQuery = true)
	Integer updateAvatar(String openid,String avatar);
	/**
	 * 修改昵称
	 */
	@Transactional
	@Modifying
	@Query(value = "update dub_wechat_user set nickname = ?2 where openid = ?1 ",nativeQuery = true)
	Integer updateNickname(String openid,String nickname);
	/**
	 * 修改手机号
	 */
	@Transactional
	@Modifying
	@Query(value = "update dub_wechat_user set phone = ?2 where openid = ?1 ",nativeQuery = true)
	Integer updatePhone(String openid,String phone);

    WxUser findByUnionid(String unionid);
}
