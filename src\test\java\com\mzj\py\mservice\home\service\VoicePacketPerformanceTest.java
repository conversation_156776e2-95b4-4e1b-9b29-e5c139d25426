package com.mzj.py.mservice.home.service;

import com.mzj.py.mservice.home.entity.VoicePacket;
import com.mzj.py.mservice.home.entity.VoiceWork;
import com.mzj.py.mservice.home.repository.VoicePacketRepository;
import com.mzj.py.mservice.home.repository.VoiceWorkRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * VoicePacket数据库性能测试类
 * 测试大数据量场景下的语音包相关操作性能
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("VoicePacket数据库性能测试")
class VoicePacketPerformanceTest {

    @Mock
    private VoicePacketRepository voicePacketRepository;

    @Mock
    private VoiceWorkRepository voiceWorkRepository;

    private static final int LARGE_DATA_SIZE = 1000; // 1000条数据（减少数据量以避免性能问题）
    private static final int MEDIUM_DATA_SIZE = 500; // 500条数据
    private static final int BATCH_SIZE = 100; // 批处理大小

    @BeforeEach
    void setUp() {
        // 初始化测试环境
    }

    @Test
    @DisplayName("测试语音包数据批量创建性能 - 1000条语音包数据")
    void testBatchCreateVoicePackets() {
        // Given - 准备1000条语音包数据
        List<VoicePacket> voicePackets = createLargeVoicePacketList(LARGE_DATA_SIZE);

        // Mock repository 批量保存操作
        when(voicePacketRepository.saveAll(anyList())).thenReturn(voicePackets.subList(0, BATCH_SIZE));

        // When - 执行批量创建操作并测量时间
        long startTime = System.currentTimeMillis();

        // 分批处理以模拟实际场景
        for (int i = 0; i < LARGE_DATA_SIZE; i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, LARGE_DATA_SIZE);
            List<VoicePacket> batch = voicePackets.subList(i, endIndex);
            voicePacketRepository.saveAll(batch);
        }

        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // Then - 验证性能和结果
        System.out.println("批量创建" + LARGE_DATA_SIZE + "条语音包数据耗时: " + executionTime + "ms");
        assertTrue(executionTime < 10000, "批量创建1000条语音包数据应在10秒内完成");

        // 验证批量保存被调用了正确的次数
        verify(voicePacketRepository, times(LARGE_DATA_SIZE / BATCH_SIZE)).saveAll(anyList());
    }

    @Test
    @DisplayName("测试大量语音包数据分页查询性能 - 千万级数据分页查询")
    void testLargeDataPageQuery() {
        // Given - 模拟千万级数据查询
        long totalRecords = 10000000L; // 1000万条记录
        int pageSize = 100;
        List<VoicePacket> pageData = createLargeVoicePacketList(pageSize);
        Page<VoicePacket> mockPage = new PageImpl<>(pageData,
                org.springframework.data.domain.PageRequest.of(0, pageSize), totalRecords);

        // 允许第一个参数为 null，确保匹配到具体调用
        when(voicePacketRepository.findAll(nullable(Specification.class), any(Pageable.class)))
                .thenReturn(mockPage);

        // When - 执行分页查询并测量时间
        long startTime = System.currentTimeMillis();
        Page<VoicePacket> result = voicePacketRepository.findAll(
                (Specification<VoicePacket>) null,
                org.springframework.data.domain.PageRequest.of(0, pageSize));
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // Then - 验证性能和结果
        System.out.println("千万级语音包数据分页查询耗时: " + executionTime + "ms");
        assertTrue(executionTime < 3000, "千万级数据分页查询应在3秒内完成");
        assertNotNull(result);
        assertEquals(totalRecords, result.getTotalElements());
        assertEquals(pageSize, result.getContent().size());
    }

    @Test
    @DisplayName("测试语音包数据并发查询性能 - 小规模并发场景")
    void testConcurrentVoicePacketQuery() {
        // Given - 准备并发查询参数（减少并发数量）
        int threadCount = 5; // 5个并发线程
        int queriesPerThread = 10; // 每个线程执行10次查询
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        // Mock 查询结果
        VoicePacket mockPacket = createTestVoicePacket(1L);
        when(voicePacketRepository.findByVoiceWorkId(anyLong())).thenReturn(mockPacket);
        when(voicePacketRepository.findById(anyLong())).thenReturn(Optional.of(mockPacket));

        // When - 执行并发查询并测量时间
        long startTime = System.currentTimeMillis();

        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (int i = 0; i < threadCount; i++) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                for (int j = 0; j < queriesPerThread; j++) {
                    // 交替执行不同类型的查询
                    if (j % 2 == 0) {
                        voicePacketRepository.findByVoiceWorkId((long) (j % 100 + 1));
                    } else {
                        voicePacketRepository.findById((long) (j % 100 + 1));
                    }
                }
            }, executor);
            futures.add(future);
        }

        // 等待所有查询完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // Then - 验证性能结果
        System.out.println(
                "语音包并发查询性能测试 - " + threadCount + "个线程，每线程" + queriesPerThread + "次查询，总耗时: " + executionTime + "ms");
        assertTrue(executionTime < 10000, "并发查询应在10秒内完成");

        // 验证总查询次数
        verify(voicePacketRepository, times(threadCount * queriesPerThread / 2))
                .findByVoiceWorkId(anyLong());
        verify(voicePacketRepository, times(threadCount * queriesPerThread / 2))
                .findById(anyLong());

        executor.shutdown();
    }

    @Test
    @DisplayName("测试大量语音包数据批量删除性能 - 按作品ID批量删除")
    void testBatchDeleteVoicePacketsByWorkId() {
        // Given - 准备5万个作品ID，每个作品对应多个语音包
        List<Long> workIds = IntStream.range(1, MEDIUM_DATA_SIZE + 1)
                .mapToObj(Long::valueOf)
                .collect(java.util.stream.Collectors.toList());

        // Mock 删除操作
        doNothing().when(voicePacketRepository).deleteByVoiceWorkId(anyLong());

        // When - 执行批量删除操作并测量时间
        long startTime = System.currentTimeMillis();

        // 分批删除以模拟实际场景
        for (int i = 0; i < workIds.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, workIds.size());
            List<Long> batch = workIds.subList(i, endIndex);

            for (Long workId : batch) {
                voicePacketRepository.deleteByVoiceWorkId(workId);
            }
        }

        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // Then - 验证性能和结果
        System.out.println("批量删除" + MEDIUM_DATA_SIZE + "个作品的语音包数据耗时: " + executionTime + "ms");
        assertTrue(executionTime < 30000, "批量删除5万个作品的语音包应在30秒内完成");

        // 验证删除操作被调用了正确的次数
        verify(voicePacketRepository, times(MEDIUM_DATA_SIZE)).deleteByVoiceWorkId(anyLong());
    }

    @Test
    @DisplayName("测试大量语音包数据批量更新性能 - 批量更新语音包信息")
    void testBatchUpdateVoicePackets() {
        // Given - 准备5万条待更新的语音包数据
        List<VoicePacket> updateList = createLargeVoicePacketList(MEDIUM_DATA_SIZE);

        // Mock 更新操作
        when(voicePacketRepository.save(any(VoicePacket.class)))
                .thenAnswer(invocation -> invocation.getArgument(0));

        // When - 执行批量更新操作并测量时间
        long startTime = System.currentTimeMillis();

        // 分批更新以模拟实际场景
        for (int i = 0; i < updateList.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, updateList.size());
            List<VoicePacket> batch = updateList.subList(i, endIndex);

            for (VoicePacket packet : batch) {
                // 模拟更新操作：修改文件URL和时长
                packet.setFileUrl("https://updated-oss.com/voice/updated_" + packet.getId() + ".mp3");
                packet.setVoiceTime(packet.getVoiceTime() + 10);
                voicePacketRepository.save(packet);
            }
        }

        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // Then - 验证性能和结果
        System.out.println("批量更新" + MEDIUM_DATA_SIZE + "条语音包数据耗时: " + executionTime + "ms");
        assertTrue(executionTime < 45000, "批量更新5万条语音包数据应在45秒内完成");

        // 验证更新操作被调用了正确的次数
        verify(voicePacketRepository, times(MEDIUM_DATA_SIZE)).save(any(VoicePacket.class));
    }

    @Test
    @DisplayName("测试语音包与作品关联查询性能 - 复杂关联查询")
    void testVoicePacketWorkRelationQuery() {
        // Given - 准备关联查询数据
        int workCount = 10000; // 1万个作品
        List<VoiceWork> works = createLargeVoiceWorkList(workCount);
        List<VoicePacket> packets = createLargeVoicePacketList(workCount * 3); // 每个作品3个语音包

        // Mock 关联查询
        when(voiceWorkRepository.findAll()).thenReturn(works);
        when(voicePacketRepository.findByVoiceWorkId(anyLong()))
                .thenAnswer(invocation -> {
                    Long workId = invocation.getArgument(0);
                    return packets.stream()
                            .filter(p -> p.getVoiceWorkId().equals(workId))
                            .findFirst()
                            .orElse(null);
                });

        // When - 执行关联查询并测量时间
        long startTime = System.currentTimeMillis();

        List<VoiceWork> allWorks = voiceWorkRepository.findAll();
        for (VoiceWork work : allWorks) {
            VoicePacket packet = voicePacketRepository.findByVoiceWorkId(work.getId());
            // 模拟业务处理
            if (packet != null) {
                packet.getName(); // 访问语音包信息
            }
        }

        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // Then - 验证性能和结果
        System.out.println("语音包与作品关联查询性能测试 - " + workCount + "个作品关联查询耗时: " + executionTime + "ms");
        assertTrue(executionTime < 15000, "1万个作品的关联查询应在15秒内完成");

        // 验证查询操作被调用了正确的次数
        verify(voiceWorkRepository, times(1)).findAll();
        verify(voicePacketRepository, times(workCount)).findByVoiceWorkId(anyLong());
    }

    @Test
    @DisplayName("测试语音包数据内存占用性能 - 大对象内存测试")
    void testVoicePacketMemoryUsage() {
        // Given - 准备大量语音包数据用于内存测试
        int testSize = 100000; // 10万条数据

        // When - 创建大量对象并测量内存使用
        Runtime runtime = Runtime.getRuntime();
        long memoryBefore = runtime.totalMemory() - runtime.freeMemory();

        List<VoicePacket> largeList = createLargeVoicePacketList(testSize);

        long memoryAfter = runtime.totalMemory() - runtime.freeMemory();
        long memoryUsed = memoryAfter - memoryBefore;

        // Then - 验证内存使用情况
        System.out.println("创建" + testSize + "个语音包对象内存使用: " + (memoryUsed / 1024 / 1024) + "MB");
        assertTrue(memoryUsed < 500 * 1024 * 1024, "10万个语音包对象内存使用应小于500MB");
        assertEquals(testSize, largeList.size());

        // 清理内存
        largeList.clear();
        System.gc();
    }

    // Helper methods for creating test data
    private List<VoicePacket> createLargeVoicePacketList(int size) {
        return IntStream.range(0, size)
                .mapToObj(this::createTestVoicePacket)
                .collect(java.util.stream.Collectors.toList());
    }

    private List<VoiceWork> createLargeVoiceWorkList(int size) {
        return IntStream.range(0, size)
                .mapToObj(this::createTestVoiceWork)
                .collect(java.util.stream.Collectors.toList());
    }

    private VoicePacket createTestVoicePacket(long index) {
        VoicePacket packet = new VoicePacket();
        packet.setId(index);
        packet.setVoiceWorkId((index - 1) / 3 + 1); // 每3个语音包对应1个作品
        packet.setName("性能测试语音包_" + index);
        packet.setVoiceTime((int) (index % 600 + 30)); // 30-630秒
        packet.setFileUrl("https://performance-test-oss.com/voice/packet_" + index + ".mp3");
        packet.setShopId((index % 1000) + 1); // 分布在1000个店铺中
        return packet;
    }

    private VoiceWork createTestVoiceWork(long index) {
        VoiceWork work = new VoiceWork();
        work.setId(index);
        work.setTitle("性能测试作品_" + index);
        work.setContent("这是第" + index + "个性能测试作品，用于测试语音包关联查询性能。");
        work.setUserId((index % 10000) + 1); // 分布在1万个用户中
        work.setShopId((index % 1000) + 1); // 分布在1000个店铺中
        work.setDelStatus(0);
        return work;
    }
}
