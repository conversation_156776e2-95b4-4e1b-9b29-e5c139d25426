package com.mzj.py.mservice.wxuser.vo;

public class OrderPayInfoReqVo {

	private Long orderId;// 订单编号
	private Long userId;// 用户id
	private Integer payCoin;//币
	private Integer payManageFee;//管理费
	private Integer payFee;//现金
	private Long userCouponId;//用户优惠券id
	
	public Long getOrderId() {
		return orderId;
	}
	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public Integer getPayCoin() {
		return payCoin;
	}
	public void setPayCoin(Integer payCoin) {
		this.payCoin = payCoin;
	}
	public Integer getPayManageFee() {
		return payManageFee;
	}
	public void setPayManageFee(Integer payManageFee) {
		this.payManageFee = payManageFee;
	}
	public Integer getPayFee() {
		return payFee;
	}
	public void setPayFee(Integer payFee) {
		this.payFee = payFee;
	}
	public Long getUserCouponId() {
		return userCouponId;
	}
	public void setUserCouponId(Long userCouponId) {
		this.userCouponId = userCouponId;
	}
	
	@Override
	public String toString() {
		return "OrderPayInfoReqVo [orderId=" + orderId + ", userId=" + userId + ", payCoin=" + payCoin
				+ ", payManageFee=" + payManageFee + ", payFee=" + payFee + ", userCouponId=" + userCouponId + "]";
	}
	
	
	
	
	

}
