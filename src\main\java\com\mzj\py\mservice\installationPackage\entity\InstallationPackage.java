package com.mzj.py.mservice.installationPackage.entity;


import javax.persistence.*;
import java.util.Date;


@Entity
@Table(name = "dub_installation_package")
public class InstallationPackage {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Basic
    @Column(name = "version_name")
    private String versionName;

    @Basic
    @Column(name = "remark")
    private String remark;

    @Basic
    @Column(name = "package_url")
    private String packageUrl;


    @Basic
    @Column(name = "create_time")
    private Date createTime;

    @Basic
    @Column(name = "create_user_id")
    private Long createUserId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPackageUrl() {
        return packageUrl;
    }

    public void setPackageUrl(String packageUrl) {
        this.packageUrl = packageUrl;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }
}
