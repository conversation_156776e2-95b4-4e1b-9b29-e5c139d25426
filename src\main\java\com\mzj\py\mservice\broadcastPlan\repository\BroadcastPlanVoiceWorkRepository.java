package com.mzj.py.mservice.broadcastPlan.repository;

import com.mzj.py.mservice.broadcastPlan.entity.BroadcastPlanVoiceWorkRef;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

public interface BroadcastPlanVoiceWorkRepository extends JpaRepository<BroadcastPlanVoiceWorkRef,Long>, JpaSpecificationExecutor<BroadcastPlanVoiceWorkRef> {

    @Transactional
    @Modifying
    @Query(value = "delete from dub_broadcast_plan_voice_work_ref  where plan_id = ?1 ",nativeQuery = true)
    Integer deleteByPlanId(Long planId);

}
