package com.mzj.py.commons;

import java.util.Calendar;
import java.util.HashMap;
import java.util.TimeZone;

public class CalendarUtils
{
	private static final ThreadLocal<HashMap<String, Calendar>> calendarMapHolder = new ThreadLocal<>() ;
	
	public static final Calendar getCalendar( String timezone )
	{
		HashMap<String, Calendar> map = calendarMapHolder.get() ;
		if( map == null )
		{
			map = new HashMap<String,Calendar>() ;
			calendarMapHolder.set( map );
		}
		
		Calendar cale = map.get( timezone ) ;
		if( cale != null )
			return cale ;
		
		cale = Calendar.getInstance( TimeZone.getTimeZone( timezone ) ) ;
		map.put( timezone, cale ) ;
		
		return cale ;
	}
}
