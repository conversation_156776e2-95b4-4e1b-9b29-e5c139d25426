package com.mzj.py.commons.exception;

import org.springframework.http.HttpStatus;

/**
 * 没找到资源异常
 * <AUTHOR>
 *
 */
public class NotFoundException extends ServiceException{

	private static final long serialVersionUID = 1L;
	
	public NotFoundException( String message) {
		super(HttpStatus.NOT_FOUND,ErrorCode.NOT_FOUND.code(), message);
	}
	
	public NotFoundException(int code, String message) {
		super(HttpStatus.NOT_FOUND,code, message);
	}
}
