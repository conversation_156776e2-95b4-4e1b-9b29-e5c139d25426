package com.mzj.py.mservice.my.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 客服信息
 * @author: duanjinze
 * @date: 2022/11/11 17:02
 * @version: 1.0
 */
@Entity
@Table(name = "dub_base_service_message")
public class BaseServiceMessage {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户id
     */
    @Basic
    @Column(name = "user_id")
    private Long userId;

    /**
     * 内容
     */
    @Basic
    @Column(name = "content")
    private String content;

    /**
     * 时间
     */
    @Basic
    @Column(name = "message_time")
    private Date messageTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Date getMessageTime() {
        return messageTime;
    }

    public void setMessageTime(Date messageTime) {
        this.messageTime = messageTime;
    }
}
