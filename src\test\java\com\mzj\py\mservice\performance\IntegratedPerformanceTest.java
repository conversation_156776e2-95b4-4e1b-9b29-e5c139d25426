package com.mzj.py.mservice.performance;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.commons.exception.CustomException;
import com.mzj.py.mservice.device.service.DeviceService;
import com.mzj.py.mservice.device.vo.DeviceVoiceAddParam;
import com.mzj.py.mservice.home.entity.DeviceVoice;
import com.mzj.py.mservice.home.entity.VoicePacket;
import com.mzj.py.mservice.home.entity.VoiceWork;
import com.mzj.py.mservice.home.repository.DeviceVoiceRepository;
import com.mzj.py.mservice.home.repository.VoicePacketRepository;
import com.mzj.py.mservice.home.repository.VoiceWorkRepository;
import com.mzj.py.mservice.home.service.AnchorService;
import com.mzj.py.mservice.home.vo.VoiceWorkVo;
import com.mzj.py.mservice.oss.service.OSSService;
import com.mzj.py.mservice.redis.RedisService;
import com.mzj.py.mservice.work.service.WorkService;
import com.mzj.py.mservice.work.vo.WorkInfoVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 综合性能测试类
 * 测试作品、语音包和设备音频之间的关联操作性能
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("综合数据库性能测试")
class IntegratedPerformanceTest {

    @Mock
    private WorkService workService;

    @Mock
    private DeviceService deviceService;

    @Mock
    private AnchorService anchorService;

    @Mock
    private VoiceWorkRepository workRepository;

    @Mock
    private VoicePacketRepository packetRepository;

    @Mock
    private DeviceVoiceRepository deviceVoiceRepository;

    @Mock
    private RedisService redisService;

    @Mock
    private OSSService ossService;

    @Mock
    private JdbcTemplate jdbcTemplate;

    private TokenRedisVo mockUser;
    private static final int LARGE_DATA_SIZE = 1000; // 1000条数据（减少数据量以避免性能问题）
    private static final int MASSIVE_DATA_SIZE = 5000; // 5000条数据

    @BeforeEach
    void setUp() {
        // 初始化测试用户
        mockUser = new TokenRedisVo();
        mockUser.setId(1L);
        mockUser.setPhone("13800138000");
        mockUser.setNicknames("综合性能测试用户");
    }

    @Test
    @DisplayName("测试作品到设备音频完整流程性能 - 端到端大数据量测试")
    void testCompleteWorkToDeviceVoiceFlow() {
        // Given - 准备完整流程的大数据量测试
        int workCount = 50000; // 5万个作品
        int deviceCount = 10000; // 1万个设备

        // 准备作品数据
        List<VoiceWork> works = createLargeVoiceWorkList(workCount);
        List<VoicePacket> packets = createLargeVoicePacketList(workCount);
        List<DeviceVoice> deviceVoices = createLargeDeviceVoiceList(workCount * deviceCount / 100); // 每100个作品对应1个设备

        // Mock 各个服务的操作
        try {
            doNothing().when(workService).save(any(WorkInfoVo.class));
        } catch (CustomException e) {
            // Mock不会抛出异常
        }
        when(deviceService.addDeviceVoice(any(DeviceVoiceAddParam.class)))
                .thenReturn(ResultBean.successfulResult(true));

        // When - 执行完整流程并测量时间
        long startTime = System.currentTimeMillis();

        // 模拟完整的业务流程：创建作品 -> 生成语音包 -> 分发到设备
        for (int i = 0; i < 1000; i++) { // 测试1000个完整流程
            try {
                // 1. 创建作品
                WorkInfoVo workInfo = createTestWorkInfoVo((long) i);
                workService.save(workInfo);

                // 2. 创建设备音频
                DeviceVoiceAddParam deviceParam = createTestDeviceVoiceAddParam((long) i);
                deviceService.addDeviceVoice(deviceParam);
            } catch (CustomException e) {
                // 忽略测试中的异常
            }
        }

        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // Then - 验证完整流程性能
        System.out.println("作品到设备音频完整流程1000次操作耗时: " + executionTime + "ms");
        assertTrue(executionTime < 60000, "完整流程应在60秒内完成");

        try {
            verify(workService, times(1000)).save(any(WorkInfoVo.class));
        } catch (CustomException e) {
            // Mock不会抛出异常
        }
        verify(deviceService, times(1000)).addDeviceVoice(any(DeviceVoiceAddParam.class));
    }

    @Test
    @DisplayName("测试大规模数据关联查询性能 - 多表联合查询")
    void testMassiveDataJoinQuery() {
        // Given - 准备大规模关联查询
        long totalWorks = 5000000L; // 500万作品
        long totalPackets = 5000000L; // 500万语音包
        long totalDeviceVoices = 50000000L; // 5000万设备音频

        // Mock 复杂关联查询结果
        when(jdbcTemplate.queryForObject(contains("COUNT"), eq(Long.class)))
                .thenReturn(totalWorks, totalPackets, totalDeviceVoices);

        // Mock 关联查询结果
        List<Object[]> joinResults = IntStream.range(0, 1000)
                .mapToObj(i -> new Object[] {
                        (long) i, "作品_" + i, "语音包_" + i, "设备音频_" + i,
                        "https://test.com/work_" + i + ".mp3", 60 + i % 300
                })
                .collect(java.util.stream.Collectors.toList());

        when(jdbcTemplate.query(anyString(), any(org.springframework.jdbc.core.RowMapper.class), any()))
                .thenReturn(joinResults);

        // When - 执行大规模关联查询并测量时间
        long startTime = System.currentTimeMillis();

        // 模拟复杂的多表关联查询
        for (int i = 0; i < 100; i++) {
            // 统计查询
            Long workCount = jdbcTemplate.queryForObject(
                    "SELECT COUNT(*) FROM dub_voice_work WHERE del_status = 0", Long.class);

            Long packetCount = jdbcTemplate.queryForObject(
                    "SELECT COUNT(*) FROM dub_voice_packet", Long.class);

            Long deviceVoiceCount = jdbcTemplate.queryForObject(
                    "SELECT COUNT(*) FROM dub_device_voice WHERE del_status = 0", Long.class);

            // 关联查询
            List<Object[]> results = jdbcTemplate.query(
                    "SELECT w.id, w.title, p.name, dv.title, p.file_url, p.voice_time " +
                            "FROM dub_voice_work w " +
                            "LEFT JOIN dub_voice_packet p ON w.id = p.voice_work_id " +
                            "LEFT JOIN dub_device_voice dv ON w.id = dv.voice_work_id " +
                            "WHERE w.shop_id = ? AND w.del_status = 0 " +
                            "LIMIT 1000",
                    (rs, rowNum) -> new Object[] {
                            rs.getLong("id"), rs.getString("title"), rs.getString("name"),
                            rs.getString("title"), rs.getString("file_url"), rs.getInt("voice_time")
                    },
                    1L);

            // 验证查询结果
            assertNotNull(workCount);
            assertNotNull(packetCount);
            assertNotNull(deviceVoiceCount);
            assertNotNull(results);
            assertEquals(1000, results.size());
        }

        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // Then - 验证关联查询性能
        System.out.println("大规模数据关联查询100次耗时: " + executionTime + "ms");
        assertTrue(executionTime < 45000, "大规模关联查询应在45秒内完成");

        verify(jdbcTemplate, times(300)).queryForObject(anyString(), eq(Long.class));
        verify(jdbcTemplate, times(100)).query(anyString(), any(org.springframework.jdbc.core.RowMapper.class), any());
    }

    @Test
    @DisplayName("测试并发混合操作性能 - 小规模读写混合测试")
    void testHighConcurrencyMixedOperations() {
        // Given - 准备小规模并发混合操作
        int threadCount = 5; // 5个并发线程
        int operationsPerThread = 10; // 每个线程10次操作
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        // Mock 各种操作
        when(workService.info(anyLong())).thenReturn(ResultBean.successfulResult(createTestWorkInfoVo(1L)));
        try {
            doNothing().when(workService).save(any(WorkInfoVo.class));
        } catch (CustomException e) {
            // Mock不会抛出异常
        }
        when(deviceService.addDeviceVoice(any(DeviceVoiceAddParam.class)))
                .thenReturn(ResultBean.successfulResult(true));
        when(packetRepository.findByVoiceWorkId(anyLong())).thenReturn(createTestVoicePacket(1L));

        // When - 执行高并发混合操作并测量时间
        long startTime = System.currentTimeMillis();

        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    int operation = (threadIndex * operationsPerThread + j) % 4;

                    try {
                        switch (operation) {
                            case 0: // 查询作品
                                workService.info((long) (j % 1000 + 1));
                                break;
                            case 1: // 创建作品
                                WorkInfoVo workInfo = createTestWorkInfoVo((long) (threadIndex * 1000 + j));
                                workService.save(workInfo);
                                break;
                            case 2: // 创建设备音频
                                DeviceVoiceAddParam deviceParam = createTestDeviceVoiceAddParam(
                                        (long) (threadIndex * 1000 + j));
                                deviceService.addDeviceVoice(deviceParam);
                                break;
                            case 3: // 查询语音包
                                packetRepository.findByVoiceWorkId((long) (j % 1000 + 1));
                                break;
                        }
                    } catch (Exception e) {
                        // 忽略测试中的异常
                    }
                }
            }, executor);
            futures.add(future);
        }

        // 等待所有操作完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // Then - 验证高并发性能
        System.out.println(
                "高并发混合操作 - " + threadCount + "个线程，每线程" + operationsPerThread + "次操作，总耗时: " + executionTime + "ms");
        assertTrue(executionTime < 30000, "并发混合操作应在30秒内完成");

        // 验证各种操作的调用次数
        int totalOperations = threadCount * operationsPerThread;
        // 计算各操作的期望调用次数，分配规则与取模逻辑保持一致
        int baseCount = totalOperations / 4;
        int remainder = totalOperations % 4;
        int infoExpected = baseCount + (remainder > 0 ? 1 : 0); // 对应 operation == 0
        int saveExpected = baseCount + (remainder > 1 ? 1 : 0); // 对应 operation == 1
        int addExpected = baseCount + (remainder > 2 ? 1 : 0); // 对应 operation == 2
        int findExpected = baseCount; // 对应 operation == 3

        verify(workService, times(infoExpected)).info(anyLong());
        try {
            verify(workService, times(saveExpected)).save(any(WorkInfoVo.class));
        } catch (CustomException e) {
            // Mock不会抛出异常
        }
        verify(deviceService, times(addExpected)).addDeviceVoice(any(DeviceVoiceAddParam.class));
        verify(packetRepository, times(findExpected)).findByVoiceWorkId(anyLong());

        executor.shutdown();
    }

    @Test
    @DisplayName("测试数据一致性压力测试 - 大量并发写入一致性验证")
    void testDataConsistencyStressTest() {
        // Given - 准备数据一致性测试
        int concurrentWrites = 1000; // 1000个并发写入
        ExecutorService executor = Executors.newFixedThreadPool(50);

        // Mock 数据库操作
        when(workRepository.save(any(VoiceWork.class))).thenAnswer(invocation -> {
            VoiceWork work = invocation.getArgument(0);
            work.setId(System.currentTimeMillis()); // 模拟数据库生成ID
            return work;
        });

        when(packetRepository.save(any(VoicePacket.class))).thenAnswer(invocation -> {
            VoicePacket packet = invocation.getArgument(0);
            packet.setId(System.currentTimeMillis()); // 模拟数据库生成ID
            return packet;
        });

        // When - 执行并发写入测试
        long startTime = System.currentTimeMillis();

        List<CompletableFuture<Long>> futures = new ArrayList<>();
        for (int i = 0; i < concurrentWrites; i++) {
            final int index = i;
            CompletableFuture<Long> future = CompletableFuture.supplyAsync(() -> {
                // 模拟事务性操作：同时创建作品和语音包
                VoiceWork work = createTestVoiceWork((long) index);
                VoiceWork savedWork = workRepository.save(work);

                VoicePacket packet = createTestVoicePacket((long) index);
                packet.setVoiceWorkId(savedWork.getId());
                packetRepository.save(packet);

                return savedWork.getId();
            }, executor);
            futures.add(future);
        }

        // 等待所有写入完成
        List<Long> workIds = futures.stream()
                .map(CompletableFuture::join)
                .collect(java.util.stream.Collectors.toList());

        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // Then - 验证数据一致性和性能
        System.out.println("数据一致性压力测试 - " + concurrentWrites + "个并发写入耗时: " + executionTime + "ms");
        assertTrue(executionTime < 60000, "并发写入应在60秒内完成");

        // 验证所有写入都成功
        assertEquals(concurrentWrites, workIds.size());
        assertTrue(workIds.stream().allMatch(id -> id != null && id > 0));

        // 验证数据库操作次数
        verify(workRepository, times(concurrentWrites)).save(any(VoiceWork.class));
        verify(packetRepository, times(concurrentWrites)).save(any(VoicePacket.class));

        executor.shutdown();
    }

    // Helper methods for creating test data
    private List<VoiceWork> createLargeVoiceWorkList(int size) {
        return IntStream.range(0, size)
                .mapToObj(this::createTestVoiceWork)
                .collect(java.util.stream.Collectors.toList());
    }

    private List<VoicePacket> createLargeVoicePacketList(int size) {
        return IntStream.range(0, size)
                .mapToObj(this::createTestVoicePacket)
                .collect(java.util.stream.Collectors.toList());
    }

    private List<DeviceVoice> createLargeDeviceVoiceList(int size) {
        return IntStream.range(0, size)
                .mapToObj(this::createTestDeviceVoice)
                .collect(java.util.stream.Collectors.toList());
    }

    private VoiceWork createTestVoiceWork(long index) {
        VoiceWork work = new VoiceWork();
        work.setId(index);
        work.setTitle("综合性能测试作品_" + index);
        work.setContent("这是第" + index + "个综合性能测试作品的内容。");
        work.setUserId((index % 10000) + 1);
        work.setShopId((index % 1000) + 1);
        work.setVoiceId((index % 100) + 1);
        work.setAnchorId((index % 50) + 1);
        work.setVoiceTime((int) (index % 300 + 30));
        work.setSpeed((int) (index % 100 + 1));
        work.setVolume((int) (index % 100 + 1));
        work.setPitch((int) (index % 100 + 1));
        work.setDelStatus(0);
        work.setCreateTime(new Date());
        return work;
    }

    private VoicePacket createTestVoicePacket(long index) {
        VoicePacket packet = new VoicePacket();
        packet.setId(index);
        packet.setVoiceWorkId(index);
        packet.setName("综合性能测试语音包_" + index);
        packet.setVoiceTime((int) (index % 300 + 30));
        packet.setFileUrl("https://integrated-test-oss.com/voice/packet_" + index + ".mp3");
        packet.setShopId((index % 1000) + 1);
        return packet;
    }

    private DeviceVoice createTestDeviceVoice(long index) {
        DeviceVoice voice = new DeviceVoice();
        voice.setId(index);
        voice.setDeviceId((index % 10000) + 1);
        voice.setVoiceWorkId((index % 100000) + 1);
        voice.setTitle("综合性能测试设备音频_" + index);
        voice.setContent("这是第" + index + "个综合性能测试设备音频的内容。");
        voice.setVoiceUrl("https://integrated-test-oss.com/device-voice/voice_" + index + ".mp3");
        voice.setSpeed((int) (index % 100 + 1));
        voice.setVolume((int) (index % 100 + 1));
        voice.setPitch((int) (index % 100 + 1));
        voice.setVoiceTime((int) (index % 300 + 30));
        voice.setType(1);
        voice.setDelStatus(0);
        return voice;
    }

    private WorkInfoVo createTestWorkInfoVo(long index) {
        WorkInfoVo workInfo = new WorkInfoVo();
        workInfo.setId(index);
        workInfo.setTitle("综合性能测试作品信息_" + index);
        workInfo.setContent("这是第" + index + "个综合性能测试作品信息的内容。");
        workInfo.setFileUrl("https://integrated-test-oss.com/work/info_" + index + ".mp3");
        workInfo.setUserId((index % 10000) + 1);
        workInfo.setShopId((index % 1000) + 1);
        workInfo.setVoiceTime((int) (index % 300 + 30));
        workInfo.setSpeed((int) (index % 100 + 1));
        workInfo.setVolume((int) (index % 100 + 1));
        workInfo.setPitch((int) (index % 100 + 1));
        return workInfo;
    }

    private DeviceVoiceAddParam createTestDeviceVoiceAddParam(long index) {
        DeviceVoiceAddParam param = new DeviceVoiceAddParam();
        param.setDeviceIds(java.util.Arrays.asList((index % 10000) + 1));
        param.setTitle("综合性能测试设备音频参数_" + index);
        param.setContent("这是第" + index + "个综合性能测试设备音频参数的内容。");
        param.setVoiceId((index % 100) + 1);
        param.setBackgroundMusicId((index % 20) + 1);
        param.setBackgroundMusicVolume((int) (index % 100 + 1));
        param.setShopId((index % 1000) + 1);
        param.setUserId((index % 10000) + 1);
        param.setUrl("https://integrated-test-oss.com/device-param/param_" + index + ".mp3");
        param.setTime((int) (index % 300 + 30));
        param.setSpeed((int) (index % 100 + 1));
        param.setVolume((int) (index % 100 + 1));
        param.setPitch((int) (index % 100 + 1));
        param.setType(1);
        return param;
    }
}
