package com.mzj.py.mservice.device.vo;

import lombok.Data;

import java.util.Map;

/**
 * @author: duanjinze
 * @date: 2022/11/11 9:29
 * @version: 1.0
 */
@Data
public class DetailVo {
    /**
     * 设备表id
     */
    private Long deviceId;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 设备id（sn）
     */
    private String sn;

    /**
     * 在线状态 0离线 1在线
     */
    private Integer status;

    private Map<String,Object> map;
}
