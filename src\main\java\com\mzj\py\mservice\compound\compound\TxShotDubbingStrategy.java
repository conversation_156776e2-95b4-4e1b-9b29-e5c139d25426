package com.mzj.py.mservice.compound.compound;

import com.alibaba.nls.client.protocol.SampleRateEnum;
import com.mzj.py.commons.StringUtils;
import com.mzj.py.commons.enums.PlatFormStatusEnum;
import com.mzj.py.commons.enums.PlatFormTypeEnum;
import com.mzj.py.mservice.compound.entity.SpeechSynthesizerDto;
import com.mzj.py.mservice.compound.utils.FfmpegUtil;
import com.mzj.py.mservice.compound.utils.FileUtil;
import com.mzj.py.mservice.compound.utils.LineInsertUtils;
import com.mzj.py.mservice.home.entity.PlatformConfig;
import com.mzj.py.mservice.home.repository.PlatformConfigRepository;
import com.mzj.py.mservice.redis.RedisService;
import com.tencent.SpeechClient;
import com.tencent.tts.model.SpeechSynthesisRequest;
import com.tencent.tts.model.SpeechSynthesisResponse;
import com.tencent.tts.service.SpeechSynthesisListener;
import com.tencent.tts.service.SpeechSynthesizer;
import com.tencent.tts.utils.Ttsutils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-03-09-13:49
 */
@Service
public class TxShotDubbingStrategy implements DubbingStrategy {

    private static final Logger logger = LoggerFactory.getLogger(TxShotDubbingStrategy.class);
    @Autowired
    private PlatformConfigRepository platformConfigRepository;

    @Override
    public String process(SpeechSynthesizerDto dto, RedisService redisService) throws Exception {
        List<PlatformConfig> platformConfigs = platformConfigRepository.findByPlatformTypeAndStatus(PlatFormTypeEnum.TX_DUBBING.name(),
                PlatFormStatusEnum.ENABLED.ordinal());
        PlatformConfig platformConfig = platformConfigs.get(0);
        String appId = platformConfig.getAppId();
        String secretId = platformConfig.getAccessKey();
        String secretKey = platformConfig.getSecretKey();
        Double volume = LineInsertUtils.mapRange(dto.getVolume(), 0,
                100, -10, 10);
        Double speechRate = LineInsertUtils.mapRange(dto.getSpeechRate() - 250, -500,
                500, -2, 6);
        String text = dto.getText().replaceAll("\\s*", "");
        text = text.replace("\u00A0", "");
        if (dto.getText().indexOf("<break") != -1) {
            dto.setText("<speak>" + dto.getText() + "</speak>");
        }
        SpeechSynthesisRequest request = SpeechSynthesisRequest.initialize();
        request.setCodec("mp3");
        request.setSampleRate(SampleRateEnum.SAMPLE_RATE_16K.value);
        request.setVolume(Integer.parseInt(String.valueOf(Math.round(volume))));
        request.setSpeed(Float.parseFloat(String.valueOf(speechRate)));
        request.setVoiceType(Integer.valueOf(dto.getVoice()));
        if (dto.getIsEmotion() == 1) {
            request.setEmotionCategory(dto.getEmotion());
            request.setEmotionIntensity(145);
        }
        //使用客户端client创建语音合成实例
        SpeechClient client = SpeechClient.newInstance(appId, secretId, secretKey);
        File templateFile = new File(dto.getUrl() + FileUtil.getFileNewName(".mp3"));
        SpeechSynthesizer speechSynthesizer = client.newSpeechSynthesizer(request, getSynthesizerListener(templateFile));
        speechSynthesizer.synthesis(dto.getText());
        if (!StringUtils.isEmpty(dto.getBgm())) {
            String bgmFileName = dto.getUrl() + FileUtil.getFileNewName(".wav");
            File bgmFile = FileUtil.taiseng(dto.getBgm(), bgmFileName, dto.getBugRate());
            File outFile = new File(dto.getUrl() + FileUtil.getFileNewName(".wav"));
            FfmpegUtil.mixBgm(templateFile, bgmFile, outFile, dto.getBeforeDelay(), dto.getAfterDelay(), dto.getBgmCenterVolum());
            File file = new File(dto.getUrl() + FileUtil.getFileNewName(".mp3"));
            if (dto.getIsHeighVoice() == 1) {
                FileUtil.coverToMp3Heigh(outFile, file);
            } else {
                FileUtil.coverToMp3(outFile, file);
            }
            return file.getAbsolutePath();
        } else {
            File file = new File(dto.getUrl() + FileUtil.getFileNewName(".mp3"));
            if (dto.getIsHeighVoice() == 1) {
                FileUtil.coverToMp3Heigh(templateFile, file);
            } else {
                FileUtil.coverToMp3(templateFile, file);
            }
            return file.getAbsolutePath();
        }
    }

    public SpeechSynthesisListener getSynthesizerListener(File tempFile) {
        SpeechSynthesisListener listener = null;
        try {
            listener = new SpeechSynthesisListener() {
                private AtomicInteger sessionId = new AtomicInteger(0);

                @Override
                public void onComplete(SpeechSynthesisResponse response) {
                    if (response.getSuccess()) {
                        try {
                            Ttsutils.saveResponseToFile(response.getAudio(), tempFile.getPath());
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                }


                //语音合成的语音二进制数据
                @Override
                public void onMessage(byte[] data) {
                    sessionId.incrementAndGet();
                }


                @Override
                public void onFail(SpeechSynthesisResponse response) {
                    logger.info("腾讯短文本合成错误:{}", response.getMessage().toString());
                }
            };
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return listener;
    }

}
