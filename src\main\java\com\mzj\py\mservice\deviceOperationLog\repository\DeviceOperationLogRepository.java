package com.mzj.py.mservice.deviceOperationLog.repository;

import com.mzj.py.mservice.deviceOperationLog.entity.DeviceOperationLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface DeviceOperationLogRepository extends JpaRepository<DeviceOperationLog, Long>, JpaSpecificationExecutor<DeviceOperationLog> {

    List<DeviceOperationLog> findByDeviceIdAndStatus(Long deviceId, Integer status);
}
