package com.mzj.py.mservice.paidPackages.controller;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.mservice.paidPackages.service.PaidPackagesService;
import com.mzj.py.mservice.paidPackages.vo.dto.PaidPackagesAddDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * 流量包管理
 */
@Controller
@RequestMapping("/mini/paidPackages")
public class PaidPackagesMiniController {

    @Autowired
    private PaidPackagesService paidPackagesService;


    /**
     * 流量包列表
     * @param accessToken
     * @param pageSize
     * @param pageNumber
     * @return
     */
    @GetMapping
    @ResponseBody
    public ResultBean list(@RequestHeader String accessToken, @RequestParam(name = "pageSize",defaultValue = "10") Integer pageSize,
                           @RequestParam(name = "pageNumber",defaultValue = "0") Integer pageNumber){
        return paidPackagesService.list(accessToken, pageSize, pageNumber);
    }

    /**
     * 详情
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    @ResponseBody
    public ResultBean getInfo(@PathVariable Long id){
        return paidPackagesService.getInfo(id);
    }

    /**
     * 添加或修改
     * @param accessToken
     * @param vo
     * @return
     */
//    @PostMapping
//    @ResponseBody
//    public ResultBean addOrUpdate(@RequestHeader String accessToken,@RequestBody PaidPackagesAddDto vo){
//        return paidPackagesService.addOrUpdate(vo,accessToken);
//    }

    /**
     * 删除
     * @param id
     * @return
     */
//    @DeleteMapping("/{id}")
//    @ResponseBody
//    public ResultBean delete(@PathVariable Long id){
//        return paidPackagesService.delete(id);
//    }

}
