package com.mzj.py.commons.exception;

/**
 * 定义通用错误码， 目前是与 HttpStatus 相同。 具体的业务接口返回的错误码可以在项目中定义
 * <AUTHOR>
 *
 */
public enum ErrorCode {

	OK(200, "OK"), 
	BAD_REQUEST(400, "Bad Request"), 
	UNAUTHORIZED(401, "Unauthorized"), 
	FORBIDDEN(403, "Forbidden"),
	NOT_FOUND(404, "Not Found"), 
	REQUEST_TIMEOUT(408, "Request Timeout"), 
	TOO_MANY_REQUESTS(429, "Too Many Requests"),
	INTERNAL_SERVER_ERROR(500, "Internal Server Error"),

	FAIL(-1, "System error"), 
	UNEXPECTED(-99, "Unexpected error"),

	//微信相关错误码
	WECHAT_ACCESS_TOKEN_ERROR(40001,"Error Wechat Access Token"),
	WECHAT_USER_INFO_ERROR(40002,"Error Wechat UserInfo");

	
	private Integer code;
	private String value;

	private ErrorCode(Integer code, String value) {
		this.code = code;
		this.value = value;
	}

	public Integer code() {
		return code;
	}

	public String value() {
		return value;
	}

}
