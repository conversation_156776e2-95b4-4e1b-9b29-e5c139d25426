package com.mzj.py.mservice.order.component;

import com.alibaba.fastjson.JSONObject;
import com.mzj.py.commons.JsonUtil;
import com.mzj.py.config.wxpay.WxPayConfig;
import com.mzj.py.mservice.order.vo.PayInfoVO;
import com.wechat.pay.java.service.payments.jsapi.JsapiServiceExtension;
import com.wechat.pay.java.service.payments.jsapi.model.*;
import com.wechat.pay.java.service.payments.model.Transaction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 小滴课堂,愿景：让技术不再难学
 *
 * @Description  微信支付
 * <AUTHOR>
 * @Remark 有问题直接联系我，源码-笔记-技术交流群
 * @Version 1.0
 **/

@Service
public class WechatPayStrategy implements  PayStrategy{
    private Logger LOG = LoggerFactory.getLogger(WechatPayStrategy.class);
    @Resource
    private WxPayConfig payConfig;

    @Resource
    private JsapiServiceExtension  jsapiService;

    @Override
    public String unifiedOrder(PayInfoVO payInfoVO) {

        //过期时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");
        //支付订单过期时间
        String timeExpire = sdf.format(new Date(System.currentTimeMillis() + payInfoVO.getOrderPayTimeoutMills()));
        JSONObject attach=new JSONObject();
        attach.put("open_id",payInfoVO.getOpenid());
        attach.put("user_id",payInfoVO.getUserId());
        Payer payer=new Payer();
        payer.setOpenid(payInfoVO.getOpenid());
        //数据库存储是double比如，100.99元，微信支付需要以分为单位
        PrepayRequest request = new PrepayRequest();
        Amount amount = new Amount();
        amount.setTotal(payInfoVO.getPayAmount().multiply(BigDecimal.valueOf(100)).intValue());
        request.setAmount(amount);
        request.setAppid(payConfig.getAppId());
        request.setMchid(payConfig.getMerchantId());
        // 微信接口校验商品描述(description)必填，若调用方未传递则回退到标题或默认值
        String description = payInfoVO.getDescription();
        if (description == null || description.trim().isEmpty()) {
            description = payInfoVO.getTitle() != null ? payInfoVO.getTitle() : "订单支付";
        }
        request.setDescription(description);
        request.setNotifyUrl(payConfig.getNotifyUrl());
        LOG.info("微信支付回调地址：{}",payConfig.getNotifyUrl());
        request.setOutTradeNo(payInfoVO.getOutTradeNo());
        request.setTimeExpire(timeExpire);
        request.setAttach(attach.toJSONString());
        request.setPayer(payer);
        PrepayWithRequestPaymentResponse response = jsapiService.prepayWithRequestPayment(request);
        JSONObject result=new JSONObject();
        result.put("timeStamp",response.getTimeStamp());
        result.put("nonceStr",response.getNonceStr());
        result.put("package",response.getPackageVal());
        result.put("signType",response.getSignType());
        result.put("paySign",response.getPaySign());
        return JsonUtil.obj2Json(result);
    }

    @Override
    public String refund(PayInfoVO payInfoVO) {
        return null;
    }

    /**
     * 微信支付查询订单状态
     * @param payInfoVO
     * @return
     */
    @Override
    public String queryPayStatus(PayInfoVO payInfoVO) {

        try{
            String outTradeNo = payInfoVO.getOutTradeNo();
            QueryOrderByOutTradeNoRequest queryRequest=new QueryOrderByOutTradeNoRequest();
            queryRequest.setMchid(payConfig.getMerchantId());
            queryRequest.setOutTradeNo(outTradeNo);
            Transaction transaction = jsapiService.queryOrderByOutTradeNo(queryRequest);
            JSONObject result=new JSONObject();
            result.put("success_time",transaction.getSuccessTime());
            result.put("trade_state",transaction.getTradeState());
            result.put("transaction_id",transaction.getTransactionId());
            result.put("trade_state",transaction.getTradeState());
            return JSONObject.toJSONString(result);
        }catch (Exception e){
            LOG.error("微信支付响应异常:{}",e);
            return "";
        }
    }

    @Override
    public String closeOrder(PayInfoVO payInfoVO) {

        try{
            String outTradeNo = payInfoVO.getOutTradeNo();
            CloseOrderRequest closeOrderRequest=new CloseOrderRequest();
            closeOrderRequest.setMchid(payConfig.getMerchantId());
            closeOrderRequest.setOutTradeNo(outTradeNo);
            jsapiService.closeOrder(closeOrderRequest);
            return "CLOSE_SUCCESS";
        }catch (Exception e){
            LOG.error("微信支付响应异常:{}",e);
            throw new RuntimeException(e);
        }
    }
}
