package com.mzj.py.mqtt;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date: 2025/3/20
 * @description:
 *      * {
 *      *  “cmd”:”addCase”,
 *      *  ”parm”: ""
 *      *  ”period":
 *      *      [
 *      *          {
 *      *              "caseDay":"mon,tue,thur",//周几配置 使用英文简写标识或者简单字符0-6也可以
 *      *              "startTime":"09:05",
 *      *              "endTime":"10:10",
 *      *              "ringid","151",
 *      *              “caseid”:”0”
 *      *          }
 *      *      ]
 *      * }
 */
@Data
public class CaseParams {
    private String cmd;
    private String parm;
    private String timeStamp;
    private List<CasePeriod> period;
}

@Data
class CasePeriod {
    private String caseDay;
    private String startTime;
    private String endTime;
    private String ringid;
    private String caseid;
}
