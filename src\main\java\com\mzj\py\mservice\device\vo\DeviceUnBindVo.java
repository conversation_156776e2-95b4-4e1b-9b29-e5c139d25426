package com.mzj.py.mservice.device.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 设备表
 * @author: duanjinze
 * @date: 2022/11/10 13:35
 * @version: 1.0
 */
@Data
public class DeviceUnBindVo {

    @NotNull(message = "设备id不能为空")
    private Long id;

    @NotNull(message = "绑定门店不能为空")
    private Long shopId;

    private List<Long> shopIds;

    private Long userId;

}
