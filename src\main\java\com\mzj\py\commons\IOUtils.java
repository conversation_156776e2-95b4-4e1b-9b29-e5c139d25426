package com.mzj.py.commons;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.CharArrayWriter;
import java.io.Closeable;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.Reader;
import java.io.StringWriter;
import java.io.UnsupportedEncodingException;
import java.io.Writer;

public class IOUtils
{
	private static final int DEFAULT_BUFFER_SIZE = 1024 * 4;

	public static final void closeQuietly(Closeable target)
	{
		if (target == null)
			return;

		try
		{
			target.close();
		}
		catch (Throwable ioe)
		{
		}
	}

	public static InputStream toInputStreamQuietly(String input, String encoding)
	{
		try
		{
			byte[] bytes = encoding != null ? input.getBytes(encoding) : input.getBytes();
			return new ByteArrayInputStream(bytes);
		}
		catch (UnsupportedEncodingException err)
		{
			throw new RuntimeException(err);
		}
	}

	public static String toStringQuietly(InputStream input, String encoding)
	{
		StringWriter sw = new StringWriter();
		try
		{
			copy(input, sw, encoding);
			return sw.toString();
		}
		catch (IOException err)
		{
			throw new RuntimeException(err);
		}
	}

	public static String toString(InputStream input, String encoding) throws IOException
	{
		StringWriter sw = new StringWriter();
		copy(input, sw, encoding);
		return sw.toString();
	}

	public static String toString(InputStream input) throws IOException
	{
		StringWriter sw = new StringWriter();
		copy(input, sw);
		return sw.toString();
	}

	public static int copyQuietly(InputStream input, OutputStream output)
	{
		try
		{
			return copy( input, output ) ;
		}
		catch( IOException err )
		{
			throw new RuntimeException(err) ;
		}
	}

	public static int copy(InputStream input, OutputStream output) throws IOException
	{
		byte[] buffer = new byte[DEFAULT_BUFFER_SIZE];
		int count = 0;
		int n = 0;
		while (-1 != (n = input.read(buffer)))
		{
			output.write(buffer, 0, n);
			count += n;
		}
		return count;
	}

	public static int copy(InputStream input, OutputStream output, int limit) throws IOException
	{
		byte[] buffer = new byte[DEFAULT_BUFFER_SIZE];
		int count = 0;
		int n = 0;
		int size = buffer.length < limit ? buffer.length : limit;

		while (-1 != (n = input.read(buffer, 0, size)))
		{
			output.write(buffer, 0, n);
			count += n;
			limit -= n;
			size = buffer.length < limit ? buffer.length : limit;
			if (size <= 0)
				break;
		}
		return count;
	}

	public static void copy(InputStream input, Writer output) throws IOException
	{
		InputStreamReader in = new InputStreamReader(input);
		copy(in, output);
	}

	public static void copy(InputStream input, Writer output, String encoding) throws IOException
	{
		if (encoding == null)
			copy(input, output);
		else
		{
			InputStreamReader in = new InputStreamReader(input, encoding);
			copy(in, output);
		}
	}

	public static int copy(Reader input, Writer output) throws IOException
	{
		char[] buffer = new char[DEFAULT_BUFFER_SIZE];
		int count = 0;
		int n = 0;
		while (-1 != (n = input.read(buffer)))
		{
			output.write(buffer, 0, n);
			count += n;
		}
		return count;
	}

	public static void copy(Reader input, OutputStream output) throws IOException
	{
		OutputStreamWriter out = new OutputStreamWriter(output);
		copy(input, out);
		out.flush();
	}

	public static void copy(Reader input, OutputStream output, String encoding) throws IOException
	{
		if (encoding == null)
			copy(input, output);
		else
		{
			OutputStreamWriter out = new OutputStreamWriter(output, encoding);
			copy(input, out);
			out.flush();
		}
	}

	public static byte[] toByteArray(InputStream input) throws IOException
	{
		ByteArrayOutputStream output = new ByteArrayOutputStream();
		copy(input, output);
		return output.toByteArray();
	}

	public static byte[] toByteArrayQuietly(InputStream input)
	{
		ByteArrayOutputStream output = new ByteArrayOutputStream();
		try
		{
			copy(input, output);
			return output.toByteArray();
		}
		catch (IOException err)
		{
			throw new RuntimeException(err);
		}
	}

	public static byte[] toByteArray(Reader input) throws IOException
	{
		ByteArrayOutputStream output = new ByteArrayOutputStream();
		copy(input, output);
		return output.toByteArray();
	}

	public static byte[] toByteArray(Reader input, String encoding) throws IOException
	{
		ByteArrayOutputStream output = new ByteArrayOutputStream();
		copy(input, output, encoding);
		return output.toByteArray();
	}

	public static char[] toCharArray(InputStream is) throws IOException
	{
		CharArrayWriter output = new CharArrayWriter();
		copy(is, output);
		return output.toCharArray();
	}

	public static char[] toCharArray(InputStream is, String encoding) throws IOException
	{
		CharArrayWriter output = new CharArrayWriter();
		copy(is, output, encoding);
		return output.toCharArray();
	}

	public static char[] toCharArray(Reader input) throws IOException
	{
		CharArrayWriter sw = new CharArrayWriter();
		copy(input, sw);
		return sw.toCharArray();
	}

	public static String toStringQuietly(InputStream input)
	{
		StringWriter sw = new StringWriter();
		try
		{
			copy(input, sw);
			return sw.toString();
		}
		catch (IOException err)
		{
			throw new RuntimeException(err);
		}
	}

	public static String toString(Reader input) throws IOException
	{
		StringWriter sw = new StringWriter();
		copy(input, sw);
		return sw.toString();
	}

	public static String toString(byte[] input, String encoding) throws IOException
	{
		if (encoding == null)
			return new String(input);
		else
			return new String(input, encoding);
	}

}
