package com.mzj.py.mservice.home.entity;

import com.mzj.py.mservice.wxuser.entity.IdEntity;
import lombok.Data;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "user_record")
@Data
public class UserRecord extends IdEntity {

    @Basic
    @Column(name = "user_id")
    private Long userId;

    @Basic
    @Column(name = "record_time")
    private Integer recordTime;

    @Basic
    @Column(name = "archor_type")
    private String archorType;
}
