package com.mzj.py.mservice.oss.service;

import cn.hutool.core.lang.UUID;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.internal.Mimetypes;
import com.aliyun.oss.model.*;
import com.mzj.py.commons.ResultBean;
import com.mzj.py.config.oss.OssConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * 阿里云OSS对象存储
 *
 * <AUTHOR>
 * @createDate: 2021年3月23日 下午2:49:08
 * @version: 1.0
 */
@Service
public class OSSService {
    private final Logger LOG = LoggerFactory.getLogger(OSSService.class);
    @Autowired
    private OSSClient ossClient;
    @Autowired
    private OssConfig ossConfig;

    /**
     * 上传文件
     *
     * @param bucketName oss桶名称
     * @param mfile      文件
     * @param type       文件业务类型
     * @return 文件key
     * <AUTHOR>
     * @date 2021年3月24日
     */
    public String putFile(String bucketName, MultipartFile mfile, String type) {
        if (StringUtils.isEmpty(bucketName)) {
            bucketName = ossConfig.getOssBucketName();
        }

        File temp = new File("temp_" + getNewName(Objects.requireNonNull(mfile.getOriginalFilename())));
        StringBuilder fileKey = new StringBuilder();
        fileKey.append(ossConfig.getFristFilePath()).append(type);
        fileKey.append("/").append(getNewName(mfile.getOriginalFilename()));
        try (FileOutputStream fos = new FileOutputStream(temp);
             BufferedOutputStream bos = new BufferedOutputStream(fos)) {
            bos.write(mfile.getBytes());
            bos.flush();
            // 上传
            ossClient.putObject(bucketName, fileKey.toString(), temp);
            return fileKey.toString();
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            if (temp.exists()) {
                temp.delete();
            }
        }
        return null;
    }

    /*public String putFile2(String bucketName, MultipartFile mfile, String type) {
        if (StringUtils.isEmpty(bucketName)) {
            bucketName = ossConfig.getOssBucketName();
        }

        File temp = new File("temp_" + getNewName(mfile.getOriginalFilename()));
        StringBuilder fileKey = new StringBuilder();
        fileKey.append(type);
        fileKey.append("/").append(getNewName(mfile.getOriginalFilename()));
        try (FileOutputStream fos = new FileOutputStream(temp);
             BufferedOutputStream bos = new BufferedOutputStream(fos)) {
            bos.write(mfile.getBytes());
            bos.flush();

            InputStream inputStream = new URL("").openStream();
            // 上传
            ossClient.putObject(bucketName, fileKey.toString(), inputStream);
            return fileKey.toString();
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            if (temp.exists()) {
                temp.delete();
            }
        }
        return null;
    }*/

    /**
     * 上传文件
     *
     * @param bucketName oss桶名称
     * @param file       文件
     * @param type       文件业务类型
     * @return 文件key
     * <AUTHOR>
     * @date 2021年3月24日
     */
    public String putFile(String bucketName, File file, String type) {
        try {
            if (StringUtils.isEmpty(bucketName)) {
                bucketName = ossConfig.getOssBucketName();
            }
            StringBuilder fileKey = new StringBuilder();
            fileKey.append(ossConfig.getFristFilePath()).append(type);
            fileKey.append("/").append(getNewName(file.getName()));
            // 上传
            ossClient.putObject(bucketName, fileKey.toString(), file);
            return fileKey.toString();
        } catch (Exception e) {
            LOG.info(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 不改变文件名称上传
     *
     * @param bucketName
     * @param file
     * @param type
     * @return
     */
    public String putFileOld(String bucketName, File file, String type) {
        try {
            if (StringUtils.isEmpty(bucketName)) {
                bucketName = ossConfig.getOssBucketName();
            }
            StringBuilder fileKey = new StringBuilder();
            fileKey.append(ossConfig.getFristFilePath()).append(type);
            fileKey.append("/").append(file.getName());
            // 上传
            ossClient.putObject(bucketName, fileKey.toString(), file);
            return fileKey.toString();
        } catch (Exception e) {
            LOG.info(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 上传文件
     *
     * @param bucketName oss桶名称
     * @param file       文件
     * @param type       文件业务类型
     * @return 文件key
     * <AUTHOR>
     * @date 2021年3月24日
     */
    public String putFileToName(String bucketName, File file, String type, String fileName) {
        try {
            if (StringUtils.isEmpty(bucketName)) {
                bucketName = ossConfig.getOssBucketName();
            }
            StringBuilder fileKey = new StringBuilder();
            fileKey.append(ossConfig.getFristFilePath()).append(type);
            fileKey.append("/").append(getName(file.getName(), fileName));
            // 上传
            ossClient.putObject(bucketName, fileKey.toString(), file);
            return fileKey.toString();
        } catch (Exception e) {
            LOG.info(e.getMessage(), e);
        }
        return null;
    }

    private String getName(String filename, String newFileName) {
        int index = filename.lastIndexOf(".");
        String suffix = filename.substring(index);
        if (newFileName == null) {
            String name = filename.substring(0, index);
            return name + new Date() + suffix;
        }
        return newFileName + suffix;
    }


    private String getNewName(String filename) {
        int index = filename.lastIndexOf(".");
        String suffix = filename.substring(index);
        return UUID.randomUUID() + suffix;
    }


    /**
     * 下载文件
     *
     * @param bucketName 桶名称，可为空
     * @param path       图片路径(不带HTTP与域名)
     * @return
     * <AUTHOR>
     * @date 2021年10月27日
     */
    public File getObjectFile(String bucketName, String path) {
        if (StringUtils.isEmpty(bucketName)) {
            bucketName = ossConfig.getOssBucketName();
        }

        File file = new File(ossConfig.getDownFileTempdir() + "temp_" + getNewName(path));
        try {
            // 下载
            ossClient.getObject(new GetObjectRequest(bucketName, path), file);

            return file;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 下载文件（文件名不变）
     *
     * @param bucketName 桶名称，可为空
     * @param path       图片路径(不带HTTP与域名)
     * @return
     * <AUTHOR>
     * @date 2021年10月27日
     */
    public File getObjectFileOldFileName(String bucketName, String path) {
        if (StringUtils.isEmpty(bucketName)) {
            bucketName = ossConfig.getOssBucketName();
        }

        File file = new File(ossConfig.getDownFileTempdir() + getOldFileName(path));
        try {
            // 下载
            ossClient.getObject(new GetObjectRequest(bucketName, path), file);

            return file;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            return null;
        }
    }

    private String getOldFileName(String filename) {
        int index = filename.lastIndexOf("/");
        return filename.substring(index + 1);
    }

    /**
     * 单文件删除
     *
     * @param bucketName 对象存储桶名称
     *                   文件名称(/prove/202105111620727079538.txt)
     * <AUTHOR>
     * @date 2020年12月30日
     */
    public ResultBean deleteObject(String bucketName, String fileKey) {
        if (StringUtils.isEmpty(bucketName)) {
            bucketName = ossConfig.getOssBucketName();
        }
        try {
            ossClient.deleteObject(bucketName, fileKey);
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            return ResultBean.failedResult(e.getMessage());
        }

        return ResultBean.successfulResult(null);
    }

    public Boolean doesObjectExist(String remoteFileName) {
        String bucketName = ossConfig.getOssBucketName();
        // 关闭OSSClient。
        //ossClient.shutdown();
        return ossClient.doesObjectExist(bucketName, remoteFileName);
    }

    /**
     * 递归获取目录所有文件
     *
     * @param dir
     * @param list
     * @return
     * <AUTHOR>
     * @date 2021年11月19日
     */
    public List<File> getAllFile(File dir, List<File> list) {
        File[] files = dir.listFiles();
        if (files != null) {
            for (File obj : files) {
                if (obj.isDirectory()) {
                    getAllFile(obj, list);
                }
                list.add(obj);
            }
        }

        return list;
    }


    public String dijia(String bucketName, File file, String type, String fileName) {
        if (StringUtils.isEmpty(bucketName)) {
            bucketName = ossConfig.getOssBucketName();
        }
        StringBuilder fileKey = new StringBuilder();
        fileKey.append(ossConfig.getFristFilePath()).append(type);
        fileKey.append("/").append(getName(file.getName(), fileName));
        String dijia = fileKey.toString();
        try {
            InitiateMultipartUploadRequest request = new InitiateMultipartUploadRequest(bucketName, dijia);
            ObjectMetadata metadata = new ObjectMetadata();
            if (metadata.getContentType() == null) {
                metadata.setContentType(Mimetypes.getInstance().getMimetype(file, dijia));
            }
            InitiateMultipartUploadResult upresult = ossClient.initiateMultipartUpload(request);
            String uploadId = upresult.getUploadId();
            List<PartETag> partETags = new ArrayList<PartETag>();
            final long partSize = 1 * 1024 * 1024;   //1 MB。
            long fileLength = file.length();
            int partCount = (int) (fileLength / partSize);
            if (fileLength % partSize != 0) {
                partCount++;
            }
            for (int i = 0; i < partCount; i++) {
                long startPos = i * partSize;
                long curPartSize = (i + 1 == partCount) ? (fileLength - startPos) : partSize;
                UploadPartRequest uploadPartRequest = new UploadPartRequest();
                uploadPartRequest.setBucketName(bucketName);
                uploadPartRequest.setKey(dijia);
                uploadPartRequest.setUploadId(uploadId);
                InputStream instream = null;
                instream = new FileInputStream(file);
                instream.skip(startPos);
                uploadPartRequest.setInputStream(instream);
                uploadPartRequest.setPartSize(curPartSize);
                uploadPartRequest.setPartNumber(i + 1);
                UploadPartResult uploadPartResult = ossClient.uploadPart(uploadPartRequest);
                partETags.add(uploadPartResult.getPartETag());
                LOG.info("dijia上传第{}次，上传大小{}", i + 1, curPartSize);
            }
            CompleteMultipartUploadRequest completeMultipartUploadRequest =
                    new CompleteMultipartUploadRequest(bucketName, dijia, uploadId, partETags);
            CompleteMultipartUploadResult completeMultipartUploadResult = ossClient.completeMultipartUpload(completeMultipartUploadRequest);
            LOG.info("dijia上传完成{}", completeMultipartUploadResult.getETag());
            return dijia;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 新文件名上传
     * @param bucketName
     * @param file
     * @param type
     * @return
     */
    public String putFileNew(String bucketName, File file, String type) {
        try {
            if (StringUtils.isEmpty(bucketName)) {
                bucketName = ossConfig.getOssBucketName();
            }
            StringBuilder fileKey = new StringBuilder();
            fileKey.append(type);
            fileKey.append("/").append(getNewName(file.getName()));
            // 上传
            ossClient.putObject(bucketName, fileKey.toString(), file);
            return fileKey.toString();
        } catch (Exception e) {
            LOG.info(e.getMessage(), e);
        }
        return null;
    }
}
