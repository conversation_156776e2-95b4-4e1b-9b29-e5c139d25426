package com.mzj.py.mservice.wxuser.entity;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-05-30-16:11
 */
@Entity
@Table(name = "follow_anchor")
public class FollowAnchor extends IdEntity{
    @Basic
    @Column(name = "user_id", nullable = true)
    private Long userId; // 小程序openid

    @Basic
    @Column(name = "anchor_id", nullable = true)
    private Long anchorId; // 小程序openid

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getAnchorId() {
        return anchorId;
    }

    public void setAnchorId(Long anchorId) {
        this.anchorId = anchorId;
    }
}
