package com.mzj.py.commons;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.URL;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;

import com.mzj.py.commons.exception.ErrorCode;
import com.mzj.py.commons.exception.ServiceException;
import com.sun.net.ssl.internal.www.protocol.https.Handler;
import com.sun.net.ssl.internal.www.protocol.https.HttpsURLConnectionOldImpl;

/**
 * SSLNet工具类
 */
public class SSLNetProvider {

	private static final Logger logger = LoggerFactory.getLogger(SSLNetProvider.class);

	private static class TrustAnyTrustManager implements X509TrustManager {

		public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
		}

		public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
		}

		public X509Certificate[] getAcceptedIssuers() {
			return new X509Certificate[] {};
		}
	}

	/**
	 * doGet请求
	 * 
	 * @param url
	 * @return
	 * @throws ServiceException
	 */
	@SuppressWarnings("deprecation")
	public static String doGet(String url) throws ServiceException {
		logger.debug("doGet url={}", url);

		InputStream in = null;
		HttpsURLConnectionOldImpl conn = null;
		try {
			SSLContext sc = SSLContext.getInstance("SSL");
			sc.init(null, new TrustManager[] { new TrustAnyTrustManager() }, new SecureRandom());
			URL console = new URL(null, url, new Handler());
			conn = (HttpsURLConnectionOldImpl) console.openConnection();
			conn.setSSLSocketFactory(sc.getSocketFactory());
			conn.setConnectTimeout(10000);
			conn.setReadTimeout(10000);
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setUseCaches(false);
			in = conn.getInputStream();
			BufferedReader br = new BufferedReader(new InputStreamReader(in, "UTF-8"));
			String ret = null;
			String str_return = "";
			while ((ret = br.readLine()) != null) {
				str_return = str_return + ret + "\n";
			}
			logger.info("result=" + str_return);
			return str_return;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			throw new ServiceException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorCode.INTERNAL_SERVER_ERROR.code(), "请求" + url + "出错");
		} finally {
			if (in != null) {
				try {
					in.close();
				} catch (Exception e) {
					logger.error("Exception:", e);
				}
			}
			if (conn != null) {
				try {
					conn.disconnect();
				} catch (Exception e) {
					logger.error("Exception", e);
				}
			}
		}
	}

	/**
	 * doPost请求
	 * 
	 * @param url
	 * @param data
	 * @return
	 * @throws ServiceException
	 */
	@SuppressWarnings("deprecation")
	public static String doPost(String url, String data) throws ServiceException {
		logger.debug("doPost url={}, data={}", url, data);

		InputStream is = null;
		OutputStream out = null;
		HttpsURLConnectionOldImpl conn = null;
		try {
			SSLContext sc = SSLContext.getInstance("SSL");// 使用ssl协议
			sc.init(null, new TrustManager[] { new TrustAnyTrustManager() }, new SecureRandom());
			URL console = new URL(null, url, new Handler());
			conn = (HttpsURLConnectionOldImpl) console.openConnection();
			conn.setSSLSocketFactory(sc.getSocketFactory());
			// conn.setHostnameVerifier(new TrustAnyHostnameVerifier());
			conn.setConnectTimeout(10000);// 链接超时时间
			conn.setReadTimeout(10000);// 阅读请求时间
			conn.setRequestMethod("POST");// 请求方式
			conn.setDoOutput(true);// 准许输出
			conn.setDoInput(true);// 准许输入
			conn.setUseCaches(false);// 不缓存?
			out = conn.getOutputStream();
			DataOutputStream httpOut = new DataOutputStream(out);
			httpOut.write(data.getBytes("UTF-8"));// 格式
			httpOut.flush();
			is = conn.getInputStream();
			BufferedReader br = new BufferedReader(new InputStreamReader(is, "UTF-8"));
			String ret = null;
			String str_return = "";
			while ((ret = br.readLine()) != null) {
				str_return = str_return + ret;
			}

			logger.debug("result=" + str_return);
			return str_return;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			throw new ServiceException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorCode.INTERNAL_SERVER_ERROR.code(), "请求" + url + "出错");
		} finally {
			if (out != null) {
				try {
					out.close();
				} catch (Exception e) {
					logger.error("Exception:", e);
				}
			}
			if (is != null) {
				try {
					is.close();
				} catch (Exception e) {
					logger.error("Exception:", e);
				}
			}
			if (conn != null) {
				try {
					conn.disconnect();
				} catch (Exception e) {
					logger.error("Exception", e);
				}
			}
		}
	}

	/**
	 * doGet请求获取字节数组
	 * 
	 * @param url
	 * @return
	 * @throws ServiceException
	 */
	@SuppressWarnings("deprecation")
	public static byte[] doGetBytes(String url) throws ServiceException {
		InputStream in = null;
		ByteArrayOutputStream outStream = null;
		HttpsURLConnectionOldImpl conn = null;
		try {
			SSLContext sc = SSLContext.getInstance("SSL");
			sc.init(null, new TrustManager[] { new TrustAnyTrustManager() }, new SecureRandom());
			URL console = new URL(null, url, new Handler());
			conn = (HttpsURLConnectionOldImpl) console.openConnection();
			conn.setSSLSocketFactory(sc.getSocketFactory());
			conn.setConnectTimeout(10000);
			conn.setReadTimeout(10000);
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setUseCaches(false);
			in = conn.getInputStream();
			outStream = new ByteArrayOutputStream();
			byte[] b = new byte[1024];
			int length = -1;
			while ((length = in.read(b, 0, 1024)) != -1) {
				outStream.write(b, 0, length);
			}
			return outStream.toByteArray();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			throw new ServiceException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorCode.INTERNAL_SERVER_ERROR.code(), "请求" + url + "出错");
		} finally {
			if (outStream != null) {
				try {
					outStream.close();
				} catch (Exception e) {
					logger.error("Exception", e);
				}
			}
			if (in != null) {
				try {
					in.close();
				} catch (Exception e) {
					logger.error("Exception", e);
				}
			}
			if (conn != null) {
				conn.disconnect();
			}
		}
	}

}
