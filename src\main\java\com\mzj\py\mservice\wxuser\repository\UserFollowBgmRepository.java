package com.mzj.py.mservice.wxuser.repository;


import com.mzj.py.mservice.wxuser.entity.UserFollowBgm;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface UserFollowBgmRepository extends PagingAndSortingRepository<UserFollowBgm,Long>, JpaSpecificationExecutor<UserFollowBgm> {


	List<UserFollowBgm> findByBgmIdAndUserId(Long bgmId, Long id);
}
