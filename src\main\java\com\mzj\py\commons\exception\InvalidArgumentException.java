package com.mzj.py.commons.exception;

import org.springframework.http.HttpStatus;

/**
 * 无效参数异常， 参数校验异常，无效请求
 * <AUTHOR>
 *
 */
public class InvalidArgumentException extends ServiceException{

	private static final long serialVersionUID = 1L;

	public InvalidArgumentException(String message) {
		super(HttpStatus.BAD_REQUEST,ErrorCode.BAD_REQUEST.code(), message);
	}
	
	public InvalidArgumentException(int code,String message) {
		super(HttpStatus.BAD_REQUEST,code, message);
	}
}
