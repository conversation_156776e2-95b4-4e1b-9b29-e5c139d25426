#kwolong database
spring.jpa.database.show-sql=true

spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=***********************************************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=!QAZ@WSX
spring.datasource.primary.initialSize=5
spring.datasource.primary.maxActive=10
spring.datasource.primary.minIdle=1
spring.datasource.primary.maxWait=30000
spring.datasource.primary.validationQuery=SELECT '1'
spring.datasource.primary.testOnBorrow=false
spring.datasource.primary.testOnReturn=false
spring.datasource.primary.testWhileIdle=true
spring.datasource.primary.poolPreparedStatements=false
spring.datasource.primary.maxOpenPreparedStatements=-1
spring.datasource.primary.org.hibernate.dialect=org.hibernate.dialect.MySQL5Dialect
spring.datasource.primary.hibernate.show_sql=true

# logback
logging.level.root=INFO
logging.level.org.springframework.web=INFO
logging.level.com.mzj.cm=DEBUG

#redis
redis.hostName=************
redis.port=6379
redis.password=A&kQE3Zvb
redis.timeout=60000
redis.connectionTimeout=100000
redis.database=11
redis.maxActive=300
redis.maxIdle=100
redis.minIdle=0
redis.maxWait=-1
redis.testOnBorrow=true
redis.testOnReturn=false
redis.testWhileIdle=true

#is token auth(true or false)
is.token.auth=true

#manager auth header token
manager.auth.header.token=7F34F184750E90CEF8CD3D1BF0B08AB3

#token auth secretKey
token.auth.secretKey=BF253E8F89EA21CF
#redis expire time(minutes)
redis.token.expire.time=5256000

ali.oss.downFile.tempdir=/home/<USER>/py-service/file/MP3/
ali.oss.bucket.url=https://peiyin-prod.oss-cn-hangzhou.aliyuncs.com/
ali.cdn.bucket.url=https://c.darhoo.com/
wx.pay.appid=wxfdb8bf236b7e23bc
wx.pay.mchid=1675024609
wx.pay.mchSer=7FFC26A64E2F65E48FCE52E381B9F79330B74BA0
wx.pay.apiV3=13858985525lanxishihongjingdianz
wx.pay.notifyUrl=https://d.darhoo.com/mini-api/mini/order/callback
wx.pay.keyPath=/home/<USER>/apiclient_key.pem
# app
wx.app.secret=5b2ea3899b7269301bc05710872899c4


pay.wechat.merchant-id=1675024609
pay.wechat.merchant-serial-number=7FFC26A64E2F65E48FCE52E381B9F79330B74BA0
pay.wechat.api-v3-key=13858985525lanxishihongjingdianz
pay.wechat.notify-url=https://d.darhoo.com/mini-api/mini/order/callback
pay.wechat.private-key-path=classpath:/cert/apiclient_key.pem
pay.wechat.app-id=wxfdb8bf236b7e23bc


spring.rabbitmq.host=127.0.0.1
spring.rabbitmq.port=5672
spring.rabbitmq.virtual-host=
spring.rabbitmq.username=admin
spring.rabbitmq.password=peiyin2022
spring.rabbitmq.listener.simple.acknowledge-mode=auto
spring.rabbitmq.listener.simple.retry.enabled=true
spring.rabbitmq.listener.simple.retry.max-attempts=4
spring.rabbitmq.listener.simple.retry.initial-interval=5000


netty.port=8050
netty.path=/ai



ty.accessKeySecret=******************************
ty.accessKeyId=LTAI5tGLtZoTrimJhNrsYRqH