package com.mzj.py.mservice.compound.utils;

import cn.hutool.core.lang.UUID;
import it.sauronsoftware.jave.AudioAttributes;
import it.sauronsoftware.jave.Encoder;
import it.sauronsoftware.jave.EncodingAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.sound.sampled.AudioFileFormat;
import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class FileUtil {
    private static final int MAX_SIZE_IN_BYTES = 4 * 1024 * 1024; // 2MB
    public static String loadMediaAsBase64(String path) throws IOException {
        FileInputStream fileInputStream = new FileInputStream(path);
        byte[] temp = new byte[1024 * 1024];
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        int l = 0;
        while ((l = fileInputStream.read(temp)) != -1) {
            bos.write(temp, 0, l);
        }
        fileInputStream.close();
        bos.close();
        return Base64.getEncoder().encodeToString(bos.toByteArray());
    }

    public static File saveFile(String path, byte[] data, boolean needDecode) throws IOException {
        File file = new File(path);
        byte[] bytes = data;
        if (needDecode) {
            String base64 = new String(data, StandardCharsets.UTF_8);
            bytes = Base64.getDecoder().decode(base64);
        }
        FileOutputStream fileOutputStream = new FileOutputStream(file);
        fileOutputStream.write(bytes);
        fileOutputStream.close();
        return file;
    }

    public static File downloadFile(String fileURL, String voiceName) throws Exception {
        URL url = new URL(fileURL);
        HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
        int responseCode = httpConn.getResponseCode();

        // 检查HTTP响应代码
        if (responseCode == HttpURLConnection.HTTP_OK) {
            Path tempFilePath = Paths.get(voiceName);
            try (InputStream inputStream = new BufferedInputStream(httpConn.getInputStream());
                 FileOutputStream outputStream = new FileOutputStream(tempFilePath.toFile())) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            } finally {
                httpConn.disconnect();
            }
            return tempFilePath.toFile();
        } else {
            throw new Exception("GET request not worked: " + responseCode);
        }
    }
    public static File taiseng(String fileURL, String voiceName, Integer tailu) throws Exception {
        URL url = new URL(fileURL);
        HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
        File outputFile=new File(voiceName);
        try (AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(new BufferedInputStream(httpConn.getInputStream()))){
            AudioFormat format = audioInputStream.getFormat();
            byte[] audioBytes = tailuo(audioInputStream,tailu);
            saiwen(outputFile, format, audioBytes);
        }
        return outputFile;
    }
    private static byte[] tailuo(AudioInputStream audioInputStream, float volumeReductionFactor) throws IOException {
        byte[] buffer = new byte[1024 * 1024]; // 假设缓冲区大小为4KB
        ByteArrayOutputStream byteOutputStream = new ByteArrayOutputStream();
        int bytesRead;
        while ((bytesRead = audioInputStream.read(buffer)) != -1) {
            if (bytesRead % 2 != 0) {
                // 如果读取的字节数不是偶数，则跳过最后一个字节（可能是不完整的样本）
                bytesRead--;
            }

            short[] shorts = new short[bytesRead / 2];
            ByteBuffer.wrap(buffer, 0, bytesRead).order(ByteOrder.LITTLE_ENDIAN).asShortBuffer().get(shorts);

            // 应用音量缩放，同时防止溢出
            for (int i = 0; i < shorts.length; i++) {
                int scaledValue = (int) (shorts[i] * volumeReductionFactor/100);
                if (scaledValue < Short.MIN_VALUE) {
                    scaledValue = Short.MIN_VALUE;
                } else if (scaledValue > Short.MAX_VALUE) {
                    scaledValue = Short.MAX_VALUE;
                }
                shorts[i] = (short) scaledValue;
            }

            // 将缩放后的short数组转换回byte数组
            ByteBuffer byteBuffer = ByteBuffer.allocate(shorts.length * 2);
            byteBuffer.order(ByteOrder.LITTLE_ENDIAN);
            byteBuffer.asShortBuffer().put(shorts);

            // 写入缩放后的字节到输出流
            byteOutputStream.write(byteBuffer.array(), 0, shorts.length * 2);
        }
        return byteOutputStream.toByteArray();
    }

    private static void saiwen(File outputFile, AudioFormat format, byte[] audioBytes) throws IOException {
        AudioSystem.write(new AudioInputStream(new ByteArrayInputStream(audioBytes), format, AudioSystem.NOT_SPECIFIED),
                AudioFileFormat.Type.WAVE, outputFile);
    }

    public static void combine(File outFile,File tempFile) throws Exception {
        FileOutputStream fos =new FileOutputStream(outFile, true);
        FileInputStream fis = new FileInputStream(tempFile);
        int len = 0;
        for (byte[] buf = new byte[1024 * 1024]; (len = fis.read(buf)) != -1; ) {
            fos.write(buf, 0, len);
        }
        fos.flush();
        fis.close();
        fos.close();
        tempFile.delete();
    }

    public static void combineDes(File outFile,File tempFile) throws Exception {
        File file = new File(outFile.getParent()+File.separator+ UUID.randomUUID()+".mp3");
        editMp3Same(tempFile,file);
        FileOutputStream fos =new FileOutputStream(file, true);
        FileInputStream fis = new FileInputStream(outFile);
        int len = 0;
        for (byte[] buf = new byte[1024 * 1024]; (len = fis.read(buf)) != -1; ) {
            fos.write(buf, 0, len);
        }
        fis.close();
        fos.close();
        cn.hutool.core.io.FileUtil.copy(file,outFile,true);
        file.delete();
    }
    public static void coverToMp3(File source,File target) throws Exception {
        AudioAttributes audio = new AudioAttributes();
        audio.setCodec("libmp3lame");
        audio.setBitRate(32000);
        audio.setSamplingRate(16000);
        audio.setChannels(1);
        EncodingAttributes attrs = new EncodingAttributes();
        attrs.setFormat("mp3");
        attrs.setAudioAttributes(audio);
        Encoder encoder = new Encoder();
        // 执行编码
        encoder.encode(source, target, attrs);
        source.delete();
    }

    public static void coverToMp3Heigh(File source,File target) throws Exception {
        AudioAttributes audio = new AudioAttributes();
        audio.setCodec("libmp3lame");
        audio.setSamplingRate(24000);
        audio.setChannels(1);
        EncodingAttributes attrs = new EncodingAttributes();
        attrs.setFormat("mp3");
        attrs.setAudioAttributes(audio);
        Encoder encoder = new Encoder();
        // 执行编码
        encoder.encode(source, target, attrs);
        source.delete();
    }
    public static File convertMultipartFileToFile(MultipartFile multipartFile,String dir) throws IOException {
        // 获取文件扩展名
        String fileName = multipartFile.getOriginalFilename();
        String extension = "";
        if (fileName != null && !fileName.isEmpty()) {
            int dotIndex = fileName.lastIndexOf('.');
            if (dotIndex > 0) {
                extension = fileName.substring(dotIndex);
            }
        }

        // 创建一个临时文件
        File tempFile = new File(dir+ fileName+"&"+extension);
        multipartFile.transferTo(tempFile);
        // 返回临时文件
        return tempFile;
    }
    public static void coverToWav(File source,File target) throws Exception {
        AudioAttributes audio = new AudioAttributes();
        audio.setCodec("pcm_s16le");
        audio.setSamplingRate(16000);
        audio.setChannels(1); //
        EncodingAttributes attrs = new EncodingAttributes();
        attrs.setFormat("wav");
        attrs.setAudioAttributes(audio);
        Encoder encoder = new Encoder();
        encoder.encode(source, target, attrs);
        source.delete();
    }


    public static void tempCoverToWav(MultipartFile source,File target,File tempFile) throws Exception {
        source.transferTo(tempFile);
        AudioAttributes audio = new AudioAttributes();
        audio.setCodec("pcm_s16le");
        audio.setSamplingRate(16000);
        audio.setChannels(1); //
        EncodingAttributes attrs = new EncodingAttributes();
        attrs.setFormat("wav");
        attrs.setAudioAttributes(audio);
        Encoder encoder = new Encoder();
        encoder.encode(tempFile, target, attrs);
        tempFile.delete();
    }
    public static void tempCoverToMp3(MultipartFile source,File target,File tempFile) throws Exception {
        source.transferTo(tempFile);
        AudioAttributes audio = new AudioAttributes();
        audio.setCodec("libmp3lame");
        audio.setSamplingRate(16000);
        audio.setChannels(1); //
        EncodingAttributes attrs = new EncodingAttributes();
        attrs.setFormat("wav");
        attrs.setAudioAttributes(audio);
        Encoder encoder = new Encoder();
        encoder.encode(tempFile, target, attrs);
        tempFile.delete();
    }
    public static void editMp3Same(File source,File target) throws Exception {
        AudioAttributes audio = new AudioAttributes();
        audio.setCodec("libmp3lame");
        audio.setSamplingRate(16000);
        audio.setBitRate(32000);
        audio.setChannels(1); //
        EncodingAttributes attrs = new EncodingAttributes();
        attrs.setFormat("mp3");
        attrs.setAudioAttributes(audio);
        Encoder encoder = new Encoder();
        encoder.encode(source, target, attrs);
        source.delete();
    }




    public static void truncateFile(MultipartFile sourceFile, File targetFile)  {
            try (InputStream inputStream = sourceFile.getInputStream();
                 OutputStream outputStream = new FileOutputStream(targetFile)) {
                byte[] buffer = new byte[1024]; // 使用1KB的缓冲区
                int bytesRead;
                long totalBytesRead = 0;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    if (totalBytesRead + bytesRead > MAX_SIZE_IN_BYTES) {
                        // 如果当前读取的字节会导致总大小超过2MB，则只写入剩余的部分
                        int bytesToWrite = (int) (MAX_SIZE_IN_BYTES - totalBytesRead);
                        outputStream.write(buffer, 0, bytesToWrite);
                        break;
                    }
                    outputStream.write(buffer, 0, bytesRead);
                    totalBytesRead += bytesRead;
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
    }

    public static void trunFile(File sourceFile, File targetFile)  {
        try (InputStream inputStream = new FileInputStream(sourceFile);
             OutputStream outputStream = new FileOutputStream(targetFile)) {
            byte[] buffer = new byte[1024]; // 使用1KB的缓冲区
            int bytesRead;
            long totalBytesRead = 0;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                if (totalBytesRead + bytesRead > MAX_SIZE_IN_BYTES) {
                    // 如果当前读取的字节会导致总大小超过2MB，则只写入剩余的部分
                    int bytesToWrite = (int) (MAX_SIZE_IN_BYTES - totalBytesRead);
                    outputStream.write(buffer, 0, bytesToWrite);
                    break;
                }
                outputStream.write(buffer, 0, bytesRead);
                totalBytesRead += bytesRead;
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    public static String replacePauseTags(String input) {
        Pattern pattern = Pattern.compile("\\[停(\\d{3,5})ms\\]");
        Matcher matcher = pattern.matcher(input);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            int timeValue = Integer.parseInt(matcher.group(1));
            if (timeValue >= 100 && timeValue <= 10000) {
                matcher.appendReplacement(sb, "<break time='" + matcher.group(1) + "ms'/>");
            }
        }
        matcher.appendTail(sb); // 添加剩余的字符串
        return sb.toString();
    }

    public static File createFileIfNotExists(String filePathAndName) {
        // 创建File对象
        File file = new File(filePathAndName);

        // 获取文件的父目录
        File parentDir = file.getParentFile();

        // 如果父目录不为null且不存在，则尝试创建它
        if (parentDir != null && !parentDir.exists()) {
            // 尝试创建所有必要的父目录
            if (!parentDir.mkdirs()) {
                // 如果创建失败，返回null或抛出异常（根据你的需求）
                return file; // 或者你可以抛出一个异常
            }
        }

        // 父目录已经存在或已创建，返回File对象
        return file;
    }

    public static String getFileNewName(String filename) {
        int indx = filename.lastIndexOf(".");
        String suffix = filename.substring(indx, filename.length());
        return UUID.fastUUID().toString() + suffix;
    }
}
