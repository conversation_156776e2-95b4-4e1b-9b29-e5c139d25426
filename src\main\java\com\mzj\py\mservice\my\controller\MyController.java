package com.mzj.py.mservice.my.controller;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.mservice.my.entity.BaseAbout;
import com.mzj.py.mservice.my.service.MyService;
import com.mzj.py.mservice.my.vo.BaseServiceMessageVo;
import com.mzj.py.mservice.wxuser.entity.WxUser;
import com.mzj.py.mservice.wxuser.vo.UserVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * 我的
 * @author: duanjinze
 * @date: 2022/11/15 15:47
 * @version: 1.0
 */
@Controller
@RequestMapping("/my")
public class MyController {
    @Autowired
    private MyService myService;

    /**
     * 我的基本信息
     *
     * @param accessToken
     * @return
     */
    @GetMapping("/info")
    @ResponseBody
    public ResultBean<WxUser> myInfo(@RequestHeader String accessToken) {
        return myService.myInfo(accessToken);
    }

    /**
     * 关于我们
     *
     * @return
     */
    @GetMapping("/about")
    @ResponseBody
    public ResultBean<BaseAbout> aboutUs() {
        return myService.aboutUs();
    }

    /**
     * 在线客服、联系方式
     *
     * @return
     */
    @GetMapping("/customerService")
    @ResponseBody
    public ResultBean customerService() {
        return myService.customerService();
    }

    /**
     * 客服留言
     *
     * @param accessToken
     * @param vo
     * @return
     */
    @PostMapping("/customerMessage")
    @ResponseBody
    public ResultBean customerMessage(@RequestHeader String accessToken, @RequestBody BaseServiceMessageVo vo) {
        return myService.customerMessage(accessToken, vo);
    }

    /**
     * 修改头像，昵称
     *
     * @param accessToken
     * @param vo
     * @return
     */
    @PutMapping("/avatarOrNickname")
    @ResponseBody
    public ResultBean updateAvatarOrNickName(@RequestHeader String accessToken, @RequestBody UserVo vo) {
        return myService.updateAvatarOrNickName(accessToken, vo);
    }

    /**
     * 修改手机号
     *
     * @param accessToken
     * @param vo
     * @return
     */
    @PutMapping("/phone")
    @ResponseBody
    public ResultBean updatePhone(@RequestHeader String accessToken, @RequestBody UserVo vo) {
        return myService.updatePhone(accessToken, vo);
    }
}