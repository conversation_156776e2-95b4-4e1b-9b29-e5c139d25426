package com.mzj.py.mservice.order.vo.dto;

import lombok.Data;

import java.util.List;

@Data
public class OrderPay {
    private Long productId;//商品id
    private List<Long> deviceIdList;
    private int totalAmount;//总金额
    private int payAmount;//实际支付金额
    private Long shopId;
    private Integer payType;
    private String billType;
    private String billHeader;
    private String billContent;
    private String billReceiverPhone;
    private String billReceiverEmail;
    private Integer clientType;
}
