package com.mzj.py.config.exception;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.exception.UnauthorizedException;
import com.mzj.py.commons.exception.CustomException;
import com.mzj.py.commons.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.HttpRequestMethodNotSupportedException;

/**
 * 统一异常处理
 * <p>
 * 将服务层或控制层抛出的未捕获异常转换为统一的 JSON 响应，
 * 避免 Spring 默认将异常继续向外抛出而导致 NestedServletException 日志。
 * </p>
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 捕获所有未处理的异常，返回 500 状态码及统一错误结构。
     *
     * @param ex 抛出的异常
     * @return 带有错误信息的 ResultBean
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ResultBean<?>> handleException(Exception ex) {
        // 记录异常日志，便于排查
        log.error("Unhandled exception caught: ", ex);
        if (ex instanceof UnauthorizedException) {
            return ResponseEntity.status(HttpStatus.OK).body(ResultBean.failedResultOfToken());
        }
        // 构造统一错误返回体
        ResultBean<?> result = ResultBean.failedResultOfException();
        // 将异常信息写入返回体，保持与原始异常信息一致
        result.setMsg("服务器开小差");

        // 返回 500 Internal Server Error
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
    }

    /**
     * 处理请求参数、头信息缺失或解析错误等导致的 400 错误。
     */
    @ExceptionHandler({
            org.springframework.http.converter.HttpMessageNotReadableException.class,
            org.springframework.web.bind.MissingServletRequestParameterException.class,
            org.springframework.web.bind.MissingRequestHeaderException.class,
            org.springframework.web.method.annotation.MethodArgumentTypeMismatchException.class
    })
    public ResponseEntity<ResultBean<?>> handleBadRequest(Exception ex) {
        log.warn("Bad request: {}", ex.getMessage());
        ResultBean<?> result = ResultBean.failedResultOfParamWithMsg(ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
    }

    /**
     * 处理 HTTP 方法不支持 (405) 异常。
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<ResultBean<?>> handleMethodNotSupported(HttpRequestMethodNotSupportedException ex) {
        log.warn("Method not allowed: {}", ex.getMessage());
        // 使用通用失败结构，但返回 405 状态码
        ResultBean<?> result = ResultBean.failedResultWithMsg(ex.getMessage());
        return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED).body(result);
    }

    /**
     * 处理业务系统自定义的 CustomException，统一返回 200 状态码，便于前端根据 code 解析。
     */
    @ExceptionHandler(CustomException.class)
    public ResponseEntity<ResultBean<?>> handleCustomException(CustomException ex) {
        log.warn("Business exception: {}", ex.getMessage());
        ResultBean<?> result = ResultBean.failedResultWithMsg(ex.getMessage());
        return ResponseEntity.status(HttpStatus.OK).body(result);
    }

    /**
     * 处理业务系统自定义的 ServiceException，统一返回 200 状态码，便于前端根据 code 解析。
     */
    @ExceptionHandler(ServiceException.class)
    public ResponseEntity<ResultBean<?>> handleServiceException(ServiceException ex) {
        log.warn("Service exception: {}", ex.getMessage());
        ResultBean<?> result = ResultBean.failedResultWithMsg(ex.getMessage());
        return ResponseEntity.status(HttpStatus.OK).body(result);
    }
}