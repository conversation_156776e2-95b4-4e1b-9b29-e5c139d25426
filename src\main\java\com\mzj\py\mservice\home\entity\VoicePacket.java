package com.mzj.py.mservice.home.entity;


import lombok.Data;

import javax.persistence.*;

/**
 * 作品语音包
 * @author: duanjinze
 * @date: 2022/11/10 11:58
 * @version: 1.0
 */
@Data
@Entity
@Table(name = "dub_voice_packet")
public class VoicePacket {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;


    /**
     * 作品id
     */
    @Basic
    @Column(name = "voice_work_id")
    private Long voiceWorkId;

    /**
     * 名称
     */
    @Basic
    @Column(name = "name")
    private String name;

    /**
     * 时长
     */
    @Basic
    @Column(name = "voice_time")
    private Integer voiceTime;

    /**
     * 文件url
     */
    @Basic
    @Column(name = "file_url")
    private String fileUrl;


    @Basic
    @Column(name = "shop_id")
    private Long shopId;


}
