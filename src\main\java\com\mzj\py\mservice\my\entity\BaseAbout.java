package com.mzj.py.mservice.my.entity;

import javax.persistence.*;

/**
 * 关于我们
 * @author: duanjinze
 * @date: 2022/11/11 15:47
 * @version: 1.0
 */
@Entity
@Table(name = "dub_base_about")
public class BaseAbout {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 内容
     */
    @Basic
    @Column(name = "content",columnDefinition = "text")
    private String content;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
