package com.mzj.py.mservice.websocket.config;

import com.wechat.pay.java.service.payments.jsapi.JsapiServiceExtension;
import io.netty.channel.ChannelPromise;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Configuration
@ConfigurationProperties(prefix = "netty")
@Data
public class NettyConfig {
    private int port;//netty监听的端口

    private String path;//websocket访问路径
}
