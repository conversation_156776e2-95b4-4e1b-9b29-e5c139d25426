package com.mzj.py.filter;

import java.io.IOException;
import java.io.OutputStreamWriter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.mzj.py.commons.exception.ServiceException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.filter.OncePerRequestFilter;

/**
 * token鉴权
 *
 * <AUTHOR>
 * @Date 2019-10-30
 */
public class AuthFilter extends OncePerRequestFilter {
    private static Logger LOG = LoggerFactory.getLogger(TokenUtil.class);

    @Value("${is.token.auth}")
    private boolean isTokenAuth;
    @Value("${no.authority.urls}")
    private String noAuthorityUrls;
    @Autowired
    private TokenUtil tokenUtil;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        LOG.info("AuthFilter doFilterInternal isTokenAuth={}", isTokenAuth);
        if (isTokenAuth) {
            try {
                if (isAuth(request, response)) {
                    filterChain.doFilter(request, response);
                } else {
                    response.setStatus(401);
                    ServletOutputStream out = response.getOutputStream();
                    OutputStreamWriter ow = new OutputStreamWriter(out, "UTF-8");
                    ow.write("{\"statusCode\":\"401\",\"msg\":\"error\"}");
                    ow.flush();
                    ow.close();
                }
            } catch (ServiceException e) {
                response.setStatus(e.getHttpStatus().value());
                ServletOutputStream out = response.getOutputStream();
                OutputStreamWriter ow = new OutputStreamWriter(out, "UTF-8");
                ow.write("{\"code\":" + e.getCode() + ",\"msg\":\"error\"}");
                ow.flush();
                ow.close();
            }
        } else {
            filterChain.doFilter(request, response);
        }
    }

    public boolean isAuth(HttpServletRequest request, HttpServletResponse response) throws IOException {
        boolean flag = false;
        //String accessToken = request.getParameter("accessToken");
        String accessToken = request.getHeader("accessToken");// 对外服务token
        String requestManagerAuthToken = request.getHeader("manager-auth-header-token"); // 后管的header token
        String URLPath = request.getRequestURI();// 请求路径
        LOG.info("AuthFilter isAuth accessToken = {}, requestManagerAuthToken = {}, URLPath = {}", accessToken, requestManagerAuthToken, URLPath);

        boolean managerFlag = false; // 后管系统token验证开关
        boolean externalFlag = false; // 对外接口token验证开关

        if (StringUtils.isNotBlank(URLPath) && !(noAuthorityUrls.trim().contains(URLPath) || this.isStartsWith(URLPath))) {
            if (StringUtils.isNotBlank(requestManagerAuthToken)) {
                managerFlag = tokenUtil.managerCheck(requestManagerAuthToken);
            }

            if (!managerFlag && StringUtils.isNotBlank(accessToken)) {
                externalFlag = tokenUtil.externalTokenCheck(accessToken);
            }

            if (managerFlag || externalFlag) {
                flag = true;
            } else {
                flag = false;
            }
        } else {
            flag = true;
        }

        LOG.info("uthFilter isAuth flag = {}", flag);
        return flag;
    }

    private boolean isStartsWith(String url) {
        if (StringUtils.isBlank(url)) {
            return false;
        }

        String[] whiteUrls = noAuthorityUrls.split(",");
        for (String whiteUrl : whiteUrls) {
            if (whiteUrl.contains("*") && url.startsWith(whiteUrl.replace("*", ""))) {
                return true;
            }
        }

        return false;
    }

}
