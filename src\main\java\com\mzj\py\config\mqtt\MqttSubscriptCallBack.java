package com.mzj.py.config.mqtt;

import com.alibaba.fastjson.JSONObject;
import com.mzj.py.mservice.deviceOperationLog.service.DeviceOperationLogService;
import com.mzj.py.mservice.pay.util.StringUtils;
import com.mzj.py.commons.RedisKeyConstant;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class MqttSubscriptCallBack implements MqttCallback {

    @Value("${mqtt.clientId}")
    private String clientId;
    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate redisTemplate;
    @Autowired
    private DeviceOperationLogService deviceOperationLogService;
    @Autowired
    private MqttSubscriptConfig mqttSubscriptConfig;

    /**
     * 智能解码MQTT消息载荷，尝试多种编码方式
     * 
     * @param payload 消息字节数组
     * @return 解码后的字符串
     */
    private String decodePayload(byte[] payload) {
        // 首先尝试UTF-8解码
        try {
            String utf8Result = new String(payload, StandardCharsets.UTF_8);
            // 检查是否包含UTF-8的替换字符，如果包含说明解码失败
            if (!utf8Result.contains("\uFFFD")) {
                return utf8Result;
            }
        } catch (Exception e) {
            log.debug("UTF-8解码失败", e);
        }

        // 尝试GBK解码
        try {
            String gbkResult = new String(payload, "GBK");
            return gbkResult;
        } catch (Exception e) {
            log.debug("GBK解码失败", e);
        }

        // 尝试GB2312解码
        try {
            String gb2312Result = new String(payload, "GB2312");
            return gb2312Result;
        } catch (Exception e) {
            log.debug("GB2312解码失败", e);
        }

        // 尝试ISO-8859-1解码（通常不会失败）
        try {
            String isoResult = new String(payload, "ISO-8859-1");
            return isoResult;
        } catch (Exception e) {
            log.debug("ISO-8859-1解码失败", e);
        }

        // 最后使用系统默认编码
        String defaultResult = new String(payload);
        log.warn("所有编码尝试都失败，使用系统默认编码，结果可能包含乱码: {}", defaultResult);
        return defaultResult;
    }

    /**
     * 与服务器断开的回调
     */
    @Override
    public void connectionLost(Throwable cause) {
        log.error(clientId + "client 与服务器断开连接！！异常类型: {}, 异常消息: {}",
                cause.getClass().getSimpleName(), cause.getMessage());
        log.error("连接断开堆栈信息: ", cause);
        log.info("连接断开，等待 SDK 自动重连，无需手动创建新连接");
    }

    @Override
    public void messageArrived(String topic, MqttMessage message) throws Exception {

        String payload = decodePayload(message.getPayload());
        JSONObject jsonObject = JSONObject.parseObject(payload);
        String timeStamp = jsonObject.getObject("timeStamp", String.class);

        if (StringUtils.isEmpty(timeStamp)) {
            log.error("MQTT返回的消息中没有 timeStamp");
            return;
        }

        log.info(String.format("response 接收消息主题 : %s", topic));
        log.info(String.format("response 接收消息Qos : %d", message.getQos()));
        log.info(String.format("response 接收消息内容 : %s", payload));
        log.info(String.format("response 接收消息retained : %b", message.isRetained()));
        int lastIndex = topic.lastIndexOf("/");
        String sn = topic.substring(lastIndex + 1);

        String lockKey = String.format(RedisKeyConstant.DEVICE_LOCK, sn);
        String snVal = redisTemplate.opsForValue().get(lockKey);

        if (StringUtils.isNotEmpty(snVal) && snVal.equals(timeStamp)) {
            // 将真正的响应写入单独的 key, 便于多并发场景下的区分
            String respKey = String.format(RedisKeyConstant.DEVICE_RESP, sn, timeStamp);
            redisTemplate.delete(lockKey);
            redisTemplate.opsForValue().set(respKey, payload, 300, TimeUnit.SECONDS);
            log.info(String.format("response 在redis中找到sn = %s, timeStamp= %s 的发送数据， value = %s", sn, snVal, payload));
            // 新增: 根据 sn 和 timeStamp 获取操作日志ID并更新日志
            String opLogKey = String.format(RedisKeyConstant.DEVICE_OP_LOG, sn, timeStamp);
            String opLogIdStr = redisTemplate.opsForValue().get(opLogKey);
            if (StringUtils.isNotEmpty(opLogIdStr)) {
                try {
                    deviceOperationLogService.update(Long.valueOf(opLogIdStr), payload);
                } catch (Exception e) {
                    log.error("更新设备操作日志失败 id={}, payload={}", opLogIdStr, payload, e);
                }
                redisTemplate.delete(opLogKey);
            }
        }
    }

    /**
     * 消息发布成功的回调
     */
    @Override
    public void deliveryComplete(IMqttDeliveryToken token) {
        IMqttAsyncClient client = token.getClient();
        log.info(client.getClientId() + "发布消息成功！");

    }
}
