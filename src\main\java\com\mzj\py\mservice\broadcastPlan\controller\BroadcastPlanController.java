package com.mzj.py.mservice.broadcastPlan.controller;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.mservice.broadcastPlan.service.BroadcastPlanService;
import com.mzj.py.mservice.broadcastPlan.vo.dto.BroadcastPlanAddDto;
import com.mzj.py.mservice.common.ApiBaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 播报计划
 */
@Controller
@RequestMapping("/mini/broadcastPlan")
public class BroadcastPlanController extends ApiBaseController {

    @Autowired
    private BroadcastPlanService broadcastPlanService;

    /**
     * 播报计划列表
     *
     * @param accessToken
     * @param pageSize
     * @param pageNumber
     * @return
     */
    @GetMapping
    @ResponseBody
    public ResultBean<Map<String, Object>> list(
            @RequestHeader String accessToken,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(name = "pageNumber", defaultValue = "0") Integer pageNumber) {
        List<Long> shopIds = getShopIds(accessToken);
        if (shopIds.isEmpty()) {
            return ResultBean.getResultMap();
        }
        return broadcastPlanService.list(shopIds, pageSize, pageNumber);
    }


    /**
     * 添加或修改
     *
     * @param accessToken
     * @param vo
     * @return
     */
    @PostMapping
    @ResponseBody
    public ResultBean<Boolean> addOrUpdate(@RequestHeader String accessToken, @RequestBody BroadcastPlanAddDto vo) {
        vo.setShopId(getShopId(accessToken));
        return broadcastPlanService.addOrUpdate(vo, accessToken);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @ResponseBody
    public ResultBean<Boolean> delete(@PathVariable Long id) {
        return broadcastPlanService.delete(id);
    }


}
