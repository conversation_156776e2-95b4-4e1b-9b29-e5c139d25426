package com.mzj.py.mservice.order.service;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.mservice.order.entity.OrderItem;
import com.mzj.py.mservice.order.repository.OrderItemRepository;
import com.mzj.py.mservice.order.vo.dto.OrderPayRecordDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class OrderPayRecordService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private OrderItemRepository orderItemRepository;


    public ResultBean getByOrderId(OrderPayRecordDto dto){
        return ResultBean.successfulResult(null);
       /* StringBuilder where = new StringBuilder();
        List<Object> args = new ArrayList<>();
        if (dto != null) {
            if (dto.getOrderId() != null){
                where.append(" and r.order_id = ? ");
                args.add(dto.getOrderId());
            }
        }

        String sql = "select * from dub_order_pay_record r where 1=1";

        Result result = Result.success();

        Long count = jdbcTemplate.queryForObject("select count(1) from ("+ sql + where+") t", Long.class, args.toArray());
        if (count == null || count == 0) {
            result.addData("total", 0);
            result.addData("result",null);
            return result;
        }

        where.append("order by r.id ASC ");
        List<OrderPayRecord> deviceVos = jdbcTemplate.query(  sql + where, new BeanPropertyRowMapper<>(OrderPayRecord.class), args.toArray());
        result.addData("total", count);
        result.addData("result",deviceVos);


        return result;*/
    }



    public void createOrderPayRecord(OrderItem entity) {
        orderItemRepository.save(entity);
    }


    public void paySuccess(Long orderId,String transactionId,String successTime,Integer status) {
        orderItemRepository.paySuccess(orderId,transactionId,successTime,status);
    }


}
