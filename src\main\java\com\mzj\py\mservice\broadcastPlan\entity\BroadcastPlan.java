package com.mzj.py.mservice.broadcastPlan.entity;


import lombok.Data;

import javax.persistence.*;
import java.util.Date;


@Data
@Entity
@Table(name = "dub_broadcast_plan")
public class BroadcastPlan {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Basic
    @Column(name = "shop_id")
    private Long shopId;

    @Basic
    @Column(name = "device_ids")
    private String deviceIds;

    @Basic
    @Column(name = "start_time")
    private String startTime;

    @Basic
    @Column(name = "end_time")
    private String endTime;

    @Basic
    @Column(name = "create_time")
    private Date createTime;

    @Basic
    @Column(name = "create_user_id")
    private Long createUserId;

    @Basic
    @Column(name = "start_date")
    private String startDate;

    @Basic
    @Column(name = "end_date")
    private String endDate;

    @Basic
    @Column(name = "type")
    private String type;

    @Basic
    @Column(name = "interval_time")
    private String intervalTime;


}
