package com.mzj.py.mservice.pay.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class HttpUtils {
    private static final ObjectMapper JSON = new ObjectMapper();

    private static final Logger log = LoggerFactory.getLogger(HttpUtils.class);

    public static String sendGetRequest(String url, Map<String, String> param) throws Exception {
        StringBuffer response = new StringBuffer();
        HttpURLConnection connection = null;

        try {
            url += mapToString(param);
            System.out.println("Http url ： " + url);
            URL requestUrl = new URL(url);
            connection = (HttpURLConnection) requestUrl.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
            } else {
                throw new Exception("HTTP request failed with response code: " + responseCode);
            }
        } catch (Exception e) {
            throw e;
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public static String mapToString(Map<String, String> param) {
        if (param != null) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("?");
            Set<Map.Entry<String, String>> entries = param.entrySet();
            for (Map.Entry<String, String> map : entries) {
                String key = map.getKey();
                stringBuilder.append(key);
                stringBuilder.append("=");
                stringBuilder.append(param.get(key));
                stringBuilder.append("&");
            }
            return stringBuilder.toString();
        }
        return StringUtils.EMPTY;
    }

    public static JsonNode doGetWexin(String url) throws Exception {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpGet httpget = new HttpGet(url);
        httpget.addHeader("Content-Type", "application/json;charset=UTF-8");
        httpget.addHeader("Accept", "application/json");
        try {
            String token = WechatPayUtils.getToken("GET", new URL(url), "");
            httpget.addHeader("Authorization", token);
            CloseableHttpResponse httpResponse = httpClient.execute(httpget);
            if (httpResponse.getStatusLine().getStatusCode() == 200) {

                String jsonResult = EntityUtils.toString(httpResponse.getEntity());
                return JSON.readTree(jsonResult);
            } else {
                System.err.println(EntityUtils.toString(httpResponse.getEntity()));
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 封装post请求
     *
     * @return
     */
    public static Map<String, Object> doPostWexin(String url, String body) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        httpPost.addHeader("Content-Type", "application/json; charset=utf-8");
        httpPost.addHeader("Accept", "application/json");
        try {
            String token = WechatPayUtils.getToken("POST", new URL(url), body);
            httpPost.addHeader("Authorization", token);

            if (body == null) {
                throw new IllegalArgumentException("data参数不能为空");
            }
            StringEntity stringEntity = new StringEntity(body, "utf-8");
            httpPost.setEntity(stringEntity);

            CloseableHttpResponse httpResponse = httpClient.execute(httpPost);

            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (statusCode == 200 || statusCode == 204) {
                HttpEntity httpEntity = httpResponse.getEntity();
                if (statusCode == 200) {
                    String jsonResult = EntityUtils.toString(httpEntity);
                    HashMap hashMap = JSON.readValue(jsonResult, HashMap.class);
//                    log.info("hashMap：{}",hashMap);
                    return hashMap;
                } else {
                    // 204 No Content，不需要解析响应体
                    Map<String, Object> result = new HashMap<>();
                    result.put("code", "success");
                    result.put("message", "订单关闭成功");
                    return result;
                }
            } else {
                String errorResult = EntityUtils.toString(httpResponse.getEntity());
                log.error("微信支付错误信息: {}", errorResult);
                return JSON.readValue(errorResult, HashMap.class);
            }
        } catch     (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 发送GET请求
     *
     * @param url 请求URL
     * @return 响应的JSON字符串
     */
    public static String sendGetXOP(String url) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        try {
            HttpGet httpGet = new HttpGet(url);
            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                return EntityUtils.toString(response.getEntity(), "UTF-8");
            }
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        } finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 发送POST请求
     *
     * @param url         请求URL
     * @param jsonPayload JSON格式的请求体
     * @return 响应的JSON字符串
     */
    public static String sendPostXOP(String url, String jsonPayload) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        try {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setEntity(new StringEntity(jsonPayload, "UTF-8"));
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                return EntityUtils.toString(response.getEntity(), "UTF-8");
            }
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        } finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

}
