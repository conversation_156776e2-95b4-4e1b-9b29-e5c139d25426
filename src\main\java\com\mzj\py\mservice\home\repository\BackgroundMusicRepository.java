package com.mzj.py.mservice.home.repository;


import com.mzj.py.mservice.home.entity.BackgroundMusic;
import com.mzj.py.mservice.home.vo.MusicVo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface BackgroundMusicRepository extends JpaRepository<BackgroundMusic,Long>, JpaSpecificationExecutor<BackgroundMusic> {

    @Query(value = "select new com.mzj.py.mservice.home.vo.MusicVo(m.id,m.name,m.musicUrl,m.musicTime) " +
            "from BackgroundMusic m right join UserFollowBgm uf on m.id = uf.bgmId where uf.userId=:userId")
    List<MusicVo> findFollowBgm(Long userId);
}
