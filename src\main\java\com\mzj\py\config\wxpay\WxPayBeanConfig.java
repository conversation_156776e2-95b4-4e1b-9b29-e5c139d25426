package com.mzj.py.config.wxpay;

import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.notification.NotificationConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.service.payments.jsapi.JsapiServiceExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.stream.Collectors;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-04-15-08:43
 */
@Configuration
public class WxPayBeanConfig {
    @Autowired
    private WxPayConfig wxPayConfig;

    @Bean("jsapiService")
    public JsapiServiceExtension getWechatPayService() {
        JsapiServiceExtension service = new JsapiServiceExtension.Builder().config(getConfig()).build();
        return service;
    }

    @Bean("wechatNotifyParser")
    public NotificationParser getNotifyParser() {
        NotificationParser parser = new NotificationParser((NotificationConfig) getConfig());
        return parser;
    }
    public PrivateKey getPrivateKey(){
        try {
            InputStream inputStream = new ClassPathResource(wxPayConfig.getPrivateKeyPath()
                    .replace("classpath:", "")).getInputStream();

            String content = new BufferedReader(new InputStreamReader(inputStream))
                    .lines().collect(Collectors.joining(System.lineSeparator()));
            String privateKey = content.replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replaceAll("\\s+", "");
            KeyFactory kf = KeyFactory.getInstance("RSA");

            PrivateKey finalPrivateKey = kf.generatePrivate(
                    new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKey)));

            return finalPrivateKey;

        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("当前Java环境不支持RSA", e);
        } catch (InvalidKeySpecException e) {
            throw new RuntimeException("无效的密钥格式");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    @Bean
    public Config getConfig() {
        Config config =
                new RSAAutoCertificateConfig.Builder()
                        .merchantId(wxPayConfig.getMerchantId())
                        .privateKey(getPrivateKey())
                        .merchantSerialNumber(wxPayConfig.getMerchantSerialNumber())
                        .apiV3Key(wxPayConfig.getApiV3key())
                        .build();
        return config;
    }
}
//
//
//
//
//    @Bean("wechatPayClient")
//    public CloseableHttpClient getWechatPayClient(){
//        WechatPayHttpClientBuilder builder = WechatPayHttpClientBuilder.create()
//                .withMerchant(wxPayConfig.getMerchantId(), wxPayConfig.getMerchantSerialNumber(),getmerchantPrivateKey())
//                .withValidator(new WechatPay2Validator(getCertificatesVerifier()));
//        CloseableHttpClient httpClient = builder.build();
//        return httpClient;
//    }
//    @Bean
//    public Verifier getCertificatesVerifier(){
//        CertificatesManager certificatesManager = CertificatesManager.getInstance();
//        try {
//            certificatesManager.putMerchant(wxPayConfig.getMerchantId(), new WechatPay2Credentials(wxPayConfig.getMerchantId(),
//                    new PrivateKeySigner(wxPayConfig.getMerchantSerialNumber(), getmerchantPrivateKey())), wxPayConfig.getApiV3key().getBytes(StandardCharsets.UTF_8));
//            Verifier verifier = certificatesManager.getVerifier(wxPayConfig.getMerchantId());
//            return  verifier;
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//    }
//

//}
