package com.mzj.py.mservice.home.vo;

import com.mzj.py.mservice.wxuser.entity.IdEntity;

import java.util.Date;

/**
 * 背景音乐
 * @author: duanjinze
 * @date: 2022/11/10 12:00
 * @version: 1.0
 */
public class UserBackgroundMusicVo extends IdEntity {


    /**
     * 名称
     */
    private String name;

    /**
     * 文件url
     */

    private String url;

    /**
     * 0未删除 1已删除
     */

    private Integer delStatus;

    /**
     * 创建人id
     */

    private Long userId;

    /**
     * 创建时间
     */
    private Date createTime;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getDelStatus() {
        return delStatus;
    }

    public void setDelStatus(Integer delStatus) {
        this.delStatus = delStatus;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
