package com.mzj.py.mservice.broadcastPlan.entity;


import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name = "dub_broadcast_plan_voice_work_ref")
public class BroadcastPlanVoiceWorkRef {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Basic
    @Column(name = "plan_id")
    private Long planId;

    @Basic
    @Column(name = "voice_work_id")
    private Long voiceWorkId;

    @Basic
    @Column(name = "sort")
    private Integer sort;


}
