package com.mzj.py.mservice.work.vo;

import lombok.Data;

@Data
public class WorkInfoVo {
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 发音人名称
     */
    private String anchorName;

    /**
     * 发音人id
     */
    private Long anchorId;

    /**
     * 发音人参数
     */
    private String voiceName;

    /**
     *音量
     */
    private Integer pitch;

    /**
     *语调
     */
    private Integer volume;

    /**
     *语速
     */
    private Integer speed;

    /**
     *作品时长
     */
    private Integer voiceTime;

    /**
     * 背景音乐id
     */
    private Long backgroundMusicId;

    /**
     * 背景音乐名称
     */
    private String musicName;

    /**
     *背景音乐音量
     */
    private Integer backgroundMusicVolume;

    /**
     *音频地址
     */
    private String fileUrl;

    /**
     *作品文本
     */
    private String content;

    private Long shopId;

    private Long userId;
}
