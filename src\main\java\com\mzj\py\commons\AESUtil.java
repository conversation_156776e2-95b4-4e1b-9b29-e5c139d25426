package com.mzj.py.commons;

import org.apache.commons.codec.binary.Base64;
import org.springframework.util.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;

public class AESUtil {

	// 密钥 (需要前端和后端保持一致)
	private static String KEY;

	private static AESUtil instance;

	public static AESUtil getInstance(String secretKey) {
		KEY = secretKey;
		if (instance == null) {
			instance = new AESUtil();
		}
		return instance;
	}

	// 算法
	private static final String ALGORITHMSTR = "AES/CBC/PKCS5Padding";

	/**
	 * aes解密
	 *
	 * @param encrypt 内容
	 * @return
	 * @throws Exception
	 */
	public static String aesDecrypt(String encrypt, String ivParameter) {
		try {
			return aesDecrypt(encrypt, ivParameter, KEY);
		} catch (Exception e) {
			e.printStackTrace();
			return "";
		}
	}

	/**
	 * aes加密
	 *
	 * @param content
	 * @return
	 * @throws Exception
	 */
	public static String aesEncrypt(String content, String ivParameter) {
		try {
			return aesEncrypt(content, ivParameter, KEY);
		} catch (Exception e) {
			e.printStackTrace();
			return "";
		}
	}

	/**
	 * 将byte[]转为各种进制的字符串
	 *
	 * @param bytes byte[]
	 * @param radix 可以转换进制的范围，从Character.MIN_RADIX到Character.MAX_RADIX，超出范围后变为10进制
	 * @return 转换后的字符串
	 */
	public static String binary(byte[] bytes, int radix) {
		return new BigInteger(1, bytes).toString(radix);// 这里的1代表正数
	}

	/**
	 * base 64 encode
	 *
	 * @param bytes 待编码的byte[]
	 * @return 编码后的base 64 code
	 */
	public static String base64Encode(byte[] bytes) {
		return Base64.encodeBase64String(bytes);
	}

	/**
	 * base 64 decode
	 *
	 * @param base64Code 待解码的base 64 code
	 * @return 解码后的byte[]
	 * @throws Exception
	 */
	public static byte[] base64Decode(String base64Code) throws Exception {
		return StringUtils.isEmpty(base64Code) ? null : Base64.decodeBase64(base64Code);
	}

	/**
	 * AES加密
	 *
	 * @param content    待加密的内容
	 * @param encryptKey 加密密钥
	 * @return 加密后的byte[]
	 * @throws Exception
	 */
	public static byte[] aesEncryptToBytes(String content, String ivParameter, String encryptKey) throws Exception {
		KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
		keyGenerator.init(128);
		Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
		IvParameterSpec iv = new IvParameterSpec(ivParameter.getBytes());// 使用CBC模式，需要一个向量iv，可增加加密算法的强度
		cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(encryptKey.getBytes(), "AES"), iv);

		return cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
	}

	/**
	 * AES加密为base 64 code
	 *
	 * @param content    待加密的内容
	 * @param encryptKey 加密密钥
	 * @return 加密后的base 64 code
	 * @throws Exception
	 */
	public static String aesEncrypt(String content, String ivParameter, String encryptKey) throws Exception {
		return base64Encode(aesEncryptToBytes(content, ivParameter, encryptKey));
	}

	/**
	 * AES解密
	 *
	 * @param encryptBytes 待解密的byte[]
	 * @param decryptKey   解密密钥
	 * @return 解密后的String
	 * @throws Exception
	 */
	public static String aesDecryptByBytes(byte[] encryptBytes, String ivParameter, String decryptKey)
			throws Exception {
		KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
		keyGenerator.init(128);

		Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
		IvParameterSpec iv = new IvParameterSpec(ivParameter.getBytes());// 使用CBC模式，需要一个向量iv，可增加加密算法的强度
		cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(decryptKey.getBytes(), "AES"), iv);
		byte[] decryptBytes = cipher.doFinal(encryptBytes);
		return new String(decryptBytes);
	}

	/**
	 * 将base 64 code AES解密
	 *
	 * @param encryptStr 待解密的base 64 code
	 * @param decryptKey 解密密钥
	 * @return 解密后的string
	 * @throws Exception
	 */
	public static String aesDecrypt(String encryptStr, String ivParameter, String decryptKey) throws Exception {
		return StringUtils.isEmpty(encryptStr) ? null
				: aesDecryptByBytes(base64Decode(encryptStr), ivParameter, decryptKey);
	}

	/**
	 * 测试
	 */
	public static void main(String[] args) throws Exception {
		AESUtil aesUtil = AESUtil.getInstance("89E84AC5D9AE189A");
		String openId = "oQGo46FGTMZI2oq85u2GL6Vx4r6M";
		String UnionId = "oYPNW6MujvmzlALqINWdzStJz8sI";
		String AppId = "wx4384ceefb948380a";
		System.out.println("加密前：" + UnionId + AppId + openId);
		System.out.println("密钥：" + KEY);
		String encrypt = aesUtil.aesEncrypt(UnionId + AppId + openId, "1234567890000000");
		System.out.println("加密后：" + encrypt);
		String decrypt = aesUtil.aesDecrypt(encrypt, "1234567890000000");
		System.out.println("解密后：" + decrypt);
	}
}
