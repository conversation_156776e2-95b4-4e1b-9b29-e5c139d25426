package com.mzj.py.mservice.wxuser.repository;


import com.mzj.py.mservice.wxuser.entity.FollowAnchor;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface FollowAnchorRepository extends PagingAndSortingRepository<FollowAnchor,Long>, JpaSpecificationExecutor<FollowAnchor> {

	List<FollowAnchor> findByAnchorIdAndUserId(Long anchorId, Long userId);
}
