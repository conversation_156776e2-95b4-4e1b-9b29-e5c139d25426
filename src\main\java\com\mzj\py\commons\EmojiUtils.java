package com.mzj.py.commons;

public class EmojiUtils {
	private EmojiUtils() {
	}

	public static String filterEmoji(String source) {
		// 判断是否为表情
//		if (hasEmoji(source)) {
//			return null;
//			// throw new Exception(HttpStatus.BAD_REQUEST, "200004", "暂不支持表情搜索");
//		}
		source = source.replaceAll("[^\\u0000-\\uFFFF]", "");
		return source;
	}

	/**
	 * 判断是否为表情
	 * 
	 * @param content
	 * @return
	 */
//	private static boolean hasEmoji(String content) {
//		Pattern pattern = Pattern.compile("[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]");
//		Matcher matcher = pattern.matcher(content);
//		if (matcher.find()) {
//			return true;
//		}
//		return false;
//	}
}
