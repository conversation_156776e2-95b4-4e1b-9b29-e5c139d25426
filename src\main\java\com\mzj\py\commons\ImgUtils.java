package com.mzj.py.commons;

import java.awt.BasicStroke;
import java.awt.Color;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.color.ColorSpace;
import java.awt.image.BufferedImage;
import java.awt.image.ColorConvertOp;
import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

import javax.imageio.ImageIO;

import org.apache.commons.lang3.StringUtils;
import org.krysalis.barcode4j.impl.code128.Code128Bean;
import org.krysalis.barcode4j.output.bitmap.BitmapCanvasProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 图片生成工具类
 */
@Component
public class ImgUtils {

	private static final Logger LOG = LoggerFactory.getLogger(ImgUtils.class);

	// logo默认边框颜色
	private static final Color DEFAULT_BORDERCOLOR = Color.WHITE;
	// logo默认边框宽度
	private static final int DEFAULT_BORDER = 5;
	// logo大小默认为照片的1/6
	private static final int DEFAULT_LOGOPART = 6;

	// 条形码默认属性
	private static final double moduleWidth = 0.792;
	private static final int resolution = 150;
	private static final String format = "image/png";

	/**
	 * 在原图加上Logo图片生成新的图片
	 * 
	 * @param qrcUrl
	 *            原图
	 * @param logoUrl
	 *            logo图
	 * @param path
	 *            可指定新图路径 默认为空
	 * @return
	 */
	public static File addLogoQRCode(String qrcUrl, String logoUrl, String path) {

		File qrcFile = getFileByUrl(qrcUrl, "jpg");

		if (!qrcFile.isFile()) {
			LOG.error("========== addLogoQRCode ========= 图片生成失败,file not find ! qrc ={}", qrcUrl);
			return null;
		}

		if (StringUtils.isBlank(logoUrl)) {
//			LOG.warn("========== addLogoQRCode ========= 无logo图片,logoUrl={}", logoUrl);
			return qrcFile;
		}

		File logoFile = getFileByUrl(logoUrl, "jpeg");
		if (!logoFile.isFile()) {
			LOG.warn("========== addLogoQRCode ========= 图片生成失败,file not find ! logo={}", logoUrl);
			return qrcFile;
		}

		try {
			// 读取原图片,并构建绘图对象
			BufferedImage image = toGray(ImageIO.read(qrcFile));
			Graphics2D g = image.createGraphics();

			// 读取Logo图片
			BufferedImage logo = ImageIO.read(logoFile);

			// 设置logo宽高
			int logoWidth = image.getWidth() / DEFAULT_LOGOPART;
			int logoHeight = image.getWidth() / DEFAULT_LOGOPART;

			// 计算图片放置位置
			int x = (image.getWidth() - logoWidth) / 2;
			int y = (image.getHeight() - logoHeight) / 2;

			// 开始绘制图片
			g.drawImage(logo, x, y, logoWidth, logoHeight, null);
			g.drawRoundRect(x, y, logoWidth, logoHeight, 10, 10);
			g.setStroke(new BasicStroke(DEFAULT_BORDER));
			g.setColor(DEFAULT_BORDERCOLOR);
			g.drawRect(x, y, logoWidth, logoHeight);
			g.dispose();

			File file = null;
			if (StringUtils.isBlank(path)) {
				file = File.createTempFile("pattern", "." + "jpg");
			} else {
				file = new File(path);
			}
			ImageIO.write(image, "jpeg", file);
			return file;
		} catch (IOException e) {
			LOG.error(e.getMessage(),e);
		}
		return null;
	}

	/**
	 * 生成条形码
	 * 
	 * @param code
	 * @param path
	 * @return
	 */
	public static File createBarCode(String code, String path) {
		try {

			if (StringUtils.isBlank(code)) {
				LOG.error("========== createBarCode ========= 生成条形码,code is null ! code ={}", code);
				return null;
			}

			File file = null;
			if (StringUtils.isBlank(path)) {
				file = File.createTempFile(code, "." + "jpg");
			} else {
				file = new File(path);
			}

			OutputStream ous = new FileOutputStream(file);

			Code128Bean bean = new Code128Bean();

			bean.setModuleWidth(moduleWidth);
			bean.doQuietZone(false);
			bean.setFontSize(3);
			BitmapCanvasProvider canvas = new BitmapCanvasProvider(ous, format, resolution, BufferedImage.TYPE_BYTE_BINARY, false, 0);
			// 生成条码
			bean.generateBarcode(canvas, code);
			canvas.finish();
			ous.close();
			return file;
		} catch (IOException e) {
			LOG.error(e.getMessage(),e);
		}
		return null;
	}

	/**
	 * 多图片 竖向合成
	 * 
	 * @param files
	 * @param path
	 * @return
	 */
	public static byte[] imgMergeVertical(List<File> files, String path) {
		try {
			Integer allWidth = 0; // 计算画布总宽
			Integer allHeight = 0; // 计算画布总高
			List<BufferedImage> imgs = new ArrayList<>();
			for (int i = 0; i < files.size(); i++) {
				imgs.add(ImageIO.read(files.get(i)));
				// 因为是竖向合成，拿图片里最大的一个宽度就行
				allWidth = Math.max(allWidth, imgs.get(i).getWidth());
				allHeight += imgs.get(i).getHeight();
			}
			BufferedImage combined = new BufferedImage(allWidth, allHeight, BufferedImage.TYPE_INT_RGB);
			Graphics g = combined.getGraphics();
			// 设置画布背景颜色 ，默认黑色
			g.setColor(Color.white);
			g.fillRect(0, 0, allWidth, allHeight);
			Integer height = 0;
			for (int i = 0; i < imgs.size(); i++) {
				if (i == 0) {
					g.drawImage(imgs.get(i), 0, height, null);
				} else {
					g.drawImage(imgs.get(i), 30, height, null);
				}
				
				height += imgs.get(i).getHeight();
			}

			if (StringUtils.isNotBlank(path)) {
				ImageIO.write(combined, "jpg", new File(path));
			}
			ByteArrayOutputStream out = new ByteArrayOutputStream();
			ImageIO.write(combined, "jpg", out);
			return out.toByteArray();
		} catch (Exception e) {
			LOG.error(e.getMessage(),e);
		}
		return null;
	}
	
	  public static byte[] httpFile(String strurl){
		  
		  ByteArrayOutputStream baos = null;
	        try{
	            URL url = new URL(strurl);
	            HttpURLConnection conn = (HttpURLConnection)url.openConnection();
	            conn.setRequestMethod("GET");
	            conn.setConnectTimeout(5 * 1000);
	            InputStream inStream = conn.getInputStream();
	            
	            baos = new ByteArrayOutputStream();
				
				byte[] buff = new byte[100];
		        int rc = 0;
		        while ((rc = inStream.read(buff, 0, 100)) > 0) {
		        	baos.write(buff, 0, rc);
		        }
				return  baos.toByteArray();
	        }catch (Exception e){
	            e.printStackTrace();
	            return null;
	        }finally {
				if(baos != null) {
					try {
						baos.close();
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
			}

	    }

	/**
	 * 多图片 横向向合成
	 * 
	 * @param files
	 * @param path
	 * @return
	 */
	public static byte[] imgMergeHorizontal(List<File> files, String path) {
		try {
			Integer allWidth = 0;
			Integer allHeight = 0;
			List<BufferedImage> imgs = new ArrayList<>();
			for (int i = 0; i < files.size(); i++) {
				imgs.add(ImageIO.read(files.get(i)));
				allHeight = Math.max(allHeight, imgs.get(i).getHeight());
				allWidth += imgs.get(i).getWidth();
			}
			BufferedImage combined = new BufferedImage(allWidth, allHeight, BufferedImage.TYPE_INT_RGB);
			Graphics g = combined.getGraphics();
			g.setColor(Color.white);
			g.fillRect(0, 0, allWidth, allHeight);
			Integer width = 0;
			for (int i = 0; i < imgs.size(); i++) {
				g.drawImage(imgs.get(i), width, 0, null);
				// 设置横向间距
				width += imgs.get(i).getWidth() + 10;
			}
			if (StringUtils.isNotBlank(path)) {
				ImageIO.write(combined, "jpg", new File(path));
			}
			ByteArrayOutputStream out = new ByteArrayOutputStream();
			ImageIO.write(combined, "jpg", out);
			return out.toByteArray();
		} catch (Exception e) {
			LOG.error(e.getMessage(),e);
		}
		return null;
	}

	/**
	 * 通过链接获取图片对象
	 * 
	 * @param fileUrl
	 * @param suffix
	 * @return
	 */
	private static File getFileByUrl(String fileUrl, String suffix) {
		ByteArrayOutputStream outStream = new ByteArrayOutputStream();
		BufferedOutputStream stream = null;
		InputStream inputStream = null;
		File file = null;
		try {
			URL imageUrl = new URL(fileUrl);

			HttpURLConnection conn = (HttpURLConnection) imageUrl.openConnection();

			conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");

			inputStream = conn.getInputStream();

			byte[] buffer = new byte[1024];

			int len = 0;

			while ((len = inputStream.read(buffer)) != -1) {
				outStream.write(buffer, 0, len);
			}

			file = File.createTempFile("pattern", "." + suffix);

			FileOutputStream fileOutputStream = new FileOutputStream(file);

			stream = new BufferedOutputStream(fileOutputStream);

			stream.write(outStream.toByteArray());

		} catch (Exception e) {
			LOG.error(e.getMessage(),e);
		} finally {
			try {
				if (inputStream != null)
					inputStream.close();
				if (stream != null)
					stream.close();
				outStream.close();
			} catch (Exception e) {
				LOG.error(e.getMessage(),e);
			}
		}
		return file;
	}

	/**
	 * 转化图片颜色
	 * 
	 * @param srcImg
	 * @return
	 */
	private static BufferedImage toGray(BufferedImage srcImg) {
		return new ColorConvertOp(ColorSpace.getInstance(ColorSpace.CS_sRGB), null).filter(srcImg, null);
	}
}
