package com.mzj.py.mservice.home.entity;

import com.mzj.py.mservice.wxuser.entity.IdEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;
@Data
@Entity
@Table(name = "dub_template_type")
public class DubTemplateType extends IdEntity {
    /**
     * 类型名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 0未删除 1已删除
     */
    @Column(name = "del_status",columnDefinition = "smallint")
    private Integer delStatus;

    /**
     * 创建人
     */
    @Column(name = "create_id")
    private Long createId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;
}
