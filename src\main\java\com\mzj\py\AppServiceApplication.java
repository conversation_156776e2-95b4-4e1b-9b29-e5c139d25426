package com.mzj.py;


import java.io.File;

import com.mzj.py.mservice.websocket.config.NettyConfig;
import com.mzj.py.mservice.websocket.server.WebSocketNettyServer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import com.mzj.py.filter.AuthFilter;

import javax.annotation.Resource;

@EnableScheduling
@SpringBootApplication
@EntityScan({"com.mzj.py"})
public class AppServiceApplication  implements CommandLineRunner{
    @Value("${server.tomcat.basedir}")
    private String tomcatBasedir;
    @Value("${spring.application.name}")
    private String applicationName;

    @Resource
    private WebSocketNettyServer webSocketNettyServer;

    @Resource
    private NettyConfig nettyConfig;


    public static void main(String[] args) {
        new SpringApplicationBuilder(AppServiceApplication.class).run(args);
    }

    /**
     * 支持跨域访问
     *
     * @return
     */
    @Bean
    public CorsFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();
        config.addAllowedOrigin("*");
        config.setAllowCredentials(true);
        config.addAllowedMethod("*");
        config.addAllowedHeader("*");

        UrlBasedCorsConfigurationSource configSource = new UrlBasedCorsConfigurationSource();
        configSource.registerCorsConfiguration("/**", config);

        return new CorsFilter(configSource);
    }

    @Bean
    public AuthFilter authFilter() {
        return new AuthFilter();
    }

    //初始化tomcat上传文件目录
    @Bean
    public String initTomcatUploadDir() {
        String dir = tomcatBasedir + "work/Tomcat/localhost/" + applicationName;
        File targetFile = new File(dir);
        if (!targetFile.isDirectory()) {
            targetFile.mkdirs();
        }
        return dir;
    }


    @Override
    public void run(String... args) throws Exception {
        new Thread(new Runnable() {
            @Override
            public void run() {
                webSocketNettyServer.start(nettyConfig.getPort());
            }
        }).start();
    }
}

