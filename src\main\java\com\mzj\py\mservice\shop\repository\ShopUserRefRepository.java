package com.mzj.py.mservice.shop.repository;

import com.mzj.py.mservice.shop.entity.ShopUserRef;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @date: 2025/3/2
 * @description:
 */
public interface ShopUserRefRepository extends JpaRepository<ShopUserRef,Long>, JpaSpecificationExecutor<ShopUserRef> {

    @Modifying
    @Query("delete from ShopUserRef where shopId in ?1")
    void deleteAllByShopIdIn(List<Long> shopIds);

    @Query("select t from ShopUserRef t where t.userId = ?1")
    List<ShopUserRef> queryByUserId(Long id);

    @Modifying
    @Query("delete from ShopUserRef where shopId = ?1")
    int deleteByShopId(Long id);

    ShopUserRef findByShopIdAndRole(Long id, int role);

    ShopUserRef findByShopIdAndUserId(Long storeId, Long userId);

    boolean existsByUserIdAndRoleAndShopIdIn(Long userId, Integer code, List<Long> allowShopIds);
}

