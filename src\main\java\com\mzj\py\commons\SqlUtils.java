package com.mzj.py.commons;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * SQL拼接工具类
 * 
 * @date: 2019-12-04
 */
public class SqlUtils {
	public static <T> String foreach(String open, String close, String separator, Iterable<T> collection) {
		Iterator<T> it = collection.iterator();
		if (!it.hasNext())
			return open + close;

		StringBuilder sb = new StringBuilder();
		sb.append(open);
		for (;;) {
			T e = it.next();
			sb.append(e);
			if (!it.hasNext())
				return sb.append(close).toString();
			sb.append(separator);
		}
	}

	public static String foreachIn(int size) {
		List<Character> args = new ArrayList<>(size);
		for (int i = 0; i < size; i++) {
			args.add('?');
		}
		return foreach("(", ")\n", ",", args);
	}
}
