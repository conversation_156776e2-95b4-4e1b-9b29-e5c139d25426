package com.mzj.py.mservice.installationPackage.service;


import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.mservice.home.entity.Device;
import com.mzj.py.mservice.installationPackage.entity.InstallationPackage;
import com.mzj.py.mservice.installationPackage.repository.InstallationPackageRepository;
import com.mzj.py.mservice.installationPackage.vo.installationPackage.InstallationPackageVo;
import com.mzj.py.mservice.installationPackage.vo.installationPackage.dto.InstallationPackageAddDto;
import com.mzj.py.mservice.redis.RedisService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class InstallationPackageService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private RedisService redisService;

    @Autowired
    private InstallationPackageRepository installationPackageRepository;

    public ResultBean list(String accessToken, Integer pageSize, Integer pageNumber){

        TokenRedisVo tokenVo = redisService.findTokenVo(accessToken);
        if (tokenVo == null) {
            return ResultBean.failedResultWithMsg("用户不存在");
        }

        StringBuilder where = new StringBuilder();
        List<Object> args = new ArrayList<>();


        String sql = "select \n" +
                "p.id,\n" +
                "p.version_name as version,\n" +
                "p.remark as description,\n" +
                "p.package_url as updatePackage,\n" +
                "p.create_time as updateTime,\n" +
                "count(pl.id) updateDeviceNum, \n" +
                "group_concat(pl.device_id) deviceIds " +
                "from dub_installation_package p " +
                "left join dub_installation_package_log pl on pl.package_id = p.id " +
                "where 1=1 ";


        Map<String, Object> map = new HashMap<>();

        where.append(" GROUP BY p.id  ORDER BY p.create_time DESC ");

        Long count = jdbcTemplate.queryForObject("select count(1) from(" + sql + where + ")t ", Long.class, args.toArray());
        if (count == 0) {
            map.put("count", 0);
            map.put("result", null);
            return ResultBean.successfulResult(map);
        }

        where.append(" limit ? offset ? ");
        args.add(pageSize);
        args.add(pageSize * pageNumber);
        List<InstallationPackageVo> devices = jdbcTemplate.query(sql + where, new BeanPropertyRowMapper<>(InstallationPackageVo.class), args.toArray());
        map.put("count", count);
        map.put("result", devices);
        return ResultBean.successfulResult(map);

    }


    public ResultBean addOrUpdate(InstallationPackageAddDto vo,String accessToken) {

        TokenRedisVo tokenVo = redisService.findTokenVo(accessToken);
        if (tokenVo == null) {
            return ResultBean.failedResultWithMsg("用户不存在");
        }
        if (vo == null){
            return ResultBean.failedResultWithMsg("数据不能为空");
        }

        if (StringUtils.isBlank(vo.getVersion())){
            return ResultBean.failedResultWithMsg("版本号不能为空");
        }
        if (StringUtils.isBlank(vo.getDescription())){
            return ResultBean.failedResultWithMsg("更新说明不能为空");
        }
        if (StringUtils.isBlank(vo.getUpdatePackage())){
            return ResultBean.failedResultWithMsg("更新包不能为空");
        }

        Date date = new Date();
        if (vo.getId() == null){
            //新增
            InstallationPackage device = new InstallationPackage();
            device.setVersionName(vo.getVersion());
            device.setRemark(vo.getDescription());
            device.setPackageUrl(vo.getUpdatePackage());
           device.setCreateUserId(tokenVo.getId());
            device.setCreateTime(date);
            installationPackageRepository.save(device);
        }else {
            Optional<InstallationPackage> byId = installationPackageRepository.findById(vo.getId());
            if (byId.isPresent()) {
                //修改
                InstallationPackage device = byId.get();
                device.setVersionName(vo.getVersion());
                device.setRemark(vo.getDescription());
                device.setPackageUrl(vo.getUpdatePackage());

                installationPackageRepository.save(device);
            }
        }
        return ResultBean.successfulResult(true);
    }

    public ResultBean delete(Long id){
         installationPackageRepository.deleteById(id);
        return ResultBean.successfulResult(true);
    }

}
