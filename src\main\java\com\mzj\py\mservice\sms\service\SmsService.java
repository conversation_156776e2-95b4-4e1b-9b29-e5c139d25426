package com.mzj.py.mservice.sms.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.mzj.py.commons.exception.CustomException;
import com.mzj.py.config.sms.SmsConfig;
import com.mzj.py.mservice.redis.RedisService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 阿里云-短信
 *
 * <AUTHOR>
 * @createDate: 2021年9月10日 下午6:41:35
 * @version: 1.0
 *
 */
@Service
public class SmsService {
    private Logger LOG = LoggerFactory.getLogger(SmsService.class);
    @Autowired
    private Client client;
    @Autowired
    private SmsConfig smsConfig;
    @Autowired
    private CodeService codeService;
    @Autowired
    private RedisService redisService;

    /**
     *
     * @param phoneNumbers
     *            接收短信的手机号码。 格式：无任何前缀的11位手机号码，例如1381111****。
     *            支持对多个手机号码发送短信，手机号码之间以英文逗号（,）分隔。上限为1000个手机号码。
     *            批量调用相对于单条调用及时性稍有延迟。
     * @param templateCode
     *            短信模板ID。请在控制台国内消息页面中的模板管理页签下模板CODE一列查看。
     * @param signName
     *            短信签名名称。请在控制台国内消息页面中的签名管理页签下签名名称一列查看。
     * @param templateParam
     *            短信模板变量对应的实际值，JSON格式。支持传入多个参数，示例：{"name":"张三","number":
     *            "15038****76"}。
     * <AUTHOR>
     * @throws CustomException
     * @date 2021年9月10日
     */
    public String sendSms(String phoneNumbers, String templateCode, String signName, String templateParam)
            throws CustomException {
        if (StringUtils.isEmpty(phoneNumbers)) {
            LOG.info("SmsService sendSms, phoneNumbers is null, templateParam={}", templateParam);
            return null;
        }
        SendSmsRequest sendSmsRequest = new SendSmsRequest();
        if (StringUtils.isBlank(templateCode)) {
            sendSmsRequest.setTemplateCode(smsConfig.getLoginTemplateCode());
        } else {
            sendSmsRequest.setTemplateCode(templateCode);
        }

        if (StringUtils.isBlank(signName)) {
            sendSmsRequest.setSignName(smsConfig.getSignName());
        } else {
            sendSmsRequest.setSignName(signName);
        }

        String code = codeService.getCode();
        if (StringUtils.isBlank(templateParam)) {
            Map<String, Object> map = new HashMap<>();
            // 自动生成验证码
            map.put("code", code);
            templateParam = JSON.toJSONString(map);
        }else{
            code = templateParam;
        }

        sendSmsRequest.setPhoneNumbers(phoneNumbers);
        sendSmsRequest.setTemplateParam(templateParam);
        // 复制代码运行请自行打印 API 的返回值
        try {
			/*SendSmsResponse response = client.sendSms(sendSmsRequest);
			LOG.info("SmsService sendSms, phoneNumbers={}, templateCode={}, signName={}, templateParam={}, response={}",
					phoneNumbers, sendSmsRequest.getTemplateCode(), sendSmsRequest.getSignName(), templateParam,
					JSON.toJSONString(response));
			if (response.getBody().getCode().equals("OK")) {
				// 有效时间五分钟
				String[] phones = phoneNumbers.split(",");
				for (int i = 0; i < phones.length; i++) {
					redisService.putSmsCode(phones[i], code);
				}
				return "success";
			}*/

            // 临时使用固定验证码
            String[] phones = phoneNumbers.split(",");
            for (int i = 0; i < phones.length; i++) {
                redisService.putSmsCode(phones[i], "2345");
            }
            return "success";
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            throw new CustomException("短信发送失败");
        }

        //return "fail";
    }
}
