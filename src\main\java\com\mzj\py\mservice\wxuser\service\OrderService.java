/*
package com.mzj.cc.mservice.wxuser.service;


import com.alibaba.fastjson.JSON;
import com.mzj.cc.commons.HttpClientUtil;
import com.mzj.cc.commons.RedisUtils;
import com.mzj.cc.commons.WeiXinUtil;
import com.mzj.cc.commons.constant.RedisKeyConstant;
import com.mzj.cc.commons.entity.device.DeviceType;
import com.mzj.cc.commons.entity.marketing.Coupon;
import com.mzj.cc.commons.entity.order.Order;
import com.mzj.cc.commons.entity.order.OrderPayInfo;
import com.mzj.cc.commons.entity.trader.LaunchPoint;
import com.mzj.cc.commons.entity.trader.PayFirstPlans;
import com.mzj.cc.commons.entity.user.UserCoupon;
import com.mzj.cc.commons.entity.user.WxUser;
import com.mzj.cc.commons.entity.user.WxUserWallet;
import com.mzj.cc.commons.enums.*;
import com.mzj.cc.commons.enums.cmd.CmdEnum;
import com.mzj.cc.commons.enums.cmd.StopCmdEnum;
import com.mzj.cc.commons.util.*;
import com.mzj.cc.commons.vo.cmd.CmdBaseVO;
import com.mzj.cc.commons.vo.cmd.StopCmdVO;
import com.mzj.cc.commons.vo.redis.*;
import com.mzj.cc.mservice.device.repository.DeviceTypeRepository;
import com.mzj.cc.mservice.device.service.DeviceService;
import com.mzj.cc.mservice.marketing.repository.CouponRepository;
import com.mzj.cc.mservice.marketing.repository.UserCouponRepository;
import com.mzj.cc.mservice.mq.service.RabbitMqService;
import com.mzj.cc.mservice.trader.repository.LaunchPointRepository;
import com.mzj.cc.mservice.trader.repository.PayFirstPlansRepository;
import com.mzj.cc.mservice.wxuser.repository.OrderPayInfoRepository;
import com.mzj.cc.mservice.wxuser.repository.OrderRepository;
import com.mzj.cc.mservice.wxuser.repository.WxUserRepository;
import com.mzj.cc.mservice.wxuser.repository.WxUserWalletRepository;
import com.mzj.cc.mservice.wxuser.vo.OrderPayInfoReqVo;
import com.mzj.cc.mservice.wxuser.vo.OrderReqVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;


@Service
public class OrderService {
    private Logger LOG = LoggerFactory.getLogger(OrderService.class);
    public static final int COUPON_LEAST_PAY_FEE = 100;//订单加上优惠券后现金至少得支付1元 优惠券才能用
    @Value("${weixin.api.pay.unifiedorder}")
    private String unifiedorderUrl;

    @Value("${wx.pay.notify.url}")
    private String wxPayNotifyUrl;

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private RabbitMqService rabbitMqService;
    @Autowired
    private PayFirstPlansRepository payFirstPlansRepository;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private OrderPayInfoRepository orderPayInfoRepository;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private WxUserWalletRepository wxUserWalletRepository;
    @Autowired
    private WxUserRepository wxUserRepository;
    @Autowired
    private LaunchPointRepository launchPointRepository;
    @Autowired
    private DeviceTypeRepository deviceTypeRepository;
    @Autowired
    private UserCouponRepository userCouponRepository;
    @Autowired
    private WxUserWalletService wxUserWalletService;
    @Autowired
    private CouponRepository couponRepository;
    @Autowired
    private DeviceService deviceService;


    */
/**
     * 创建订单
     *
     * @param vo
     * @return
     *//*

    public ResultBean<Object> createOrder(OrderReqVo vo) {
        LOG.info("OrderService createOrder params = {} ", vo.toString());
        boolean deleteCouponFlag = false;
        try {
            if (vo.getDeviceId() == null) {
                return ResultBean.failedResultOfParamWithMsg("设备id不能为空");
            }

            if (vo.getPayFirstPlansId() == null) {
                return ResultBean.failedResultOfParamWithMsg("套餐id不能为空");
            }

            if (vo.getPayCoin() == null) {
                return ResultBean.failedResultOfParamWithMsg("支付的币不能为空");
            }

            if (vo.getPayCoin() < 0) {
                return ResultBean.failedResultOfParamWithMsg("支付的币不能小于0");
            }

            if (vo.getPayFee() == null) {
                return ResultBean.failedResultOfParamWithMsg("支付金额不能为空");
            }

            if (vo.getPayFee() < 0) {
                return ResultBean.failedResultOfParamWithMsg("支付金额不能小于0");
            }

            if (vo.getUserId() == null) {
                return ResultBean.failedResultOfParamWithMsg("用户id不能为空");
            }

            if (vo.getPayFirstPlansId() == null) {
                return ResultBean.failedResultOfParamWithMsg("套餐id不能为空");
            }

            if (vo.getMinute() == null) {
                return ResultBean.failedResultOfParamWithMsg("套餐分钟数不能为空");
            }

            if (vo.getPrice() == null) {
                return ResultBean.failedResultOfParamWithMsg("套餐价格不能为空");
            }

            Optional<PayFirstPlans> plansOptional = payFirstPlansRepository.findById(vo.getPayFirstPlansId());
            if (!plansOptional.isPresent()) {
                return ResultBean.failedResultOfParamWithMsg("非法套餐");
            }

            PayFirstPlans plans = plansOptional.get();
            if (plans.getDeleted() == 1) {
                return ResultBean.failedResultOfParamWithMsg("套餐已被删除");
            }

            if (plans.getStatus() == 2) {
                return ResultBean.failedResultOfParamWithMsg("套餐已被禁用");
            }

            if (plans.getMinute().intValue() != vo.getMinute().intValue()) {
                return ResultBean.failedResultOfParamWithMsg("套餐分钟数错误");
            }

            if (plans.getPrice().intValue() != vo.getPrice().intValue()) {
                return ResultBean.failedResultOfParamWithMsg("套餐金额错误");
            }

            WxUserRedisVo user = redisUtils.getWxUser(vo.getUserId() + "");
            if (user == null) {
                return ResultBean.failedResultOfParamWithMsg("非法用户");
            }

            String imei = redisUtils.getImei(vo.getDeviceId().toString());
            if (StringUtils.isBlank(imei)) {
                LOG.warn("OrderService createOrder imei = {} DB is not exist and deviceId = {}", vo.getDeviceId());
                return ResultBean.failedResultOfParamWithMsg("设备不存在");

            }

            DeviceRedisVo deviceRedisVo = redisUtils.getDevice(imei);
            if (deviceRedisVo == null) {
                LOG.warn("OrderService createOrder deviceRedisVo is NULL and imei = {}", imei);
                return ResultBean.failedResultOfParamWithMsg("设备不存在");

            }


            DeviceTypeRedisVo deviceTypeRedisVo = redisUtils.getDeviceType(deviceRedisVo.getTypeId());
            if (deviceTypeRedisVo == null) {
                LOG.warn("OrderService createOrder deviceTypeRedisVo is NULL and deviceTypeId = {}", deviceRedisVo.getTypeId());
                return ResultBean.failedResultOfParamWithMsg("设备类型不存在");
            }

            LaunchPointRedisVo launchPointRedisVo = redisUtils.getLaunchPoint(deviceRedisVo.getLaunchPointId().toString());
            if (launchPointRedisVo == null) {
                LOG.warn("OrderService createOrder launchPointRedisVo is NULL and launchPointId = {}", deviceRedisVo.getLaunchPointId());
                return ResultBean.failedResultOfParamWithMsg("投放点数据异常");

            }
            //商家校验
            TradeRedisVo tradeRedisVo = redisUtils.getTrade(launchPointRedisVo.getTradeId().toString());
            if (tradeRedisVo == null) {
                LOG.warn("OrderService createOrder tradeRedisVo is NULL and tradeId( = {}", launchPointRedisVo.getTradeId());
                return ResultBean.failedResultOfParamWithMsg("商家数据异常");
            }

            //设备状态检查
            ResultBean<Map<String, Object>> stautsMap = deviceService.deviceStatusCheck(vo.getUserId(), vo.getDeviceId());
            if (!stautsMap.getCode().equals("10000")) {
                return ResultBean.failedResultOfParamWithMsg(stautsMap.getMsg());
            }
            //用户钱包校验
            WxUserWalletRedisVo userWalletRedisVo = redisUtils.getWxUserWallet(vo.getUserId().toString());
            if (userWalletRedisVo == null) {
                LOG.warn("OrderService createOrder userWalletRedisVo is NULL and userId = {}", vo.getUserId());
                return ResultBean.failedResultOfParamWithMsg("用户钱包数据异常");
            }
            //用户购买的币余额
            //BigDecimal userCoin =  new BigDecimal(wxUserWalletService.getUserRechargeCoinBalance(userWalletRedisVo));
            //用户赠送的币余额
            BigDecimal userGiveCoin = new BigDecimal(wxUserWalletService.getUserGiveCoinBalance(userWalletRedisVo));
            //用户总的币余额（购买的 + 送的）
            BigDecimal userTotalCoin = new BigDecimal(wxUserWalletService.getUserCoinBalance(userWalletRedisVo));
            if (vo.getPayCoin() > 0 && vo.getPayCoin() > userTotalCoin.intValue()) {
                LOG.warn("OrderService createOrder userWalletRedisVo userTotalCoin = {} , payCoin = {} and userId = {}", userTotalCoin.intValue(), vo.getPayCoin(), vo.getUserId());
                return ResultBean.failedResultOfParamWithMsg("用户钱包币数据异常");
            }

            BigDecimal payFee = new BigDecimal(vo.getPayFee());
            int payCoin = 0;//支付的购买币数
            int payGiveCoin = 0;//支付的赠送的币
            int colinFee = 0;
            int couponFee = 0;//优惠券金额
            int couponTime = 0;//优惠券的时间
            Long couponId = null;//优惠券id
            String couponName = "";//优惠券名称
            Integer couponType = null;//优惠券类型
            //优惠券校验
            boolean couponFlag = false;
            List<UserCouponRedisVo> userCouponRedisVoList = null;
            UserCouponRedisVo userCouponRedis = null;
            if (vo.getUserCouponId() != null) {
                Map<String, List<UserCouponRedisVo>> userCouponRedisMap = redisUtils.getUserCouponMap(vo.getUserId().toString(), vo.getDeviceId(), vo.getPayFirstPlansId());
                userCouponRedisVoList = userCouponRedisMap == null ? null : userCouponRedisMap.get("noUse");
            }

            if (vo.getUserCouponId() != null && userCouponRedisVoList != null) {
                for (UserCouponRedisVo redisVo : userCouponRedisVoList) {
                    if (redisVo.getId().equals(vo.getUserCouponId())) {
                        couponFlag = true;
                        userCouponRedis = redisVo;
                        break;
                    }
                }

                if (!couponFlag) {
                    LOG.warn("OrderService createOrder couponFlag = {}", couponFlag);
                    return ResultBean.failedResultOfParamWithMsg("优惠券异常");

                }

                if (couponFlag) {
                    if (StringUtils.isNotBlank(userCouponRedis.getUseTime()) || userCouponRedis.getStatus().intValue() == UserCouponStatusEnum.USED.getValue().intValue()) {
                        LOG.warn("OrderService createOrder  userId = {} userCouponId = {} status = {} useTime = {}  is used", vo.getUserId(), vo.getUserCouponId(), userCouponRedis.getStatus(), userCouponRedis.getUseTime());
                        deleteCouponFlag = true;
                        return ResultBean.failedResultOfParamWithMsg("优惠已经使用，不能重复使用");


                    }

                    Date nowTime = new Date();
                    Date validStartTime = DateUtils.convert(userCouponRedis.getValidStartTime() + " 00:00:00", DateUtils.FORMAT_DATETIME_14);
                    Date validEndTime = DateUtils.convert(userCouponRedis.getValidEndTime() + " 23:59:59", DateUtils.FORMAT_DATETIME_14);

                    if (userCouponRedis.getStatus() == UserCouponStatusEnum.EXPIRE.getValue() || nowTime.getTime() > validEndTime.getTime()) {
                        LOG.warn("OrderService createOrder userId = {} userCouponId = {} status = {} validEndTime = {} > notTime = {}  is expire", vo.getUserId(), vo.getUserCouponId(), userCouponRedis.getStatus(), DateUtils.format(validEndTime, DateUtils.FORMAT_DATETIME_14), DateUtils.format(nowTime, DateUtils.FORMAT_DATETIME_14));
                        deleteCouponFlag = true;
                        return ResultBean.failedResultOfParamWithMsg("此优惠券已过期");

                    }


                    if (validStartTime.getTime() > nowTime.getTime()) {
                        LOG.warn("OrderService createOrder userId = {} userCouponId = {} validStartTime = {} < nowTime = {} ", vo.getUserId(), vo.getUserCouponId(), DateUtils.format(validStartTime, DateUtils.FORMAT_DATETIME_14), DateUtils.format(nowTime, DateUtils.FORMAT_DATETIME_14));
                        deleteCouponFlag = true;
                        return ResultBean.failedResultOfParamWithMsg("优惠券活动还未开始，此优惠券不能使用");

                    }

                    CouponRedisVo couponRedisVo = redisUtils.getCoupon(userCouponRedis.getCouponId().toString());
                    if (couponRedisVo == null) {
                        LOG.warn("OrderService createOrder couponRedisVo is NULL and couponId = {}", userCouponRedis.getCouponId());
                        deleteCouponFlag = true;
                        return ResultBean.failedResultOfParamWithMsg("此优惠券已经过期");
                    }

                }

            }

            if (userCouponRedis != null && vo.getUserCouponId() != null) {
                if (userCouponRedis.getType().intValue() == CouponTypeEnum.CASH_COUPON.getValue().intValue()) {//代金券 抵钱
                    //优惠条件
                    int termsFee = new BigDecimal(userCouponRedis.getPreferentialTerms()).intValue() * 100;//转分
                    if (vo.getPrice() < termsFee) {
                        deleteCouponFlag = true;
                        return ResultBean.failedResultOfParamWithMsg("此优惠券无效，需要达到 ￥" + ConvertUtils.fenConvertYuan(termsFee) + "元 才能使用此优惠券");
                    }

                } else if (userCouponRedis.getType() == CouponTypeEnum.DEDUCTION.getValue().intValue()) {//抵扣券 抵时长
                    //最低时长条件
                    int termsTime = new BigDecimal(userCouponRedis.getPreferentialTerms()).intValue();
                    if (vo.getMinute() < termsTime) {
                        deleteCouponFlag = true;
                        return ResultBean.failedResultOfParamWithMsg("此优惠券无效，需要使用" + termsTime + "分钟才能使用");
                    }
                }
            }

            //用优惠券
            if (vo.getUserCouponId() != null) {
                if (userCouponRedis.getType().intValue() == CouponTypeEnum.CASH_COUPON.getValue().intValue()) {//代金券 抵钱
                    couponId = userCouponRedis.getCouponId();
                    couponName = userCouponRedis.getCouponName();
                    couponType = userCouponRedis.getType();
                    //优惠条件
                    //int termsFee = new BigDecimal(userCouponRedis.getPreferentialTerms()).intValue() * 100;//转分
                    int payCoinFee = vo.getPayCoin() * 100;//对于用户而言 1币=1元
                    int preferenceAmount = (new BigDecimal(userCouponRedis.getPreferenceAmount()).intValue() * 100);
                    //优惠券减免足够
                    if (preferenceAmount - payFee.intValue() - payCoinFee >= 0) {
                        payFee = new BigDecimal(0);
                        payCoin = 0;
                        colinFee = 0;
                        payGiveCoin = 0;
                        //优惠券减免不足
                    } else {
                        payFee = new BigDecimal(preferenceAmount - vo.getPayFee());//先减现金
                        //现金减了再减币
                        if (payFee.intValue() > 0 && vo.getPayCoin() > 0) {
                            //减少币，币只能整的减少
                            int subtractCoinNum = new BigDecimal(payFee.intValue()).divide(new BigDecimal(100), BigDecimal.ROUND_DOWN).intValue();
                            //至少需要1个币
                            if (subtractCoinNum == 0) {
                                subtractCoinNum = 1;
                            }

                            payCoin = vo.getPayCoin() - subtractCoinNum;
                            //赠送的币足够支付 优先用户赠送的币支付
                            if (userGiveCoin.intValue() - payCoin >= 0) {
                                payGiveCoin = payCoin;
                                payCoin = 0;
                                //赠送的币不够支付
                            } else {
                                payGiveCoin = userGiveCoin.intValue();
                                payCoin = payCoin - payGiveCoin;
                            }
                            payFee = new BigDecimal(0);
                            //现金不够减，算支付购买的币与赠送的币数量
                        } else if (payFee.intValue() <= 0 && vo.getPayCoin() > 0) {
                            payCoin = vo.getPayCoin();
                            //赠送的币足够支付 优先用户赠送的币支付
                            if (userGiveCoin.intValue() - payCoin >= 0) {
                                payGiveCoin = payCoin;
                                payCoin = 0;
                                //赠送的币不够支付
                            } else {
                                payGiveCoin = userGiveCoin.intValue();
                                payCoin = payCoin - payGiveCoin;
                            }
                        }

                        payFee = new BigDecimal(Math.abs(payFee.intValue()));

                    }
                } else if (userCouponRedis.getType() == CouponTypeEnum.DEDUCTION.getValue().intValue()) {//抵扣券 抵时长
                    //最低时长条件
                    //int termsTime = new BigDecimal(userCouponRedis.getPreferentialTerms()).intValue();
                    int payCoinFee = vo.getPayCoin() * 100;//对于用户而言 1币=1元
                    //减免时间
                    int reductionTime = new BigDecimal(userCouponRedis.getPreferenceAmount()).intValue();
                    int mPrice = new BigDecimal(plans.getPrice()).divide(new BigDecimal(plans.getMinute()), 0, BigDecimal.ROUND_HALF_UP).intValue();//每分钟单价

                    int preferenceAmount = reductionTime * mPrice;//优惠减免金额
                    //优惠券减免足够
                    if (preferenceAmount - payFee.intValue() - payCoinFee >= 0) {
                        payFee = new BigDecimal(0);
                        payCoin = 0;
                        colinFee = 0;
                        payGiveCoin = 0;
                        //优惠券减免不足
                    } else {
                        payFee = new BigDecimal(preferenceAmount - vo.getPayFee());//先减现金
                        //现金减了再减币
                        if (payFee.intValue() > 0 && vo.getPayCoin() > 0) {
                            //减少币，币只能整的减少
                            int subtractCoinNum = new BigDecimal(payFee.intValue()).divide(new BigDecimal(100), BigDecimal.ROUND_DOWN).intValue();
                            //至少需要1个币
                            if (subtractCoinNum == 0) {
                                subtractCoinNum = 1;
                            }

                            payCoin = vo.getPayCoin() - subtractCoinNum;
                            //赠送的币足够支付 优先用户赠送的币支付
                            if (userGiveCoin.intValue() - payCoin >= 0) {
                                payGiveCoin = payCoin;
                                payCoin = 0;
                                //赠送的币不够支付
                            } else {
                                payGiveCoin = userGiveCoin.intValue();
                                payCoin = payCoin - payGiveCoin;
                            }
                            payFee = new BigDecimal(0);
                            //现金不够减，算支付购买的币与赠送的币数量
                        } else if (payFee.intValue() <= 0 && vo.getPayCoin() > 0) {
                            payCoin = vo.getPayCoin();
                            //赠送的币足够支付 优先用户赠送的币支付
                            if (userGiveCoin.intValue() - payCoin >= 0) {
                                payGiveCoin = payCoin;
                                payCoin = 0;
                                //赠送的币不够支付
                            } else {
                                payGiveCoin = userGiveCoin.intValue();
                                payCoin = payCoin - payGiveCoin;
                            }
                        }

                        payFee = new BigDecimal(Math.abs(payFee.intValue()));
                    }

                    couponTime = reductionTime;
                }
            } else {//不用优惠券
                if (vo.getPayCoin() > 0) {
                    payCoin = vo.getPayCoin();
                    //赠送的币足够支付 优先用户赠送的币支付
                    if (userGiveCoin.intValue() - payCoin >= 0) {
                        payGiveCoin = payCoin;
                        payCoin = 0;
                        //赠送的币不够支付
                    } else {
                        payGiveCoin = userGiveCoin.intValue();
                        payCoin = payCoin - payGiveCoin;
                    }
                }
            }

            //币价值的钱,购买的币才算钱，赠送的币不算钱
            if (payCoin > 0) {
                colinFee = new BigDecimal(payCoin).divide(new BigDecimal(wxUserWalletService.getUserCoinBalance(userWalletRedisVo)), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(wxUserWalletService.getUserCoinBalance(userWalletRedisVo))).setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
            }

            //前段与后端接口算币与现金对比，如果不一样则说明算法有问题
            if (vo.getPayCoin() != payCoin + payGiveCoin) {
                LOG.warn("OrderService createOrder userId = {} param.payCoin = {} payCoin = {} payGiveCoin = {}", vo.getPayCoin(), payCoin, payGiveCoin);
                return ResultBean.failedResultOfParamWithMsg("币支付错误");
            }

            if (vo.getPayFee() != payFee.intValue()) {
                LOG.warn("OrderService createOrder userId = {} param.payFee = {} payFee = {} ", vo.getPayFee(), payFee.intValue());
                return ResultBean.failedResultOfParamWithMsg("支付的金额错误");
            }

            String orgName = "";//设备部门名称
            StringBuilder sql = new StringBuilder("select id, name from t_sys_department where id = ? ");
            Map<String, Object> orgMap = jdbcTemplate.queryForMap(sql.toString(), deviceRedisVo.getOrgId());
            if (!orgMap.isEmpty()) {
                orgName = orgMap.get("name").toString();
            }


            //借车的时候产生订单编号
            String orderNo = RandomNoUtils.getRandomNo(2, 2);
            Order entity = new Order();
            entity.setUserId(vo.getUserId());
            entity.setAppid(redisUtils.getValue(RedisKeyConstant.REDIS_SYS_PARAMS_OF_WX_SP_APPID));
            entity.setDeviceDes(deviceRedisVo.getRemarks());
            entity.setDeviceId(vo.getDeviceId());
            entity.setDeviceTypeId(deviceRedisVo.getTypeId());
            entity.setDeviceTypeName(deviceTypeRedisVo.getName());
            entity.setFappid(user.getFappid());
            entity.setFopenid(user.getFopenid());
            entity.setImei(imei);
            entity.setLaunchPointId(launchPointRedisVo.getId());
            entity.setLaunchPointName(launchPointRedisVo.getName());
            entity.setLaunchPointAddress(launchPointRedisVo.getAddr());
            entity.setNickName(user.getNickName() == null ? null : user.getNickName().getBytes());
            entity.setOpenid(user.getOpenid());
            entity.setUnionid(user.getUnionid());
            entity.setUseStatus(OrderUseStatusEnum.NOT_USE.getStatus());
            entity.setTradeName(tradeRedisVo.getName());
            entity.setTradeId(tradeRedisVo.getId());
            //初始化状态
            entity.setPayStatus(0);
            entity.setRefundStatus(0);
            entity.setAuditStatus(0);
            entity.setRefundAuditStatus(0);
            entity.setOrgId(deviceRedisVo.getOrgId());
            entity.setOrgName(orgName);
            entity.setOrderNo(orderNo);
            entity.setUserCashBalance(wxUserWalletService.getUserWalletFeeBalance(userWalletRedisVo));
            entity.setUserCoinBalance(wxUserWalletService.getUserRechargeCoinBalance(userWalletRedisVo));
            entity.setPayCoin(payCoin);
            entity.setPayFee(vo.getPayFee());
            entity.setManageFee(0);
            entity.setCreateTime(new Date());
//			entity.setUseStartTime(useStartTime);//支付通知的时候在更新
//			entity.setUseEndTime(useEndTime);////支付通知的时候在更新
            entity.setLaunchPointLowestTime(launchPointRedisVo.getLowestTime());
            entity.setLaunchPointHighestTime(launchPointRedisVo.getHighestTime());
            entity.setLaunchPointPrice(launchPointRedisVo.getPrice());
            entity.setCouponFee(couponFee);
            entity.setCouponTime(couponTime);
            entity.setColinFee(colinFee);
            entity.setPayGiveCoin(payGiveCoin);
            entity.setSettlementStatus(0);
            entity.setPaymentModel(PaymentModelEnum.PAY_FIRST.getValue());
            entity.setCouponId(couponId);
            entity.setCouponName(couponName);
            entity.setCouponType(couponType);
            entity.setPayFirstPlansId(vo.getPayFirstPlansId());
            //先设置时间
            Date nowTime = new Date();
            entity.setUseStartTime(nowTime);
            entity.setUseEndTime(DateUtils.stringToDateTimeForTags(DateUtils.rollMinute(plans.getMinute()), DateUtils.FORMAT_DATETIME_14));

            orderRepository.save(entity);
            Map<String, Object> resultMap = new HashMap<String, Object>();
            resultMap.put("paymentModel", PaymentModelEnum.PAY_FIRST.getValue());
            resultMap.put("orderId", entity.getId());
            return ResultBean.successfulResult(resultMap);

        } catch (Exception e) {
            LOG.error("OrderService createOrder  Exception = {}", e);
        } finally {
            if (deleteCouponFlag) {
                redisUtils.deleteUserCoupon(vo.getUserId().toString());
            }

        }
        return ResultBean.successfulResult(null);
    }

    @SuppressWarnings("unchecked")
    public ResultBean<Map<Object, Object>> unifiedorder(OrderPayInfoReqVo vo) {
        LOG.info("OrderService unifiedorder params = {} ", vo.toString());
        ResultBean<Map<Object, Object>> resultBean = new ResultBean<>();
        SortedMap<Object, Object> returnMap = new TreeMap<>();
        boolean deleteCouponFlag = false;
        try {
            resultBean.setCode(StatusCode.SUCCESS_CODE_10000.getErrorCode());
            resultBean.setMsg(StatusCode.SUCCESS_CODE_10000.getErrorMsg());
            if (vo.getOrderId() == null) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("订单id不能为空");
                LOG.warn("OrderService unifiedorder orderId is NULL");
                return resultBean;
            }

            if (vo.getUserId() == null) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("用户id不能为空");
                LOG.warn("OrderService unifiedorder userId is NULL");
                return resultBean;
            }

            if (vo.getPayManageFee() == null) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("管理费不能为空");
                LOG.warn("OrderService unifiedorder manageFee is NULL");
                return resultBean;
            }

            if (vo.getPayFee() == null) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("支付金额不能为空");
                LOG.warn("OrderService unifiedorder payFee is NULL");
                return resultBean;
            }

            if (vo.getPayFee() < 0) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("支付金额不能小于0");
                LOG.warn("OrderService unifiedorder payFee < 0");
                return resultBean;
            }

            if (vo.getPayManageFee() < 0) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("管理费不能小于0");
                LOG.warn("OrderService unifiedorder manageFee < 0 ");
                return resultBean;
            }

            if (vo.getPayCoin() == null) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("支付的币不能为空");
                LOG.warn("OrderService unifiedorder payCoin is NULL");
                return resultBean;
            }

            if (vo.getPayCoin() < 0) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("支付的币不能小于0");
                LOG.warn("OrderService unifiedorder payCoin < 0 ");
                return resultBean;
            }

            Optional<Order> orderOptional = orderRepository.findById(vo.getOrderId());
            if (!orderOptional.isPresent()) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("订单数据不存在");
                LOG.warn("OrderService unifiedorder orderId = {} DB is not exsit ", vo.getOrderId());
                return resultBean;
            }

            Order order = orderOptional.get();
            if (order.getPayStatus() == 1) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("订单已经支付成功，不需要重复支付");
                return resultBean;
            }
            //系统服务商子商户号
            String subMchId = redisUtils.getValue(RedisKeyConstant.REDIS_SYS_PARAMS_OF_WX_SYSTEM_SERVICE_SUB_MCHID);
            //系统服务商户号
            String mchId = redisUtils.getValue(RedisKeyConstant.REDIS_SYS_PARAMS_OF_WX_SYSTEM_SERVICE_MCHID);
            if (StringUtils.isBlank(mchId)) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("商户号不存在");
                return resultBean;
            }
            String appid = redisUtils.getValue(RedisKeyConstant.REDIS_SYS_PARAMS_OF_WX_SYSTEM_SERVICE_APPID);
            if (StringUtils.isBlank(appid)) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("系统服务商appid不存在");
                return resultBean;
            }

            String spAppid = redisUtils.getValue(RedisKeyConstant.REDIS_SYS_PARAMS_OF_WX_SP_APPID);

            //系统服务商户的密钥
            String mchIdkey = redisUtils.getValue(RedisKeyConstant.REDIS_SYS_PARAMS_OF_WX_SYSTEM_SERVICE_MCHID_KEY);
//            WechatAccount account = wechatAccountRepository.findByMchId(mchId);
//            if(account == null) {
//            	resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
//				resultBean.setMsg("商户号不存在");
//				return resultBean;
//            }

            int paymentModel = order.getPaymentModel() == null ? PaymentModelEnum.PAY_AFTER.getValue() : order.getPaymentModel();//付费模式：付费模式:1、先付费，2、后付费
            String outTradeNo = "CD" + RandomNoUtils.getRandomNo(2, 2); //支付流水单号

            int couponFee = 0;//优惠券金额
            int couponTime = 0;//优惠券的时间
            Long couponId = null;//优惠券id
            String couponName = "";//优惠券名称
            Integer couponType = null;//优惠券类型
            //投放点单价为0
            int launchPointPrice = order.getLaunchPointPrice() == null ? 0 : order.getLaunchPointPrice();
            int manageFee = order.getManageFee() == null ? 0 : order.getManageFee();
            //极端情况 投放点设置的单价为0 并且没有收管理费
            if (launchPointPrice + manageFee == 0 && paymentModel == PaymentModelEnum.PAY_AFTER.getValue()) {
                Date nowTime = new Date();
                order.setPayStatus(OrderPayStatusEnum.PAY_SUCCESS.getStatus());
                order.setLastUpdateTime(nowTime);
                order.setPayTime(nowTime);
                //重置优惠券信息
                order.setCouponId(null);
                order.setUserCouponId(null);
                order.setCouponFee(0);
                order.setCouponTime(0);
                order.setCouponName(couponName);
                order.setCouponType(couponType);
                order.setPayTime(new Date());
                //修改订单
                orderRepository.save(order);
                //保存流水
                this.saveOrderPayInfo(order, subMchId, outTradeNo, appid, null, OrderPayStatusEnum.PAY_SUCCESS.getStatus(), null);

                //删除用户设备
                redisUtils.deleteUserDevice(order.getUserId().toString());
                //删除用户未支付订单
                redisUtils.deleteUserNoPayOrder(order.getUserId().toString());
                //删除用户钱包
                redisUtils.deleteWxUserWallet(order.getUserId().toString());

                returnMap.put("payType", 1);//0 默认是币支付 ;
                resultBean.setResultData(returnMap);
                return resultBean;
            }

            if (order.getPayStatus() == OrderPayStatusEnum.PAY_SUCCESS.getStatus()) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("订单已支付成功，无需再次支付");
                LOG.warn("OrderService unifiedorder orderId = {} is paySucceed ", vo.getOrderId());
                return resultBean;
            }


            if (order.getPayFee() + order.getManageFee() == 0 && order.getPayCoin() + order.getPayGiveCoin() == 0 && vo.getUserCouponId() != null) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("订单无需用优惠券支付");
                LOG.warn("OrderService unifiedorder orderId = {} payFee + manageFee = 0 ", vo.getOrderId());
                deleteCouponFlag = true;
                return resultBean;
                //币支付且没有用优惠券
            } else if (order.getPayFee() + order.getManageFee() == 0 && vo.getUserCouponId() == null && order.getPayCoin() + order.getPayGiveCoin() > 0) {
                Date nowTime = new Date();
                order.setPayStatus(OrderPayStatusEnum.PAY_SUCCESS.getStatus());
                order.setLastUpdateTime(nowTime);
                order.setPayTime(nowTime);
                //重置优惠券信息
                order.setCouponId(null);
                order.setUserCouponId(null);
                order.setCouponFee(0);
                order.setCouponTime(0);
                order.setCouponName(couponName);
                order.setCouponType(couponType);
                order.setPayTime(new Date());
                //修改订单
                orderRepository.save(order);
                //保存流水
                this.saveOrderPayInfo(order, subMchId, outTradeNo, appid, null, OrderPayStatusEnum.PAY_SUCCESS.getStatus(), null);

                //修改钱包数据
                this.updateUserWallet(order.getUserId(), order.getPayCoin(), order.getPayGiveCoin(), order.getColinFee());
                //修改用户表
                this.updateUser(order.getUserId());

                //删除用户设备
                redisUtils.deleteUserDevice(order.getUserId().toString());
                //删除用户未支付订单
                redisUtils.deleteUserNoPayOrder(order.getUserId().toString());
                //先付费的发送开机指令
                if (paymentModel == PaymentModelEnum.PAY_FIRST.getValue()) {
                    order.setUseStartTime(nowTime);
                    order.setUseStatus(OrderUseStatusEnum.USEING.getStatus());
                    Optional<PayFirstPlans> plans = payFirstPlansRepository.findById(order.getPayFirstPlansId());
                    order.setUseEndTime(DateUtils.convert(DateUtils.rollMinute(plans.get().getMinute()), DateUtils.FORMAT_DATETIME_14));
                    rabbitMqService.putPayFirstMqQueue(order.getUserId(), order.getImei(), order.getPayFirstPlansId());
                }
                returnMap.put("payType", 1);//币支付;
                resultBean.setResultData(returnMap);
                return resultBean;
            }

            List<UserCouponRedisVo> userCouponRedisVoList = null;
            UserCouponRedisVo userCouponRedis = null;
            //优惠券校验
            boolean couponFlag = false;
            if (vo.getUserCouponId() != null) {
                Map<String, List<UserCouponRedisVo>> userCouponRedisMap = redisUtils.getUserCouponMap(vo.getUserId().toString(), order.getDeviceId(), order.getPayFirstPlansId());
                userCouponRedisVoList = userCouponRedisMap == null ? null : userCouponRedisMap.get("noUse");
            }

            if (vo.getUserCouponId() != null && userCouponRedisVoList != null && order.getUserCouponId() == null) {
                for (UserCouponRedisVo redisVo : userCouponRedisVoList) {
                    if (redisVo.getId().equals(vo.getUserCouponId())) {
                        couponFlag = true;
                        userCouponRedis = redisVo;
                        break;
                    }
                }

                if (!couponFlag) {
                    LOG.warn("OrderService unifiedorder couponFlag = {}", couponFlag);
                    resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                    resultBean.setMsg("优惠券异常");
                    return resultBean;
                }

                if (couponFlag) {
                    if (StringUtils.isNotBlank(userCouponRedis.getUseTime()) || userCouponRedis.getStatus().intValue() == UserCouponStatusEnum.USED.getValue().intValue()) {
                        LOG.warn("OrderService unifiedorder userId = {} userCouponId = {} status = {} useTime = {}  is used", vo.getUserId(), vo.getUserCouponId(), userCouponRedis.getStatus(), userCouponRedis.getUseTime());
                        resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                        resultBean.setMsg("优惠已经使用，不能重复使用");
                        deleteCouponFlag = true;
                        return resultBean;
                    }

                    Date nowTime = new Date();
                    Date validStartTime = DateUtils.convert(userCouponRedis.getValidStartTime() + " 00:00:00", DateUtils.FORMAT_DATETIME_14);
                    Date validEndTime = DateUtils.convert(userCouponRedis.getValidEndTime() + " 23:59:59", DateUtils.FORMAT_DATETIME_14);

                    if (userCouponRedis.getStatus() == UserCouponStatusEnum.EXPIRE.getValue() || nowTime.getTime() > validEndTime.getTime()) {
                        LOG.warn("OrderService unifiedorder userId = {} userCouponId = {} status = {} validEndTime = {} > notTime = {}  is expire", vo.getUserId(), vo.getUserCouponId(), userCouponRedis.getStatus(), DateUtils.format(validEndTime, DateUtils.FORMAT_DATETIME_14), DateUtils.format(nowTime, DateUtils.FORMAT_DATETIME_14));
                        deleteCouponFlag = true;
                        resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                        resultBean.setMsg("此优惠券已过期");
                        return resultBean;
                    }


                    if (validStartTime.getTime() > nowTime.getTime()) {
                        LOG.warn("OrderService unifiedorder userId = {} userCouponId = {} validStartTime = {} < nowTime = {} ", vo.getUserId(), vo.getUserCouponId(), DateUtils.format(validStartTime, DateUtils.FORMAT_DATETIME_14), DateUtils.format(nowTime, DateUtils.FORMAT_DATETIME_14));
                        resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                        deleteCouponFlag = true;
                        resultBean.setMsg("优惠券活动还未开始，此优惠券不能使用");
                        return resultBean;
                    }

                    CouponRedisVo couponRedisVo = redisUtils.getCoupon(userCouponRedis.getCouponId().toString());
                    if (couponRedisVo == null) {
                        LOG.warn("OrderService unifiedorder couponRedisVo is NULL and couponId = {}", userCouponRedis.getCouponId());
                        deleteCouponFlag = true;
                        resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                        resultBean.setMsg("此优惠券已经过期");
                        return resultBean;
                    }

                }

            }


            WxUserWalletRedisVo userWalletRedisVo = redisUtils.getWxUserWallet(vo.getUserId().toString());
            if (userWalletRedisVo == null) {
                LOG.warn("OrderService unifiedorder userWalletRedisVo is NULL = {}", vo.getUserId());
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("用户钱包数据异常");
                return resultBean;
            }


            if (wxUserWalletService.getUserCoinBalance(userWalletRedisVo) < vo.getPayCoin()) {
                LOG.warn("OrderService unifiedorder userWalletRedisVo coinBalance = {} , vo.coin = {} and userId = {}", wxUserWalletService.getUserCoinBalance(userWalletRedisVo), vo.getPayCoin(), vo.getUserId());
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("币数据异常");
                return resultBean;
            }

            Date nowTime = new Date();
            Integer totalFee = order.getPayFee();
            Date useStartTime = order.getUseStartTime();
            Date useEndTime = order.getUseEndTime();

            int useMinutes = DateUtils.minuteBetween(useStartTime, useEndTime);
            int lowestTime = order.getLaunchPointLowestTime() == null ? 0 : order.getLaunchPointLowestTime();//投放点最少使用时间
            int highestTime = order.getLaunchPointHighestTime() == null ? 0 : order.getLaunchPointHighestTime();//投放点最高时间时间
            int price = order.getLaunchPointPrice() == null ? 0 : order.getLaunchPointPrice();//收费单价
            int payCoin = order.getPayCoin();//支付的购买币数
            int payGiveCoin = order.getPayGiveCoin();//支付的赠送的币
            int colinFee = order.getColinFee();


            //车棚的车根据实际时间来算
            if (order.getPosition() == null && order.getPaymentModel() == PaymentModelEnum.PAY_AFTER.getValue()) {
                if (useMinutes < lowestTime) {
                    useMinutes = lowestTime;
                } else if (useMinutes > highestTime) {
                    useMinutes = highestTime;
                }
            }

            //订单如果已经选择了优惠券了不需要再校验 优先使用送的币再次是购买的币
            if (userCouponRedis != null && order.getUserCouponId() == null) {
                couponId = userCouponRedis.getCouponId();
                couponName = userCouponRedis.getCouponName();
                couponType = userCouponRedis.getType();
                if (userCouponRedis.getType().intValue() == CouponTypeEnum.CASH_COUPON.getValue().intValue()) {//代金券 抵钱
                    //优惠条件
                    int termsFee = new BigDecimal(userCouponRedis.getPreferentialTerms()).intValue() * 100;//转分
                    int payCoinFee = (order.getPayCoin() + order.getPayGiveCoin()) * 100;//对于用户而言 1币=1元
                    if (order.getPayFee() + payCoinFee < termsFee) {
                        deleteCouponFlag = true;
                        resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                        resultBean.setMsg("此优惠券无效，需要达到 ￥" + ConvertUtils.fenConvertYuan(termsFee) + "元 才能使用此优惠券");
                        return resultBean;
                    }


                    int preferenceAmount = (new BigDecimal(userCouponRedis.getPreferenceAmount()).intValue() * 100);
                    //优惠券减免足够
                    if (preferenceAmount - order.getPayFee() - payCoinFee >= 0) {
                        totalFee = 0;
                        payCoin = 0;
                        colinFee = 0;
                        payGiveCoin = 0;
                        //优惠券减免不足
                    } else {
                        totalFee = preferenceAmount - order.getPayFee();//先减现金
                        if (totalFee > 0 && payCoin + payGiveCoin > 0) {
                            //减少币，币只能整的减少
                            int subtractCoinNum = new BigDecimal(totalFee).divide(new BigDecimal(100), BigDecimal.ROUND_DOWN).intValue();
                            if (subtractCoinNum - payCoin > 0) {
                                payGiveCoin = payGiveCoin - (subtractCoinNum - payCoin);
                                payCoin = 0;
                            } else {
                                payCoin = payCoin - subtractCoinNum;
                            }

                            totalFee = totalFee - (subtractCoinNum * 100);
                            //币价值的钱,购买的币才算钱，赠送的币不算钱
                            if (payCoin > 0) {
                                colinFee = new BigDecimal(payCoin).divide(new BigDecimal(order.getUserCoinBalance()), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(order.getUserCashBalance())).setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
                            }
                        }

                        totalFee = Math.abs(totalFee);
                    }

                    couponFee = preferenceAmount;
                } else if (userCouponRedis.getType() == CouponTypeEnum.DEDUCTION.getValue().intValue()) {//抵扣券 抵时长
                    //最低时长条件
                    int termsTime = new BigDecimal(userCouponRedis.getPreferentialTerms()).intValue();
                    if (useMinutes < termsTime) {
                        deleteCouponFlag = true;
                        resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                        resultBean.setMsg("此优惠券无效，需要使用" + termsTime + "分钟才能使用");
                        return resultBean;
                    }

                    int payCoinFee = (order.getPayCoin() + order.getPayGiveCoin()) * 100;//对于用户而言 1币=1元
                    //减免时间
                    int reductionTime = new BigDecimal(userCouponRedis.getPreferenceAmount()).intValue();

                    int preferenceAmount = reductionTime * price;//优惠减免金额

                    //优惠券减免足够
                    if (preferenceAmount - order.getPayFee() - payCoinFee >= 0) {
                        totalFee = 0;
                        payCoin = 0;
                        colinFee = 0;
                        payGiveCoin = 0;
                        //优惠券减免不足
                    } else {
                        totalFee = preferenceAmount - order.getPayFee();//先减现金
                        if (totalFee > 0 && payCoin + payGiveCoin > 0) {
                            //减少币，币只能整的减少
                            int subtractCoinNum = new BigDecimal(totalFee).divide(new BigDecimal(100), BigDecimal.ROUND_DOWN).intValue();

                            if (subtractCoinNum - payCoin > 0) {
                                payGiveCoin = payGiveCoin - (subtractCoinNum - payCoin);
                                payCoin = 0;
                            } else {
                                payCoin = payCoin - subtractCoinNum;
                            }

                            totalFee = totalFee - (subtractCoinNum * 100);
                            //币价值的钱,购买的币才算钱，赠送的币不算钱
                            if (payCoin > 0) {
                                colinFee = new BigDecimal(payCoin).divide(new BigDecimal(order.getUserCoinBalance()), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(order.getUserCashBalance())).setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
                            }
                        }

                        totalFee = Math.abs(totalFee);
                    }

                    couponTime = reductionTime;
                }
            }

            if (vo.getPayFee().intValue() != totalFee.intValue()) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("支付金额错误");
                deleteCouponFlag = true;
                LOG.warn("OrderService unifiedorder payFee = {} != totalFee = {} and orderId = {}", vo.getPayFee(), totalFee, order.getId());
                return resultBean;
            }

            LOG.info("OrderService unifiedorder vo.payCoin = {} ,payCoin = {}, payGiveCoin = {}", vo.getPayCoin(), payCoin, payGiveCoin);
            if (vo.getPayCoin().intValue() != payCoin + payGiveCoin) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("支付的币数错误");
                deleteCouponFlag = true;
                LOG.warn("OrderService unifiedorder vo.payCoin = {} != payCoin = {} and orderId = {}", vo.getPayCoin(), payCoin + payGiveCoin, order.getId());
                return resultBean;
            }

            //订单之前没有选择过优惠券
            if (order.getUserCouponId() == null && vo.getUserCouponId() != null) {
                order.setPayFee(totalFee);
                order.setPayCoin(payCoin);
                order.setPayGiveCoin(payGiveCoin);
                order.setColinFee(colinFee);
                order.setCouponFee(couponFee);
                order.setCouponTime(couponTime);
                order.setLastUpdateTime(new Date());
                order.setUserCouponId(vo.getUserCouponId());
                order.setCouponId(couponId);
                order.setCouponName(couponName);
                order.setCouponType(couponType);
                order.setPayTime(new Date());
                if (userCouponRedis != null) {
                    order.setCouponType(userCouponRedis.getType());
                }
            } else {
                //订单之前有选择过优惠券
                order.setLastUpdateTime(new Date());
            }

            if (order.getPayFee() + order.getManageFee() == 0) {
                order.setPayStatus(OrderPayStatusEnum.PAY_SUCCESS.getStatus());
                order.setLastUpdateTime(new Date());
                //修改订单
                orderRepository.save(order);
                //保存流水
                this.saveOrderPayInfo(order, subMchId, outTradeNo, appid, null, OrderPayStatusEnum.PAY_SUCCESS.getStatus(), null);
                //修改钱包数据
                this.updateUserWallet(order.getUserId(), payCoin, payGiveCoin, colinFee);
                //修改用户表
                this.updateUser(order.getUserId());
                if (order.getUserCouponId() != null) {
                    //修改优惠券信息
                    this.updateUserCoupon(order.getUserCouponId(), order.getUserId(), order.getDeviceId(), order.getId(), order.getLaunchPointId());
                }

                //先付费的发送开机指令
                if (paymentModel == PaymentModelEnum.PAY_FIRST.getValue()) {
                    //Date nowTime = new Date();
                    order.setUseStartTime(nowTime);
                    Optional<PayFirstPlans> plans = payFirstPlansRepository.findById(order.getPayFirstPlansId());
                    order.setUseEndTime(DateUtils.convert(DateUtils.rollMinute(plans.get().getMinute()), DateUtils.FORMAT_DATETIME_14));
                    rabbitMqService.putPayFirstMqQueue(order.getUserId(), order.getImei(), order.getPayFirstPlansId());
                } else {//后付费的删除信息
                    //删除用户设备
                    redisUtils.deleteUserDevice(order.getUserId().toString());
                    //删除用户未支付订单
                    redisUtils.deleteUserNoPayOrder(order.getUserId().toString());
                }
                returnMap.put("payType", 1);//看成是币支付
                resultBean.setResultData(returnMap);
                return resultBean;
            }


            String nonceStr = RandomNoUtils.getRandomStringByLength(32);

            SortedMap<Object, Object> paymentPo = new TreeMap<Object, Object>();
            paymentPo.put("appid", appid);
            paymentPo.put("mch_id", mchId);
            paymentPo.put("sub_mch_id", subMchId);
            paymentPo.put("openid", order.getOpenid());//小程序openid必传值
            paymentPo.put("nonce_str", nonceStr);
            paymentPo.put("body", "儿童车车辆订单");
            paymentPo.put("out_trade_no", outTradeNo);
            paymentPo.put("total_fee", totalFee + order.getManageFee());
            paymentPo.put("spbill_create_ip", "127.0.0.1");
            paymentPo.put("notify_url", wxPayNotifyUrl);
            paymentPo.put("trade_type", "JSAPI");
            paymentPo.put("sign_type", "MD5");

            String sign = WeiXinUtil.getSign("UTF-8", paymentPo, mchIdkey); //生成签名
            paymentPo.put("sign", sign);

            String result = HttpClientUtil.httpPost(unifiedorderUrl, WeiXinUtil.mapToXml(paymentPo), HttpClientUtil.ContentTypeXML);
            Map<String, String> dataMap = WeiXinUtil.doXMLParse(result);
            String prepayId = "";//微信预支付单号

            if ("SUCCESS".equals(dataMap.get("result_code")) && "SUCCESS".equals(dataMap.get("return_code"))) {
                //再次签名
                prepayId = dataMap.get("prepay_id") + "";
                returnMap.put("appId", spAppid);//设置(小程序ID)(这块一定要是大写)
                returnMap.put("timeStamp", Long.valueOf(System.currentTimeMillis()).toString());//设置(时间戳)
                returnMap.put("nonceStr", nonceStr); //设置(随机串)
                returnMap.put("package", "prepay_id=" + prepayId); //设置(数据包)
                returnMap.put("signType", "MD5");  //设置(签名方式)

                String twiceSign = WeiXinUtil.getSign("UTF-8", returnMap, mchIdkey); //生成签名
                returnMap.put("paySign", twiceSign);
            } else {
                LOG.error("WxRechargeRecordService unifiedorder fails result = {}", result);
                resultBean.setCode(StatusCode.ERROR_CODE_30001.getErrorCode());
                resultBean.setMsg("微信支付异常，请重新支付");
                return resultBean;
            }

            //保存订单
            orderRepository.save(order);
            if (paymentModel == PaymentModelEnum.PAY_AFTER.getValue()) {
                //删除缓存未支付订单
                redisUtils.deleteUserNoPayOrder(vo.getUserId().toString());
            }


            //保存支付流水
            this.saveOrderPayInfo(order, subMchId, outTradeNo, spAppid, prepayId, OrderPayStatusEnum.NOT_PAY.getStatus(), nonceStr);

            returnMap.put("payType", 2);//微信支付;
            resultBean.setResultData(returnMap);
            return resultBean;


        } catch (Exception e) {
            LOG.error("OrderService unifiedorder Exception = {}", e);
            resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
            resultBean.setMsg(StatusCode.ERROR_CODE_10001.getErrorMsg());
            return resultBean;
        } finally {
            if (deleteCouponFlag) {
                redisUtils.deleteUserCoupon(vo.getUserId().toString());
            }

        }

    }


    */
/**
     * 保存支付流水
     *
     * @param order
     * @param mchId
     * @param outTradeNo
     * @param appid
     * @param prepayId
     * @param payStatus
     * @param nonceStr
     *//*

    public void saveOrderPayInfo(Order order, String mchId, String outTradeNo, String appid, String prepayId, Integer payStatus, String nonceStr) {
        OrderPayInfo orderPayInfo = new OrderPayInfo();
        orderPayInfo.setAppid(appid);
        orderPayInfo.setCreateTime(new Date());
        orderPayInfo.setMchId(mchId);
        orderPayInfo.setNonceStr(nonceStr);
        orderPayInfo.setOpenid(order.getOpenid());
        orderPayInfo.setOutTradeNo(outTradeNo);
        orderPayInfo.setOrderId(order.getId());
        orderPayInfo.setTotalFee(order.getPayFee() + order.getManageFee());
        orderPayInfo.setImei(order.getImei());
        orderPayInfo.setDeviceId(order.getDeviceId());
        orderPayInfo.setPayCoin(order.getPayCoin());
        orderPayInfo.setPayGiveCoin(order.getPayGiveCoin());
        orderPayInfo.setPrepayId(prepayId);
        orderPayInfo.setPayStatus(payStatus);
        orderPayInfoRepository.save(orderPayInfo);
    }


    public void updateUserWallet(Long userId, Integer payCoin, Integer payGiveCoin, Integer colinFee) {
        //修改钱包数据
        WxUserWallet wxUserWallet = wxUserWalletRepository.findByUserId(userId);
        if (wxUserWallet != null) {
            wxUserWallet.setTotalConsumeCoinNum(wxUserWallet.getTotalConsumeCoinNum() + payCoin);
            wxUserWallet.setTotalConsumeFee(wxUserWallet.getTotalConsumeFee() + colinFee);
            wxUserWallet.setTotalConsumeGiveCoinNum(wxUserWallet.getTotalConsumeGiveCoinNum() + payGiveCoin);
            wxUserWallet.setLastUpdateTime(new Date());
            wxUserWalletRepository.save(wxUserWallet);
            redisUtils.deleteWxUserWallet(userId + "");
            redisUtils.getWxUserWallet(userId + "");
        } else {
            LOG.warn("OrderService updateUserWallet userId = {} is userWallet", userId);
        }
    }

    public void updateUser(Long userId) {
        Optional<WxUser> wxUserOptional = wxUserRepository.findById(userId);
        if (wxUserOptional.isPresent()) {
            WxUser wxUser = wxUserOptional.get();
            wxUser.setUseNum(wxUser.getUseNum() == null ? 1 : wxUser.getUseNum() + 1);
            wxUser.setLastUpdateTime(new Date());
            wxUserRepository.save(wxUser);
            redisUtils.putWxUser(wxUser);
        } else {
            LOG.warn("OrderService updateUser userId = {} is not extis");
        }
    }

    public void updateUserCoupon(Long userCouponId, Long userId, Long deviceId, Long orderId, Long launchPointId) {
        if (userCouponId != null) {
            //删除缓存用户优惠券
            redisUtils.deleteUserCoupon(userId + "");
            Optional<UserCoupon> userCouponOptional = userCouponRepository.findById(userCouponId);
            if (!userCouponOptional.isPresent()) {
                LOG.warn("OrderService updateUserCoupon  userCoupon DB is not exsit and userCouponId = {} ", userCouponId);
            } else {
                Date nowTime = new Date();
                UserCoupon userCoupon = userCouponOptional.get();
                userCoupon.setStatus(UserCouponStatusEnum.USED.getValue());
                userCoupon.setUseTime(nowTime);
                userCoupon.setLaunchPointId(launchPointId);
                userCouponRepository.save(userCoupon);

                Optional<Coupon> couponOptional = couponRepository.findById(userCoupon.getCouponId());
                if (!couponOptional.isPresent()) {
                    LOG.warn("OrderService updateUserCoupon  coupon DB is not exsit and couponId = {} ", userCoupon.getCouponId());
                } else {
                    //6、修改优惠券使用数量
                    Coupon coupon = couponOptional.get();
                    coupon.setUseNum(coupon.getNum() == null ? 1 : coupon.getNum() + 1);
                    coupon.setLastUpdateTime(nowTime);
                    couponRepository.save(coupon);
                }
            }
        }
    }


    public ResultBean<Map<String, Object>> userNoPayOrder(Long userId, Long deviceId) {
        LOG.info("OrderService userNoPayOrder userId = {}, deviceId = {} ", userId, deviceId);
        ResultBean<Map<String, Object>> resultBean = new ResultBean<>();
        try {
            resultBean.setCode(StatusCode.SUCCESS_CODE_10000.getErrorCode());
            resultBean.setMsg(StatusCode.SUCCESS_CODE_10000.getErrorMsg());
            Map<String, Object> returnMap = new HashMap<>();
            if (userId == null) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("用户id不能为空");
                LOG.warn("OrderService userNoPayOrder userId is NULL");
                return resultBean;
            }

            String deviceIdStr = deviceId == null ? "" : deviceId.toString();
            OrderRedisVo orderRedisVo = redisUtils.getNoPayOrder(userId.toString(), deviceIdStr);

            Integer status = 0;//没有未支付订单
            String useStartTime = "";//车辆使用开始时间
            String endStartTime = "";//车辆使用结束时间
            Long orderId = null;
            String deviceTypeName = "";//设备类型名称
            Integer bluetoothType = 0;
            Integer lockType = 0;
            Long launchPointId = null;//投放点id
            LaunchPointRedisVo launchPointRedisVo = null;
            String launchPointName = "";
            Integer payFee = 0;
            Integer payCoin = 0;
            Integer payManageFee = 0;
            int totalTime = 0;//总共玩了多长时间
            int lowestTime = 0;//最低时长
            int highestTime = 0;//最高时长
            int price = 0;//投放点单价
            String orderNo = "";//订单编号
            String useStatusDesc = "";//车辆使用描述
            String bluetoothId = "";//蓝牙id
            String imei = "";
            Integer typeId = null;//车型
            Long userCouponId = null;//用户的优惠券
            String userCouponDes = "";//用户优惠券的使用条件
            Date nowTime = new Date();

            //说明订单已经初始化入库了
            if (orderRedisVo != null && orderRedisVo.getUserId().longValue() == userId.longValue()) {
                status = 1;//可以跳转到未支付页面
                orderId = orderRedisVo.getId();
                useStartTime = DateUtils.convert(orderRedisVo.getUseStartTime(), DateUtils.FORMAT_14).getTime() + "";
                endStartTime = DateUtils.convert(orderRedisVo.getUseEndTime(), DateUtils.FORMAT_14).getTime() + "";
                deviceId = orderRedisVo.getDeviceId();
                imei = orderRedisVo.getImei();
                deviceTypeName = orderRedisVo.getDeviceTypeName();
                DeviceTypeRedisVo deviceTypeRedisVo = redisUtils.getDeviceType(orderRedisVo.getDeviceTypeId());
                if (deviceTypeRedisVo == null) {
                    Optional<DeviceType> deviceTypeOptional = deviceTypeRepository.findById(orderRedisVo.getDeviceTypeId());
                    if (deviceTypeOptional.isPresent()) {
                        bluetoothType = deviceTypeOptional.get().getBluetoothType();
                        lockType = deviceTypeOptional.get().getLockType();
                        typeId = deviceTypeOptional.get().getType();
                    }
                } else {
                    bluetoothType = deviceTypeRedisVo.getBluetoothType();
                    lockType = deviceTypeRedisVo.getLockType();
                    typeId = deviceTypeRedisVo.getType();
                }

                DeviceRedisVo deviceRedisVo = redisUtils.getDevice(imei);
                if (deviceRedisVo == null) {
                    resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                    resultBean.setMsg("设备不存在");
                    LOG.warn("OrderService userNoPayOrder order db is extis and redis deviceRedisVo is NULL and deviceId = {}", deviceId);
                    return resultBean;
                }

                if (orderRedisVo.getPosition() != null) {
                    ShedDetailRedisVo shedDetail = redisUtils.getShedDetailByPosition(orderRedisVo.getImei(), orderRedisVo.getPosition());
                    if (shedDetail != null) {
                        bluetoothId = shedDetail.getBluetoothId();
                    }
                } else if (deviceRedisVo.getGprs() != null && deviceRedisVo.getGprs() == 0) {////没有网络模块的车
                    bluetoothId = deviceRedisVo.getBluetoothId();
                }

                launchPointId = orderRedisVo.getLaunchPointId();
                launchPointName = orderRedisVo.getLaunchPointName();
                payFee = orderRedisVo.getPayFee();
                payCoin = orderRedisVo.getPayCoin() + orderRedisVo.getPayGiveCoin();
                payManageFee = orderRedisVo.getManageFee();
                lowestTime = orderRedisVo.getLaunchPointLowestTime();
                highestTime = orderRedisVo.getLaunchPointHighestTime();
                price = orderRedisVo.getLaunchPointPrice() == null ? 0 : orderRedisVo.getLaunchPointPrice();
                orderNo = orderRedisVo.getOrderNo();
                useStatusDesc = OrderUseStatusEnum.getValueByStatus(orderRedisVo.getUseStatus());
                userCouponId = orderRedisVo.getUserCouponId();
                if (userCouponId != null) {
                    boolean flag = false;
                    Map<String, List<UserCouponRedisVo>> userCouponRedisMap = redisUtils.getUserCouponMap(orderRedisVo.getUserId().toString(), orderRedisVo.getDeviceId(), orderRedisVo.getPayFirstPlansId());
                    List<UserCouponRedisVo> userCouponRedisVoList = userCouponRedisMap == null ? null : userCouponRedisMap.get("noUse");
                    if (userCouponRedisVoList != null && userCouponRedisVoList.size() > 0) {
                        for (UserCouponRedisVo vo : userCouponRedisVoList) {
                            if (vo.getCouponId().intValue() == orderRedisVo.getCouponId().intValue() && vo.getId().intValue() == orderRedisVo.getUserCouponId().intValue()) {
                                Date validEndTime = DateUtils.convert(vo.getValidEndTime() + " 23:59:59", DateUtils.FORMAT_DATETIME_14);
                                if (vo.getStatus() == UserCouponStatusEnum.EXPIRE.getValue() || nowTime.getTime() > validEndTime.getTime()) {
                                    flag = true;//优惠券已经过期了
                                    break;
                                }
                            }
                        }
                    }
                    //优惠券没有过期
                    if (!flag) {
                        if (orderRedisVo.getCouponType() == 1) {
                            userCouponDes = "减免" + ConvertUtils.fenConvertYuan(orderRedisVo.getCouponFee()) + "元";
                        } else {
                            userCouponDes = "减免" + ConvertUtils.fenConvertYuan(orderRedisVo.getCouponTime()) + "分钟";
                        }
                    } else {
                        //优惠券过期、重置订单数据
                        payFee = orderRedisVo.getPayFee() + orderRedisVo.getCouponFee();
                        userCouponId = null;
                        userCouponDes = "";
                        Optional<Order> orderOptional = orderRepository.findById(orderRedisVo.getId());
                        if (!orderOptional.isPresent()) {
                            Order order = orderOptional.get();
                            orderRedisVo.setPayFee(payFee);
                            order.setCouponFee(0);
                            order.setCouponTime(0);
                            order.setCouponId(null);
                            order.setUserCouponId(null);
                            order.setCouponName("");
                            order.setCouponType(null);
                            orderRepository.save(order);
                            redisUtils.deleteUserNoPayOrder(orderRedisVo.getUserId() + "");
                            redisUtils.deleteUserCoupon(orderRedisVo.getUserId() + "");
                            LOG.info("OrderService userNoPayOrder save order = {}", JSON.toJSON(order));
                        }

                    }

                }

                //算玩的时长
                int times = DateUtils.minuteBetween(DateUtils.convert(orderRedisVo.getUseStartTime(), DateUtils.FORMAT_14), DateUtils.convert(orderRedisVo.getUseEndTime(), DateUtils.FORMAT_14));
                //大车、小车计时(车棚根据实际时间记时)
                if (orderRedisVo.getPosition() == null) {
                    if (times >= orderRedisVo.getLaunchPointHighestTime()) {
                        totalTime = orderRedisVo.getLaunchPointHighestTime();
                    } else if (times <= orderRedisVo.getLaunchPointLowestTime()) {
                        totalTime = orderRedisVo.getLaunchPointLowestTime();
                    } else {
                        totalTime = times;
                    }
                } else {
                    totalTime = times;
                }

            } else {

                UserDeviceRedisVo userDeviceRedisVo = redisUtils.getUserDevice(userId + "");
                if (userDeviceRedisVo != null) {
                    DeviceRedisVo deviceVo = redisUtils.getDevice(userDeviceRedisVo.getImei());
                    if (deviceVo != null) {
                        deviceId = deviceVo.getId();
                    }
                }


                if (deviceId != null) {
                    imei = redisUtils.getImei(deviceId.toString());
                    if (StringUtils.isBlank(imei)) {
                        resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                        resultBean.setMsg("设备不存在");
                        LOG.warn("OrderService userNoPayOrder redis imei is not exsit and deviceId = {}", deviceId);
                        return resultBean;
                    }

                    DeviceRedisVo deviceRedisVo = redisUtils.getDevice(imei);
                    if (deviceRedisVo == null) {
                        resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                        resultBean.setMsg("设备不存在");
                        LOG.warn("OrderService userNoPayOrder redis deviceRedisVo is NULL and deviceId = {}", deviceId);
                        return resultBean;
                    }


                    DeviceTypeRedisVo deviceTypeRedisVo = redisUtils.getDeviceType(deviceRedisVo.getTypeId());
                    if (deviceTypeRedisVo != null) {
                        deviceTypeName = deviceTypeRedisVo.getName();
                        bluetoothType = deviceTypeRedisVo.getBluetoothType();
                        lockType = deviceTypeRedisVo.getLockType();
                        typeId = deviceTypeRedisVo.getType();
                    } else {
                        Optional<DeviceType> deviceTypeOptional = deviceTypeRepository.findById(orderRedisVo.getDeviceTypeId());
                        if (deviceTypeOptional.isPresent()) {
                            bluetoothType = deviceTypeOptional.get().getBluetoothType();
                            lockType = deviceTypeOptional.get().getLockType();
                            deviceTypeName = deviceTypeOptional.get().getName();
                            typeId = deviceTypeOptional.get().getType();
                        }
                    }

                    launchPointId = deviceRedisVo.getLaunchPointId();
                    if (launchPointId != null) {
                        launchPointRedisVo = redisUtils.getLaunchPoint(launchPointId.toString());
                        if (launchPointRedisVo == null && launchPointId != null) {
                            Optional<LaunchPoint> launchPointOptional = launchPointRepository.findById(launchPointId);
                            if (launchPointOptional.isPresent()) {
                                launchPointName = launchPointOptional.get().getName();
                            }
                        } else {
                            launchPointName = launchPointRedisVo.getName();
                        }
                    }

                    //	UserDeviceRedisVo userDeviceRedisVo = redisUtils.getUserDevice(userId +"");
                    DeviceUseRedisVo deviceUseRedisVo = null;
                    if (userDeviceRedisVo != null && userDeviceRedisVo.getPosition() == null) {
                        deviceUseRedisVo = redisUtils.getDeviceUse(imei, null);
                    } else if (userDeviceRedisVo != null && userDeviceRedisVo.getPosition() != null) {
                        deviceUseRedisVo = redisUtils.getDeviceUse(imei, userDeviceRedisVo.getPosition());
                    }

                    //Date nowTime = new Date();

                    if (deviceUseRedisVo != null) {
                        payManageFee = deviceUseRedisVo.getManageFee();
                        price = deviceUseRedisVo.getPrice() == null ? 0 : deviceUseRedisVo.getPrice();
                        highestTime = deviceUseRedisVo.getHighestTime() == null ? 0 : deviceUseRedisVo.getHighestTime();
                        lowestTime = deviceUseRedisVo.getLowestTime() == null ? 0 : deviceUseRedisVo.getLowestTime();
                        Date createTime = DateUtils.convert(deviceUseRedisVo.getCreateTime(), DateUtils.FORMAT_DATETIME_14);
                        Date statrTime = (deviceUseRedisVo.getStartTime() == null || deviceUseRedisVo.getStartTime() == "") ? createTime : DateUtils.convert(deviceUseRedisVo.getStartTime(), DateUtils.FORMAT_DATETIME_14);
                        useStartTime = statrTime.getTime() + "";
                        endStartTime = nowTime.getTime() + "";
                        int minuteBetween = DateUtils.minuteBetween(statrTime, nowTime);
                        //车棚的车
                        if (deviceTypeRedisVo.getId() == 3) {
                            ShedDetailRedisVo shedDetail = redisUtils.getShedDetailByPosition(userDeviceRedisVo.getImei(), userDeviceRedisVo.getPosition());
                            if (shedDetail != null) {
                                bluetoothId = shedDetail.getBluetoothId();
                            }

                        } else if (deviceRedisVo.getGprs() != null && deviceRedisVo.getGprs() == 0) {////没有网络模块的车
                            bluetoothId = deviceRedisVo.getBluetoothId();
                        }
                        //超过最大时间自动结束订单（停车棚的车不走下面流程）
                        if (deviceTypeRedisVo.getType() != 3) {
                            if (minuteBetween > highestTime) {
                                deviceUseRedisVo.setStartTime(DateUtils.format(statrTime, DateUtils.FORMAT_DATETIME_14));
                                deviceUseRedisVo.setEndTime(DateUtils.format(nowTime, DateUtils.FORMAT_DATETIME_14));

                                int userStopType = StopCmdEnum.OTHER_USER_STOP.getValue();
                                int orderStopType = OrderUseStatusEnum.OTHER_USER_STOP_CAR.getStatus();

                                //说明商家试车的订单没有结束
                                if (deviceUseRedisVo.getUserId() == null) {
                                    userStopType = StopCmdEnum.TRADER_STOP.getValue();
                                    orderStopType = OrderUseStatusEnum.TRADER_STOP_CAR.getStatus();
                                } else if (deviceUseRedisVo.getUserId().longValue() == userId.longValue()) {
                                    userStopType = StopCmdEnum.USER_STOP.getValue();
                                    orderStopType = OrderUseStatusEnum.USER_STOP_CAR.getStatus();
                                } else {
                                    userStopType = StopCmdEnum.OTHER_USER_STOP.getValue();
                                    orderStopType = OrderUseStatusEnum.OTHER_USER_STOP_CAR.getStatus();
                                }

                                if (deviceRedisVo.getGprs() == 0) {
                                    userStopType = StopCmdEnum.SYSTEM_STOP.getValue();
                                    orderStopType = OrderUseStatusEnum.SYSTEM_STOP_CAR.getStatus();
                                }

                                deviceUseRedisVo.setUseStatus(orderStopType);

                                //创建订单（没有网络模块的先结束订单2021.1.26 李明）
                                rabbitMqService.putCreateOrder(deviceUseRedisVo);
                                //有网络模块的下发停车指令
                                if (deviceRedisVo.getGprs() == 1) {
                                    //下发停车指令
                                    CmdBaseVO<StopCmdVO> cmdVo = new CmdBaseVO<>();
                                    cmdVo.setCmd(CmdEnum.STOP_OF_PAY_AFTER.getValue());
                                    cmdVo.setImei(imei);

                                    StopCmdVO data = new StopCmdVO();
                                    data.setVal(userStopType);
                                    cmdVo.setData(data);
                                    rabbitMqService.putStop(cmdVo);
                                }


                                if (deviceUseRedisVo.getUserId() != null && deviceUseRedisVo.getUserId().longValue() == userId.longValue()) {
                                    Thread.sleep(500);
                                    OrderRedisVo orderVo = redisUtils.getNoPayOrder(userId.toString(), deviceIdStr);
                                    if (orderVo != null && orderVo.getId() != null) {
                                        //算玩的时长
                                        int times = DateUtils.minuteBetween(DateUtils.convert(useStartTime, DateUtils.FORMAT_14), DateUtils.convert(endStartTime, DateUtils.FORMAT_14));
                                        if (times > orderVo.getLaunchPointHighestTime()) {
                                            totalTime = orderVo.getLaunchPointHighestTime();
                                        } else if (times < orderVo.getLaunchPointLowestTime()) {
                                            totalTime = orderVo.getLaunchPointLowestTime();
                                        }

                                        status = 1;
                                        orderId = orderVo.getId();
                                        orderNo = orderVo.getOrderNo();
                                        payCoin = orderVo.getPayCoin() + orderVo.getPayGiveCoin();
                                        payFee = orderVo.getPayFee();
                                        useStatusDesc = OrderUseStatusEnum.getValueByStatus(orderVo.getUseStatus());
                                        LOG.info("OrderService userNoPayOrder orderId = {} userId = {},deviceId = {}", orderVo.getId(), userId, deviceId);
                                    }
                                }
                                endStartTime = nowTime.getTime() + "";
                            } else if (deviceUseRedisVo.getUserId() != null && deviceUseRedisVo.getUserId().longValue() == userId.longValue() && minuteBetween < highestTime) {
                                status = 2;//倒计时页面
                            }
                            //停车棚走倒计时
                        } else if (deviceUseRedisVo.getUserId() != null && deviceUseRedisVo.getUserId().longValue() == userId.longValue()) {
                            status = 2;//倒计时页面
                        }

                    }

                }
            }


            returnMap.put("userId", userId);
            returnMap.put("status", status);
            returnMap.put("orderId", orderId);
            returnMap.put("useStartTime", useStartTime);
            returnMap.put("endStartTime", endStartTime);
            returnMap.put("price", ConvertUtils.fenConvertYuan(price));
            returnMap.put("deviceId", deviceId);
            returnMap.put("imei", imei);
            returnMap.put("deviceTypeName", deviceTypeName);
            returnMap.put("launchPointId", launchPointId);
            returnMap.put("launchPointName", launchPointName);
            returnMap.put("bluetoothType", bluetoothType);
            returnMap.put("lockType", lockType);
            returnMap.put("typeId", typeId);
            returnMap.put("payFee", ConvertUtils.fenConvertYuan(payFee));
            returnMap.put("payCoin", payCoin);
            returnMap.put("payManageFee", ConvertUtils.fenConvertYuan(payManageFee));
            returnMap.put("totalTime", totalTime);
            returnMap.put("highestTime", highestTime);
            returnMap.put("lowestTime", lowestTime);
            returnMap.put("orderNo", orderNo);
            returnMap.put("useStatusDesc", useStatusDesc);
            returnMap.put("bluetoothId", bluetoothId);
            returnMap.put("userCouponDes", userCouponDes);
            returnMap.put("userCouponId", userCouponId == null ? "" : userCouponId);
            resultBean.setResultData(returnMap);
            LOG.info("OrderService userNoPayOrder orderNo = {} and returnMap = {} ", orderNo, JSON.toJSON(returnMap));

        } catch (Exception e) {
            LOG.error("OrderService userNoPayOrder Exception = {}", e);
            resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
            resultBean.setMsg(StatusCode.ERROR_CODE_10001.getErrorMsg());
            return resultBean;
        }
        return resultBean;
    }

    public ResultBean<ApiData<Map<String, Object>>> userOrder(Long userId, Integer pageSize, Integer pageNumber) {
        ResultBean<ApiData<Map<String, Object>>> resultBean = new ResultBean<>();
        ApiData<Map<String, Object>> apiData = new ApiData<Map<String, Object>>();
        LOG.info("OrderService userOrder userId = {} pageSize = {} pageNumber = {}", userId, pageSize, pageNumber);
        try {
            resultBean.setCode(StatusCode.SUCCESS_CODE_10000.getErrorCode());
            resultBean.setMsg(StatusCode.SUCCESS_CODE_10000.getErrorMsg());
            resultBean.setResultData(apiData);
            if (userId == null) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("用户id不能为空");
                return resultBean;
            }

            if (pageSize == null) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("pageSize不能为空");
                return resultBean;
            }

            if (pageNumber == null) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("pageNumber不能为空");
                return resultBean;
            }

            StringBuilder totalSql = new StringBuilder("select count(1) from t_order o where o.user_id = ? \n");
            Long total = jdbcTemplate.queryForObject(totalSql.toString(), Long.class, userId);
            if (total == 0) {
                return resultBean;
            }

            PageBean pBean = new PageBean(pageSize, (pageNumber + 1), total.intValue());

            StringBuilder sql = new StringBuilder();
            sql.append("SELECT o.`id` AS orderId,o.order_no as orderNo ,o.pay_fee AS payFee, \n");
            sql.append("DATE_FORMAT(o.pay_time,'%Y-%m-%d %H:%i:%S') AS payTime,o.pay_status as payStatus, \n");
            sql.append("o.refund_status as refundStatus,o.refund_fee as refundFee,o.manage_fee as manageFee,\n");
            sql.append("o.refund_reason as refundReason,o.coupon_fee as couponFee,o.coupon_time as couponTime,o.user_coupon_id as userCouponId,\n");
            sql.append("DATE_FORMAT(o.refund_resp_time,'%Y-%m-%d %H:%i:%S') AS refundRespTime, \n ");
            sql.append("o.device_id as deviceId, o.launch_point_name as launchPointName,o.device_type_name\n ");
            sql.append("as deviceTypeName,o.pay_coin as payCoin, o.pay_give_coin as payGiveCoin,DATE_FORMAT(o.use_start_time,'%Y-%m-%d %H:%i:%S') AS useStartTime,\n");
            sql.append("DATE_FORMAT(o.use_end_time,'%Y-%m-%d %H:%i:%S') AS useEndTime,o.payment_model as paymentModel ");
            sql.append("FROM `t_order` o ");
            sql.append("where o.user_id = ? \n");

            List<Map<String, Object>> data = jdbcTemplate.queryForList(sql + " order by o.create_time desc,o.refund_req_time desc limit " + pBean.getStartNum() + "," + pBean.getPageSize() + "", userId);
            data.forEach(map -> {
                int status = 0;//0未支付
                int payStatus = Integer.parseInt(map.get("payStatus").toString());
                int refundStatus = Integer.parseInt(map.get("refundStatus").toString());
                if (payStatus == 0) {
                    status = 0;
                } else if (payStatus == 1 && refundStatus == 0) {
                    status = 1;//支付成功
                } else if (payStatus == 1 && refundStatus == 1) {
                    status = 2;//退款成功
                } else if (payStatus == 1 && refundStatus == 2) {
                    status = 3;//退款失败
                }

                //支付的微信通知时间
                String payTime = map.get("payTime") == null ? "" : map.get("payTime").toString();
                //退款时间
                String refundRespTime = map.get("refundRespTime") == null ? "" : map.get("refundRespTime").toString();
                //使用开始时间
                String useStartTime = map.get("useStartTime") == null ? "" : map.get("useStartTime").toString();
                //使用结束时间
                String useEndTime = map.get("useEndTime") == null ? "" : map.get("useEndTime").toString();

                //String payFee = ConvertUtils.fenConvertYuan(Integer.parseInt(map.get("payFee").toString()) + Integer.parseInt(map.get("manageFee").toString()));
                String payFee = ConvertUtils.fenConvertYuan(Integer.parseInt(map.get("payFee").toString()));
                String manageFee = ConvertUtils.fenConvertYuan(Integer.parseInt(map.get("manageFee").toString()));
                Date startTime = DateUtils.convert(map.get("useStartTime").toString(), DateUtils.FORMAT_DATETIME_14);
                Date endTime = DateUtils.convert(map.get("useEndTime").toString(), DateUtils.FORMAT_DATETIME_14);
                String refundFee = ConvertUtils.fenConvertYuan(map.get("refundFee") == null ? 0 : Integer.parseInt(map.get("refundFee").toString()));
                int minuteBetween = DateUtils.minuteBetween(startTime, endTime);
                int couponFee = map.get("couponFee") == null ? 0 : Integer.valueOf(map.get("couponFee").toString());
                int couponTime = map.get("couponTime") == null ? 0 : Integer.valueOf(map.get("couponTime").toString());
                String couponDesc = "";//优惠券描述
                if (couponTime > 0) {
                    couponDesc = "减免" + couponTime + "分钟";
                } else if (couponFee > 0) {
                    couponDesc = "减免" + ConvertUtils.fenConvertYuan(couponFee) + "元";
                }

                String refundReason = map.get("refundReason") == null ? "" : map.get("refundReason").toString();
                Integer payCoin = map.get("payCoin") == null ? 0 : Integer.valueOf(map.get("payCoin").toString());
                Integer payGiveCoin = map.get("payGiveCoin") == null ? 0 : Integer.valueOf(map.get("payGiveCoin").toString());
                Integer paymentModel = map.get("paymentModel") == null ? 2 : Integer.valueOf(map.get("paymentModel").toString());
                map.put("payTime", payTime);
                map.put("refundRespTime", refundRespTime);
                map.put("useStartTime", useStartTime);
                map.put("useEndTime", useEndTime);
                map.put("totalTime", minuteBetween);
                map.put("couponDesc", couponDesc);
                map.put("payFee", payFee);
                map.put("status", status);
                map.put("manageFee", manageFee);
                map.put("refundFee", refundFee);
                map.put("refundReason", refundReason);
                map.put("payCoin", payCoin + payGiveCoin);
                map.put("paymentModel", paymentModel);

                map.remove("refundStatus");
                map.remove("payStatus");
                map.remove("couponFee");
                map.remove("couponTime");
                map.remove("payGiveCoin");
            });

            apiData.setData(data);
            apiData.setTotal(total);
            resultBean.setResultData(apiData);
        } catch (Exception e) {
            LOG.error("OrderService userOrder Exception = {}", e);
            resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
            resultBean.setMsg(StatusCode.ERROR_CODE_10001.getErrorMsg());
        }

        return resultBean;
    }


    public static int getCouponLeastPayFee() {
        return COUPON_LEAST_PAY_FEE;
    }


}
*/
