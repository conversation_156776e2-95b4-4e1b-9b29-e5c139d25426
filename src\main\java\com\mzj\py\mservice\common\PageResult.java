package com.mzj.py.mservice.common;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2025/3/20
 * @description:
 */
@Data
public class PageResult<T> {

    private List<T> result;

    private Long total;

    public PageResult(List<T> result, Long total) {
        this.result = result;
        this.total = total;
    }

    public PageResult() {
    }


    public static <T> PageResult<T> empty() {
        return new PageResult<>(new ArrayList<>(), 0L);
    }

    public static <T> PageResult<T> of(List<T> result, Long total) {
        return new PageResult<>(result, total);
    }

}
