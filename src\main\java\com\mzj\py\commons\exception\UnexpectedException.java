package com.mzj.py.commons.exception;

import org.springframework.http.HttpStatus;

/**
 * 未知异常
 * <AUTHOR>
 *
 */
public class UnexpectedException extends ServiceException{

	private static final long serialVersionUID = 1L;
	
	public UnexpectedException(String message) {
		super(HttpStatus.INTERNAL_SERVER_ERROR,ErrorCode.UNEXPECTED.code(), message);
	}

	public UnexpectedException(int code,String message) {
		super(HttpStatus.INTERNAL_SERVER_ERROR,code, message);
	}
}
