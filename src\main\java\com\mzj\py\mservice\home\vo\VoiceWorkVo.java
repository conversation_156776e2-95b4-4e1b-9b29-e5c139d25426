package com.mzj.py.mservice.home.vo;


import lombok.Data;

@Data
public class VoiceWorkVo {
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 作品时长(秒)
     */
    private Integer voiceTime;

    /**
     * 语速
     */
    private Integer speed;

    /**
     * 语调
     */
    private Integer volume;

    /**
     * 音量
     */
    private Integer pitch;

    /**
     * 背景音乐音量
     */
    private Integer backgroundMusicVolume;

    /**
     * 背景音乐id
     */
    private Long backgroundMusicId;

    /**
     * 作品文字
     */
    private String text;


    private String url;

}
