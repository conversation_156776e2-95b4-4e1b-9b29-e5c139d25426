package com.mzj.py.mservice.device.service;

import com.alibaba.fastjson.JSONObject;
import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.exception.NotFoundException;
import com.mzj.py.mqtt.PyMqttService;
import com.mzj.py.mservice.device.vo.DelAudioParams;
import com.mzj.py.mservice.device.vo.UpdateVolumeParams;
import com.mzj.py.mservice.home.entity.Device;
import com.mzj.py.mservice.home.repository.DeviceRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date: 2025/3/20
 * @description:
 */
@Slf4j
@Service
@Scope("prototype")
@Transactional(rollbackFor = Exception.class)
public class RemoteDeviceService {

    @Resource
    private DeviceRepository deviceRepository;
    @Resource
    private JdbcTemplate jdbcTemplate;
    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate redisTemplate;
    @Autowired
    private PyMqttService pyMqttService;

    private void checkDevice(Long deviceId) {
        boolean dev = deviceRepository.existsById(deviceId);
        if (!dev) {
            throw new NotFoundException("设备不存在");
        }

    }

    /**
     * 更新音量
     *
     * @param params
     * @return
     */
    public ResultBean<Object> updateVolume(UpdateVolumeParams params, Long userId) {
        checkDevice(params.getDeviceId());
        Device dev = deviceRepository.getOne(params.getDeviceId());
        ResultBean<Object> resultBean = pyMqttService.setVolume(dev.getSn(), params.getVolume(), userId);
        if ("10000".equals(resultBean.getCode())) {
            dev.setVolume(params.getVolume());
            deviceRepository.save(dev);
        }
        return resultBean;
    }

    /**
     * 删除音频
     *
     * @param params
     * @return
     */
    public ResultBean<Object> delAudio(DelAudioParams params, Long userId) {
        checkDevice(params.getDeviceId());
        Device dev = deviceRepository.getOne(params.getDeviceId());
        ResultBean<Object> resultBean = pyMqttService.delAudio(dev.getSn(), params.getAudioName(), userId);
        if ("10000".equals(resultBean.getCode())) {
            String sql = "delete from dub_device_voice where device_id = ?  and title = ? ";
            jdbcTemplate.update(sql, params.getDeviceId(), params.getAudioName());
        }
        return resultBean;
    }

    /**
     * 获取时间
     *
     * @param deviceId
     * @return
     */
    public ResultBean<Object> getTime(Long deviceId, Long userId) {
        checkDevice(deviceId);
        Device dev = deviceRepository.getOne(deviceId);
        ResultBean<Object> resultBean = pyMqttService.setTime(dev.getSn(), userId);
        log.info("获取时间：{}", JSONObject.toJSON(resultBean));
        return resultBean;
    }

    /**
     * 获取音乐
     *
     * @param deviceId
     * @return
     */
    public ResultBean<Object> getMusic(Long deviceId, Long userId) {
        checkDevice(deviceId);
        Device dev = deviceRepository.getOne(deviceId);
        ResultBean<Object> resultBean = pyMqttService.getAudioList(dev.getSn(), userId);
        log.info("获取音乐：{}", JSONObject.toJSON(resultBean));
        return resultBean;
    }

    /**
     * 试听音乐
     *
     * @param deviceId
     * @param audioName
     * @return
     */
    public ResultBean<Object> listenAudio(Long deviceId, String audioName, Long userId) {
        checkDevice(deviceId);
        Device dev = deviceRepository.getOne(deviceId);
        ResultBean<Object> resultBean = pyMqttService.tryAudio(dev.getSn(), audioName, userId);
        log.info("试听音乐：{}", JSONObject.toJSON(resultBean));
        return resultBean;
    }

    /**
     * 获取磁盘
     *
     * @param deviceId
     * @return
     */
    public ResultBean<Object> getSpace(Long deviceId, Long userId) {
        checkDevice(deviceId);
        Device dev = deviceRepository.getOne(deviceId);
        ResultBean<Object> resultBean = pyMqttService.getDisk(dev.getSn(), userId);
        log.info("获取磁盘：{}", JSONObject.toJSON(resultBean));
        return resultBean;
    }

    /**
     * 获取音量
     *
     * @param deviceId
     * @return
     */
    public ResultBean<Object> getVolume(Long deviceId, Long userId) {
        checkDevice(deviceId);
        Device dev = deviceRepository.getOne(deviceId);
        return pyMqttService.getVolume(dev.getSn(), userId);
    }

    /**
     * 定时播放
     *
     * @param deviceId
     * @param audioId
     * @return
     */
    public ResultBean<Object> setTimePlay(Long deviceId, String audioId) {
        checkDevice(deviceId);
        Device dev = deviceRepository.getOne(deviceId);
        Map<String, Object> map = new HashMap<>();
        map.put("msg", "此功能还没完善，需要对接，请联系开发");
        return ResultBean.successfulResult(map);
    }

    /**
     * 复制音频
     *
     * @param deviceId
     * @return
     */
    public ResultBean<Object> transmissionAudio(Long deviceId) {
        checkDevice(deviceId);
        Device dev = deviceRepository.getOne(deviceId);
        Map<String, Object> map = new HashMap<>();
        map.put("msg", "此功能还没完善，需要对接，请联系开发");
        return ResultBean.successfulResult(map);
    }

    /**
     * 发送音频
     *
     * @param deviceId
     * @return
     */
    public ResultBean<Object> sendAudio(Long deviceId, String audioUrl, Long userId, String name) {
        checkDevice(deviceId);
        Device dev = deviceRepository.getOne(deviceId);
        return pyMqttService.urlCmd(dev.getSn(), audioUrl, name, userId);
    }

    /**
     * 扫描蓝牙
     *
     * @param deviceId
     * @return
     */
    public ResultBean<Object> scanBluetooth(Long deviceId, Long userId) {
        checkDevice(deviceId);
        Device dev = deviceRepository.getOne(deviceId);
        return pyMqttService.getBluetoothList(dev.getSn(), userId);
    }

    /**
     * 扫描蓝牙
     *
     * @param deviceId
     * @return
     */
    public ResultBean<Object> getBluetooth(Long deviceId, Long userId) {
        checkDevice(deviceId);
        Device dev = deviceRepository.getOne(deviceId);
        String payload = redisTemplate.opsForValue().get(dev.getSn());
        Map<String, Object> map = new HashMap<>();
        map.put("list", payload);
        return ResultBean.successfulResult(map);
    }
}
