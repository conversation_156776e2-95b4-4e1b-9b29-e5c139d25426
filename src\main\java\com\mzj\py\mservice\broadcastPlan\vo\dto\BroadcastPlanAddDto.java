package com.mzj.py.mservice.broadcastPlan.vo.dto;


import cn.hutool.core.collection.CollUtil;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@Data
public class BroadcastPlanAddDto {

    private Long id;
    private Long shopId;
    private List<Long> deviceIds;
    private String startTime;
    private String endTime;

    private String startDate;
    private String endDate;
    private String type;
    private String intervalTime;

    private List<Long> voiceWorkList;

    public String getDevIds() {
        if (CollUtil.isEmpty(this.deviceIds)) {
            return null;
        }
        return this.deviceIds.stream().map(String::valueOf).collect(Collectors.joining(","));
    }


}
