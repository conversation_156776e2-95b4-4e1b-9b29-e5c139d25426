package com.mzj.py.mservice.shop.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2025/3/20
 * @description:
 * {"storeId":0,"deviceIds":[0]}
 */
@Data
public class StoreBindDeviceParams {

    @NotNull(message = "门店id不能为空")
    private Long storeId;

    @NotEmpty(message = "设备id不能为空")
    private List<Long> deviceIds;

    private Long userId;
}
