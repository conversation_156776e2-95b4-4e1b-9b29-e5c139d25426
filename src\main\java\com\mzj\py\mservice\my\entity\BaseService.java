package com.mzj.py.mservice.my.entity;

import javax.persistence.*;

/**
 * 客服联系方式
 * @author: duanjinze
 * @date: 2022/11/11 15:45
 * @version: 1.0
 */
@Entity
@Table(name = "dub_base_service")
public class BaseService {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 客服微信
     */
    @Basic
    @Column(name = "wechat")
    private String wechat;

    /**
     * 客服电话
     */
    @Basic
    @Column(name = "phone")
    private String phone;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getWechat() {
        return wechat;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
}
