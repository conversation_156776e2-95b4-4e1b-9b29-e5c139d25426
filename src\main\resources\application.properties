spring.application.name=py-service
server.port=${port:8060}
server.servlet.context-path=/
spring.profiles.active=${profiles:dev}
#create, create-drop, update, validate
spring.jpa.properties.hibernate.hbm2ddl.auto=validate
# ?? SQL ?????????
spring.jpa.show-sql=true
# ??? SQL???????????
spring.jpa.properties.hibernate.format_sql=true
# ?? SQL ?????????????
spring.jpa.properties.hibernate.use_sql_comments=true
# ?? Hibernate ??????
spring.jpa.properties.hibernate.type.descriptor.sql.BasicBinder.log_sql_params=true
#info
info.app.name=@project.name@
info.app.description=@project.description@
info.app.version=@project.version@
spring.servlet.multipart.max-file-size=30MB
spring.servlet.multipart.max-request-size=30MB
# upload.file.compress.size.limit, units: MB
upload.file.compress.size.limit=1
#mq
spring.rabbitmq.publisher-returns=true
#\u5f53mandatory\u6807\u5fd7\u4f4d\u8bbe\u7f6e\u4e3atrue\u65f6\uff0c\u5982\u679cexchange\u6839\u636e\u81ea\u8eab\u7c7b\u578b\u548c\u6d88\u606froutingKey\u65e0\u6cd5\u627e\u5230\u4e00\u4e2a\u5408\u9002\u7684queue\u5b58\u50a8\u6d88\u606f\uff0c
# \u90a3\u4e48broker\u4f1a\u8c03\u7528basic.return\u65b9\u6cd5\u5c06\u6d88\u606f\u8fd4\u8fd8\u7ed9\u751f\u4ea7\u8005;\u5f53mandatory\u8bbe\u7f6e\u4e3afalse\u65f6\uff0c\u51fa\u73b0\u4e0a\u8ff0\u60c5\u51b5broker\u4f1a\u76f4\u63a5\u5c06\u6d88\u606f\u4e22\u5f03;
spring.rabbitmq.template.mandatory=true
spring.rabbitmq.template.retry.enabled=true
spring.rabbitmq.template.retry.initial-interval=2s
# \u624b\u52a8ACK \u4e0d\u5f00\u542f\u81ea\u52a8ACK\u6a21\u5f0f,\u76ee\u7684\u662f\u9632\u6b62\u62a5\u9519\u540e\u672a\u6b63\u786e\u5904\u7406\u6d88\u606f\u4e22\u5931 \u9ed8\u8ba4 \u4e3a none
spring.rabbitmq.listener.simple.acknowledge-mode=manual
# ??????OSS????
ali.oss.endpoint=oss-cn-hangzhou.aliyuncs.com
ali.oss.accessKeyId=LTAI5tGLtZoTrimJhNrsYRqH
ali.oss.accessKeySecret=******************************
ali.oss.appKey=UDk4g8pGSIvNJwxU
ali.oss.bucket.name=peiyin-prod
ali.oss.bucket.path=wifi/
ali.oss.bucket.url=https://peiyin-prod.oss-cn-hangzhou.aliyuncs.com/
ali.oss.downFile.tempdir=/home/<USER>/py-service/file/MP3/
#tomcat basedir
server.tomcat.basedir=./temp/
#url white list
no.authority.urls=/token/authToken,/wxUser/register,/wxUser/openid,/wxUser/userPhone,/home/<USER>/list,/mini/order/callback,/actuator/health,/emqx/webhook
#sms
ali.sms.endpoint=dysmsapi.aliyuncs.com
ali.sms.accessKeyId=LTAI5tGu3du11Y3ZRcXEt7BF
ali.sms.accessKeySecret=******************************
ali.sms.sign.name=????
ali.sms.login.template.code=SMS_256986829
#mqtt
mqtt.broker=tcp://d.darhoo.com:1883
mqtt.username=admin
mqtt.password=peiyin2022
mqtt.clientId=mini-service-test
mqtt.timeout=5
mqtt.keepalive.interval=60
mqtt.clean.session=false
mqtt.automatic.reconnect=true


myapp.snowflake.worker-id=1
myapp.snowflake.datacenter-id=1
