package com.mzj.py.mservice.home.repository;


import com.mzj.py.mservice.home.entity.WxKey;
import com.mzj.py.mservice.wxuser.entity.WxUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface WxKeyRepository extends JpaRepository<WxKey,Long>, JpaSpecificationExecutor<WxUser> {
	/**
	 * 通过小程序appId查询秘钥
	 * @param appId
	 * @return
	 */
	WxKey findByAppId(String appId);

}
