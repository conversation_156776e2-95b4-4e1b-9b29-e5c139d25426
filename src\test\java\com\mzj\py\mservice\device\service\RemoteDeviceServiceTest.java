package com.mzj.py.mservice.device.service;

import com.mzj.py.AppServiceApplication;
import com.mzj.py.mservice.device.vo.UpdateVolumeParams;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;


@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class RemoteDeviceServiceTest {

    @Autowired
    private RemoteDeviceService remoteDeviceService;

    @Test
    public void updateVolumeTest(){
        UpdateVolumeParams params = new UpdateVolumeParams();
        params.setDeviceId(61L);
        params.setVolume(100);
        remoteDeviceService.updateVolume(params, 153L);
    }
}
