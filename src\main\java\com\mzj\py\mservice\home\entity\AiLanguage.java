package com.mzj.py.mservice.home.entity;

import com.mzj.py.mservice.wxuser.entity.IdEntity;
import lombok.Data;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-08-08-10:46
 */
@Entity
@Table(name = "ai_language")
@Data
public class AiLanguage extends IdEntity {
    @Basic
    @Column(name = "title")
    private String title;

    @Basic
    @Column(name = "del_status",columnDefinition = "smallint")
    private Integer delStatus;
    @Basic
    @Column(name = "order_index",columnDefinition = "smallint")
    private Integer orderIndex;
}
