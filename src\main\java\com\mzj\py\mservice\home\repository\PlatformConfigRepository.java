package com.mzj.py.mservice.home.repository;

import com.mzj.py.mservice.home.entity.PlatformConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

public interface PlatformConfigRepository
        extends JpaRepository<PlatformConfig, Long>, JpaSpecificationExecutor<PlatformConfig> {

    /**
     * 根据平台类型查找配置
     * 
     * @param platformType 平台类型
     * @return 平台配置
     */
    List<PlatformConfig> findByPlatformType(String platformType);

    /**
     * 根据平台类型和状态查找配置
     *
     * @param platformType 平台类型
     * @param status       状态
     * @return 平台配置
     */
    List<PlatformConfig> findByPlatformTypeAndStatus(String platformType, Integer status);
}