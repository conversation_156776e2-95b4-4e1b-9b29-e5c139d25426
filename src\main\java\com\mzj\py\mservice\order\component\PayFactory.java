package com.mzj.py.mservice.order.component;

import com.mzj.py.commons.enums.ProductOrderPayTypeEnum;
import com.mzj.py.mservice.order.vo.PayInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class PayFactory {


    @Autowired
    private WechatPayStrategy wechatPayStrategy;

    /**
     * 创建支付，简单工厂模式
     *
     * @param payInfoVO
     * @return
     */
    public String pay(PayInfoVO payInfoVO) {

        Integer payType = payInfoVO.getPayType();

        if (ProductOrderPayTypeEnum.ALI_PAY.ordinal() == payType) {

        } else if (ProductOrderPayTypeEnum.WECHAT_PAY.ordinal() == payType) {
            //微信支付
            PayStrategyContext payStrategyContext = new PayStrategyContext(wechatPayStrategy);
            return payStrategyContext.executeUnifiedOrder(payInfoVO);
        }
        return null;
    }


    /**
     * 关闭订单
     *
     * @param payInfoVO
     * @return
     */
    public String closeOrder(PayInfoVO payInfoVO) {

        Integer payType = payInfoVO.getPayType();

        if (ProductOrderPayTypeEnum.ALI_PAY.ordinal() == payType) {

        } else if (ProductOrderPayTypeEnum.WECHAT_PAY.ordinal() == payType) {

            //微信支付
            PayStrategyContext payStrategyContext = new PayStrategyContext(wechatPayStrategy);
            return payStrategyContext.executeCloseOrder(payInfoVO);
        }
        return "";
    }


    /**
     * 查询支付状态
     *
     * @param payInfoVO
     * @return
     */
    public String queryPayStatus(PayInfoVO payInfoVO) {

        Integer payType = payInfoVO.getPayType();

        if (ProductOrderPayTypeEnum.ALI_PAY.ordinal() == payType) {

        } else if (ProductOrderPayTypeEnum.WECHAT_PAY.ordinal() == payType) {

            //微信支付
            PayStrategyContext payStrategyContext = new PayStrategyContext(wechatPayStrategy);
            return payStrategyContext.executeQueryPayStatus(payInfoVO);
        }
        return "";
    }


    /**
     * 退款接口
     *
     * @param payInfoVO
     * @return
     */
    public String refund(PayInfoVO payInfoVO) {

        Integer payType = payInfoVO.getPayType();

        if (ProductOrderPayTypeEnum.ALI_PAY.ordinal() == payType) {

        } else if (ProductOrderPayTypeEnum.WECHAT_PAY.ordinal() == payType) {
            //微信支付
            PayStrategyContext payStrategyContext = new PayStrategyContext(wechatPayStrategy);
            return payStrategyContext.executeRefund(payInfoVO);
        }

        return "";
    }


}
