package com.mzj.py.mservice.token.vo;

/**
 * tokenVo
 * 
 * <AUTHOR>
 * @Date 2019-12-4
 *
 */
public class TokenVo {
	private String code; // 小程序登录code
	private String ivParameter; // 随机iv值
	private String encryptCode; // 加密串

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getIvParameter() {
		return ivParameter;
	}

	public void setIvParameter(String ivParameter) {
		this.ivParameter = ivParameter;
	}

	public String getEncryptCode() {
		return encryptCode;
	}

	public void setEncryptCode(String encryptCode) {
		this.encryptCode = encryptCode;
	}

}
