package com.mzj.py.mservice.common;

import cn.hutool.core.collection.CollUtil;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.commons.exception.UnauthorizedException;
import com.mzj.py.mservice.redis.RedisService;
import com.mzj.py.mservice.shop.entity.Shop;
import com.mzj.py.mservice.shop.entity.ShopUserRef;
import com.mzj.py.mservice.shop.repository.ShopRepository;
import com.mzj.py.mservice.shop.repository.ShopUserRefRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date: 2025/3/20
 * @description:
 */
@RestController
public class ApiBaseController {

    @Resource
    private RedisService redisService;

    @Autowired
    private ShopUserRefRepository shopUserRefRepository;

    @Autowired
    private ShopRepository shopRepository;

    public TokenRedisVo getUser(String accessToken) {
        TokenRedisVo vo = redisService.findTokenVo(accessToken);
        if (vo == null) {
            throw new UnauthorizedException("token已失效，请重新登录");
        }
        return vo;
    }

    public List<Long> getShopIds(String accessToken) {
        TokenRedisVo vo = getUser(accessToken);
        List<ShopUserRef> list = shopUserRefRepository.queryByUserId(vo.getId());
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        // 用户直接绑定的店铺ID
        List<Long> shopIds = list.stream()
                .map(ShopUserRef::getShopId)
                .collect(Collectors.toList());

        // 查询这些店铺作为"总店"时所包含的所有分店ID
        List<Long> branchIds = shopRepository.selectByParentIdIn(shopIds);
        if (CollUtil.isEmpty(branchIds)) {
            return shopIds;
        }
        // 合并并去重
        shopIds.addAll(branchIds);
        return shopIds.stream().distinct().collect(Collectors.toList());
    }

    public Long getShopId(String accessToken) {
        TokenRedisVo vo = getUser(accessToken);
        List<ShopUserRef> list = shopUserRefRepository.queryByUserId(vo.getId());
        if (CollUtil.isNotEmpty(list)) {
            return list.get(0).getShopId();
        }
        return null;
    }

}
