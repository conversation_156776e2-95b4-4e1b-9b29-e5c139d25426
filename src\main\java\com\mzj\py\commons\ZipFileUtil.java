package com.mzj.py.commons;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.Enumeration;

import org.apache.tools.zip.ZipEntry;
import org.apache.tools.zip.ZipFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date ：Created in 2019/11/21 14:22
 * @description：支付宝对账文件下载处理工具类
 * @modified By：
 * @version: v1.0$
 */
public class ZipFileUtil {
	private static final Logger log = LoggerFactory.getLogger(ZipFileUtil.class);

	/**
	 * 使用GBK编码可以避免压缩中文文件名乱码
	 */
	private static final String CHINESE_CHARSET = "GBK";

	/**
	 * 文件读取缓冲区大小
	 */
	private static final int CACHE_SIZE = 1024;

	/**
	 * 第一步： 把 支付宝生成的账单 下载到本地目录
	 *
	 * @param path     支付宝资源url
	 * @param filePath 生成的zip 包目录
	 * @throws IOException
	 */
	public static void downloadNet(String path, String filePath) throws IOException {
		// 下载网络文件
		int byteread = 0;

		URL url = new URL(path);
		FileOutputStream fs = null;
		try {
			URLConnection conn = url.openConnection();
			InputStream inStream = conn.getInputStream();
			fs = new FileOutputStream(filePath);

			byte[] buffer = new byte[1204];
			while ((byteread = inStream.read(buffer)) != -1) {
				fs.write(buffer, 0, byteread);
			}
		} catch (FileNotFoundException e) {
			log.error(e.getMessage(), e);
		} catch (IOException e) {
			log.error(e.getMessage(), e);
		} finally {
			if (fs != null) {
				fs.close();
			}
		}
	}

	public static void unZip(String zipFilePath, String destDir) throws Exception {
		ZipFile zipFile = new ZipFile(zipFilePath, CHINESE_CHARSET);
		Enumeration<?> emu = zipFile.getEntries();
		BufferedInputStream bis;
		FileOutputStream fos;
		BufferedOutputStream bos;
		File file, parentFile;
		ZipEntry entry;
		byte[] cache = new byte[CACHE_SIZE];
		while (emu.hasMoreElements()) {
			entry = (ZipEntry) emu.nextElement();
			if (entry.isDirectory()) {
				new File(destDir + entry.getName()).mkdirs();
				continue;
			}
			bis = new BufferedInputStream(zipFile.getInputStream(entry));
			file = new File(destDir + entry.getName());
			parentFile = file.getParentFile();
			if (parentFile != null && (!parentFile.exists())) {
				parentFile.mkdirs();
			}
			fos = new FileOutputStream(file);
			bos = new BufferedOutputStream(fos, CACHE_SIZE);
			int nRead = 0;
			while ((nRead = bis.read(cache, 0, CACHE_SIZE)) != -1) {
				fos.write(cache, 0, nRead);
			}
			bos.flush();
			bos.close();
			fos.close();
			bis.close();
		}
		zipFile.close();
	}

}
