package com.mzj.py.mservice.wxuser.controller;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.StatusCode;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.mservice.common.ApiBaseController;
import com.mzj.py.mservice.wxuser.controller.request.*;
import com.mzj.py.mservice.wxuser.service.WxUserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.http.MediaType;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * WxUserController单元测试类
 * 测试WxUserController中的所有公共方法，包括正面和负面场景
 * 
 * @author: Test Author
 * @date: 2024/12/12
 * @version: 1.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("WxUserController单元测试")
class WxUserControllerTest {

    @Mock
    private WxUserService wxUserService;

    @InjectMocks
    private WxUserController wxUserController;

    private MockMvc mockMvc;
    private String validAccessToken;
    private String validAppId;
    private String validCode;
    private TokenRedisVo mockTokenRedisVo;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(wxUserController).build();

        validAccessToken = "valid_token_123";
        validAppId = "wx123456789";
        validCode = "test_code_123";

        // 设置模拟的TokenRedisVo
        mockTokenRedisVo = new TokenRedisVo();
        mockTokenRedisVo.setId(1L);
        mockTokenRedisVo.setOpenid("test_openid");
        mockTokenRedisVo.setAvatar("test_avatar");
        mockTokenRedisVo.setGender(1);
        mockTokenRedisVo.setPhone("13800138000");
        mockTokenRedisVo.setArea("北京");
        mockTokenRedisVo.setNicknames("测试用户");
    }

//    @Test
//    @DisplayName("测试userOpenId接口 - 成功获取openId")
//    void testUserOpenId_成功获取openId() {
//        // Given
//        Map<String, Object> mockResult = new HashMap<>();
//        mockResult.put("openId", "test_openid");
//        mockResult.put("unionId", "test_unionid");
//
//        ResultBean<Map<String, Object>> mockResponse = ResultBean.successfulResult(mockResult);
//        when(wxUserService.userOpenId(validAppId, validCode)).thenReturn(mockResponse);
//
//        // When
//        ResultBean<Map<String, Object>> result = wxUserController.userOpenId(validAppId, validCode);
//
//        // Then
//        assertNotNull(result);
//        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
//        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
//        assertNotNull(result.getResultData());
//        assertEquals("test_openid", result.getResultData().get("openId"));
//        assertEquals("test_unionid", result.getResultData().get("unionId"));
//
//        // 验证service方法被调用
//        verify(wxUserService, times(1)).userOpenId(validAppId, validCode);
//    }

//    @Test
//    @DisplayName("测试userOpenId接口 - 参数为空")
//    void testUserOpenId_参数为空() {
//        // Given
//        ResultBean<Map<String, Object>> mockResponse = new ResultBean<>();
//        mockResponse.setCode(StatusCode.ERROR_CODE_10004.getErrorCode());
//        mockResponse.setMsg("CODE不能为空");
//
//        when(wxUserService.userOpenId(validAppId, "")).thenReturn(mockResponse);
//
//        // When
//        ResultBean<Map<String, Object>> result = wxUserController.userOpenId(validAppId, "");
//
//        // Then
//        assertNotNull(result);
//        assertEquals(StatusCode.ERROR_CODE_10004.getErrorCode(), result.getCode());
//        assertEquals("CODE不能为空", result.getMsg());
//
//        // 验证service方法被调用
//        verify(wxUserService, times(1)).userOpenId(validAppId, "");
//    }

    @Test
    @DisplayName("测试userLoginRegister接口 - 成功登录注册")
    void testUserLoginRegister_成功登录注册() {
        // Given
        UserRegistReq request = new UserRegistReq();
        request.setAppId(validAppId);
        request.setCode(validCode);
        request.setNickName("测试用户");
        request.setPhone("13800138000");
        request.setSex(1);
        request.setCountry("中国");
        request.setProvince("北京");
        request.setCity("北京");

        Map<String, Object> mockResult = new HashMap<>();
        mockResult.put("accessToken", validAccessToken);
        mockResult.put("user", "{\"id\":1,\"nickname\":\"测试用户\"}");

        ResultBean<Object> mockResponse = ResultBean.successfulResult(mockResult);
        when(wxUserService.userLoginRegister(request)).thenReturn(mockResponse);

        // When
        ResultBean<Object> result = wxUserController.userLoginRegister(request);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNotNull(result.getResultData());

        @SuppressWarnings("unchecked")
        Map<String, Object> resultData = (Map<String, Object>) result.getResultData();
        assertEquals(validAccessToken, resultData.get("accessToken"));
        assertNotNull(resultData.get("user"));

        // 验证service方法被调用
        verify(wxUserService, times(1)).userLoginRegister(request);
    }

    @Test
    @DisplayName("测试userPhone接口 - 成功获取手机号")
    void testUserPhone_成功获取手机号() {
        // Given
        String sessionKey = "test_session_key";
        String encryptedData = "encrypted_data";
        String iv = "test_iv";

        Map<String, Object> mockResult = new HashMap<>();
        mockResult.put("phone", "13800138000");

        ResultBean<Map<String, Object>> mockResponse = ResultBean.successfulResult(mockResult);
        when(wxUserService.userPhone(validAccessToken, sessionKey, encryptedData, iv)).thenReturn(mockResponse);

        // When
        ResultBean<Map<String, Object>> result = wxUserController.userPhone(validAccessToken, sessionKey, encryptedData,
                iv);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNotNull(result.getResultData());
        assertEquals("13800138000", result.getResultData().get("phone"));

        // 验证service方法被调用
        verify(wxUserService, times(1)).userPhone(validAccessToken, sessionKey, encryptedData, iv);
    }

    // 注意：updatePhone方法依赖于父类的getUser方法，在单元测试中难以模拟
    // 建议在集成测试中测试此功能，或者重构代码以便更好地进行单元测试

    @Test
    @DisplayName("测试getMyLongAnchor接口 - 成功获取长音频主播列表")
    void testGetMyLongAnchor_成功获取长音频主播列表() {
        // Given
        Map<String, Object> mockResult = new HashMap<>();
        mockResult.put("result", "anchor_list");

        ResultBean<Map<String, Object>> mockResponse = ResultBean.successfulResult(mockResult);
        when(wxUserService.getMyLongAnchor(validAccessToken)).thenReturn(mockResponse);

        // When
        ResultBean<Map<String, Object>> result = wxUserController.getMyLongAnchor(validAccessToken);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNotNull(result.getResultData());
        assertEquals("anchor_list", result.getResultData().get("result"));

        // 验证service方法被调用
        verify(wxUserService, times(1)).getMyLongAnchor(validAccessToken);
    }

    @Test
    @DisplayName("测试getMyVipAnchor接口 - 成功获取VIP主播列表")
    void testGetMyVipAnchor_成功获取VIP主播列表() {
        // Given
        Map<String, Object> mockResult = new HashMap<>();
        mockResult.put("result", "vip_anchor_list");

        ResultBean<Map<String, Object>> mockResponse = ResultBean.successfulResult(mockResult);
        when(wxUserService.getMyVipAnchor(validAccessToken)).thenReturn(mockResponse);

        // When
        ResultBean<Map<String, Object>> result = wxUserController.getMyVipAnchor(validAccessToken);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNotNull(result.getResultData());
        assertEquals("vip_anchor_list", result.getResultData().get("result"));

        // 验证service方法被调用
        verify(wxUserService, times(1)).getMyVipAnchor(validAccessToken);
    }

    @Test
    @DisplayName("测试followAnchor接口 - 成功关注主播")
    void testFollowAnchor_成功关注主播() {
        // Given
        FollowAnchorReq request = new FollowAnchorReq();
        request.setAnchorId(1L);

        ResultBean<Object> mockResponse = ResultBean.successfulResult(null);
        when(wxUserService.followAnchor(validAccessToken, request)).thenReturn(mockResponse);

        // When
        ResultBean<Object> result = wxUserController.followAnchor(validAccessToken, request);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNull(result.getResultData());

        // 验证service方法被调用
        verify(wxUserService, times(1)).followAnchor(validAccessToken, request);
    }

    @Test
    @DisplayName("测试followLongAnchor接口 - 成功关注长音频主播")
    void testFollowLongAnchor_成功关注长音频主播() {
        // Given
        FollowAnchorReq request = new FollowAnchorReq();
        request.setAnchorId(1L);

        ResultBean<Object> mockResponse = ResultBean.successfulResult(null);
        when(wxUserService.followLongAnchor(validAccessToken, request)).thenReturn(mockResponse);

        // When
        ResultBean<Object> result = wxUserController.followLongAnchor(validAccessToken, request);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNull(result.getResultData());

        // 验证service方法被调用
        verify(wxUserService, times(1)).followLongAnchor(validAccessToken, request);
    }

    @Test
    @DisplayName("测试followBgm接口 - 成功关注背景音乐")
    void testFollowBgm_成功关注背景音乐() {
        // Given
        FollowBgmReq request = new FollowBgmReq();
        request.setBgmId(1L);

        ResultBean<Object> mockResponse = ResultBean.successfulResult(null);
        when(wxUserService.followBgm(validAccessToken, request)).thenReturn(mockResponse);

        // When
        ResultBean<Object> result = wxUserController.followBgm(validAccessToken, request);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNull(result.getResultData());

        // 验证service方法被调用
        verify(wxUserService, times(1)).followBgm(validAccessToken, request);
    }

    @Test
    @DisplayName("测试getFollowBgm接口 - 成功获取关注的背景音乐列表")
    void testGetFollowBgm_成功获取关注的背景音乐列表() {
        // Given
        Map<String, Object> mockResult = new HashMap<>();
        mockResult.put("result", "bgm_list");

        ResultBean<Map<String, Object>> mockResponse = ResultBean.successfulResult(mockResult);
        when(wxUserService.getFollowBgm(validAccessToken)).thenReturn(mockResponse);

        // When
        ResultBean<Map<String, Object>> result = wxUserController.getFollowBgm(validAccessToken);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNotNull(result.getResultData());
        assertEquals("bgm_list", result.getResultData().get("result"));

        // 验证service方法被调用
        verify(wxUserService, times(1)).getFollowBgm(validAccessToken);
    }

    @Test
    @DisplayName("测试addTextTemplate接口 - 成功添加文本模板")
    void testAddTextTemplate_成功添加文本模板() {
        // Given
        TextTemplateAddReq request = new TextTemplateAddReq();
        request.setTextContent("这是一个测试模板");

        ResultBean<Object> mockResponse = ResultBean.successfulResult(true);
        when(wxUserService.addTextTemplate(validAccessToken, request)).thenReturn(mockResponse);

        // When
        ResultBean<Object> result = wxUserController.addTextTemplate(validAccessToken, request);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertTrue((Boolean) result.getResultData());

        // 验证service方法被调用
        verify(wxUserService, times(1)).addTextTemplate(validAccessToken, request);
    }

    @Test
    @DisplayName("测试getTextTemplate接口 - 成功获取文本模板列表")
    void testGetTextTemplate_成功获取文本模板列表() {
        // Given
        Map<String, Object> mockResult = new HashMap<>();
        mockResult.put("result", "template_list");

        ResultBean<Map<String, Object>> mockResponse = ResultBean.successfulResult(mockResult);
        when(wxUserService.getTextTemplate(validAccessToken)).thenReturn(mockResponse);

        // When
        ResultBean<Map<String, Object>> result = wxUserController.getTextTemplate(validAccessToken);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertNotNull(result.getResultData());
        assertEquals("template_list", result.getResultData().get("result"));

        // 验证service方法被调用
        verify(wxUserService, times(1)).getTextTemplate(validAccessToken);
    }

    @Test
    @DisplayName("测试delTextTemplate接口 - 成功删除文本模板")
    void testDelTextTemplate_成功删除文本模板() {
        // Given
        TextTemplateDelReq request = new TextTemplateDelReq();
        request.setId(1L);

        ResultBean<Object> mockResponse = ResultBean.successfulResult(true);
        when(wxUserService.delTextTemplate(validAccessToken, request)).thenReturn(mockResponse);

        // When
        ResultBean<Object> result = wxUserController.delTextTemplate(validAccessToken, request);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorCode(), result.getCode());
        assertEquals(StatusCode.SUCCESS_CODE_10000.getErrorMsg(), result.getMsg());
        assertTrue((Boolean) result.getResultData());

        // 验证service方法被调用
        verify(wxUserService, times(1)).delTextTemplate(validAccessToken, request);
    }

    @Test
    @DisplayName("测试userLoginRegister接口 - 参数验证失败")
    void testUserLoginRegister_参数验证失败() {
        // Given
        UserRegistReq request = new UserRegistReq();
        request.setCode(""); // 空的code

        ResultBean<Object> mockResponse = new ResultBean<>();
        mockResponse.setCode(StatusCode.ERROR_CODE_10004.getErrorCode());
        mockResponse.setMsg("CODE不能为空");

        when(wxUserService.userLoginRegister(request)).thenReturn(mockResponse);

        // When
        ResultBean<Object> result = wxUserController.userLoginRegister(request);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.ERROR_CODE_10004.getErrorCode(), result.getCode());
        assertEquals("CODE不能为空", result.getMsg());

        // 验证service方法被调用
        verify(wxUserService, times(1)).userLoginRegister(request);
    }

    @Test
    @DisplayName("测试userPhone接口 - 解密失败")
    void testUserPhone_解密失败() {
        // Given
        String sessionKey = "invalid_session_key";
        String encryptedData = "invalid_encrypted_data";
        String iv = "invalid_iv";

        ResultBean<Map<String, Object>> mockResponse = new ResultBean<>();
        mockResponse.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
        mockResponse.setMsg(StatusCode.ERROR_CODE_30001.getErrorMsg());

        when(wxUserService.userPhone(validAccessToken, sessionKey, encryptedData, iv)).thenReturn(mockResponse);

        // When
        ResultBean<Map<String, Object>> result = wxUserController.userPhone(validAccessToken, sessionKey, encryptedData,
                iv);

        // Then
        assertNotNull(result);
        assertEquals(StatusCode.ERROR_CODE_10001.getErrorCode(), result.getCode());
        assertEquals(StatusCode.ERROR_CODE_30001.getErrorMsg(), result.getMsg());

        // 验证service方法被调用
        verify(wxUserService, times(1)).userPhone(validAccessToken, sessionKey, encryptedData, iv);
    }
}
