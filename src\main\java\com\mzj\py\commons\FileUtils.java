package com.mzj.py.commons;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.net.URL;
import java.util.BitSet;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class FileUtils
{
	private static Logger LOG = LoggerFactory.getLogger(FileUtils.class);

	private static final char[] HEX_TABLE = "0123456789abcdef".toCharArray();
	private static final char ESCAPE_CHAR = '%';
	public static final BitSet SAFE_NAMECHARS;
	public static final BitSet SAFE_PATHCHARS;

	static
	{
		SAFE_NAMECHARS = new BitSet(256);
		int i;
		for (i = 'a'; i <= 'z'; i++)
			SAFE_NAMECHARS.set(i);
		for (i = 'A'; i <= 'Z'; i++)
			SAFE_NAMECHARS.set(i);
		for (i = '0'; i <= '9'; i++)
			SAFE_NAMECHARS.set(i);
		SAFE_NAMECHARS.set('-');
		SAFE_NAMECHARS.set('_');
		SAFE_NAMECHARS.set('.');

		SAFE_PATHCHARS = (BitSet) SAFE_NAMECHARS.clone();
		SAFE_PATHCHARS.set(File.separatorChar);
		SAFE_PATHCHARS.set('/');
	}

	private static String escape(String s, BitSet safeChars)
	{
		byte[] bytes = s.getBytes();
		StringBuffer out = new StringBuffer(bytes.length);
		for (int i = 0; i < bytes.length; i++)
		{
			int c = bytes[i] & 0xff;
			if (safeChars.get(c) && c != ESCAPE_CHAR)
			{
				out.append((char) c);
			}
			else
			{
				out.append(ESCAPE_CHAR);
				out.append(HEX_TABLE[(c >> 4) & 0x0f]);
				out.append(HEX_TABLE[(c) & 0x0f]);
			}
		}
		return out.toString();
	}

	public static String escapePath(String path)
	{
		return escape(path, SAFE_PATHCHARS);
	}

	public static String escapeName(String name)
	{
		return escape(name, SAFE_NAMECHARS);
	}

	public static String unescape(String pathOrName)
	{
		ByteArrayOutputStream out = new ByteArrayOutputStream(pathOrName.length());
		for (int i = 0; i < pathOrName.length(); i++)
		{
			char c = pathOrName.charAt(i);
			if (c == ESCAPE_CHAR)
			{
				try
				{
					out.write(Integer.parseInt(pathOrName.substring(i + 1, i + 3), 16));
				}
				catch (NumberFormatException e)
				{
					throw new IllegalArgumentException();
				}
				i += 2;
			}
			else
				out.write(c);
		}
		return new String(out.toByteArray());
	}

	public static String getFileText(String fileName)
	{
		InputStream input = null;
		try
		{
			input = getFileInputStream(fileName);
			String content = IOUtils.toString(input);

			return content;
		}
		catch (Throwable err)
		{
			LOG.warn("Read file {} failed.", fileName);
			if (LOG.isDebugEnabled())
				LOG.error(null, err);
			return "";
		}
		finally
		{
			IOUtils.closeQuietly(input);
		}
	}

	public static String getFileText(URL url)
	{
		InputStream input = null;
		try
		{
			input = url.openStream();
			String content = IOUtils.toString(input);

			return content;
		}
		catch (Exception err)
		{
			LOG.warn("Read file {} failed.", url);
			if (LOG.isDebugEnabled())
				LOG.error(null, err);
			return "";
		}
		finally
		{
			IOUtils.closeQuietly(input);
		}
	}

	public static String getFileText(File file)
	{
		InputStream input = null;
		try
		{
			if (!file.exists())
				return "";
			input = getFileInputStream(file);
			String content = IOUtils.toString(input);

			return content;
		}
		catch (Throwable err)
		{
			LOG.warn("Read file {} failed.", file.getAbsolutePath());
			if (LOG.isDebugEnabled())
				LOG.error(null, err);
			return "";
		}
		finally
		{
			IOUtils.closeQuietly(input);
		}
	}

	public static String getFileText(URL url, String encoding)
	{
		InputStream input = null;
		try
		{
			input = url.openStream();
			String content = IOUtils.toString(input, encoding);

			return content;
		}
		catch (Throwable err)
		{
			LOG.warn("Read file {} failed.", url);
			if (LOG.isDebugEnabled())
				LOG.error(null, err);
			return "";
		}
		finally
		{
			IOUtils.closeQuietly(input);
		}
	}

	public static String getFileText(String fileName, String encoding)
	{
		InputStream input = null;
		try
		{
			input = getFileInputStream(fileName);
			String content = IOUtils.toString(input, encoding);

			return content;
		}
		catch (Throwable err)
		{
			LOG.warn("Read file {} failed.", fileName);
			if (LOG.isDebugEnabled())
				LOG.error(null, err);
			return "";
		}
		finally
		{
			IOUtils.closeQuietly(input);
		}
	}

	public static String getFileText(File file, String encoding)
	{
		InputStream input = null;
		try
		{
			input = getFileInputStream(file);
			String content = IOUtils.toString(input, encoding);

			return content;
		}
		catch (Throwable err)
		{
			LOG.warn("Read file {} failed.", file.getAbsolutePath());
			if (LOG.isDebugEnabled())
				LOG.error(null, err);
			return "";
		}
		finally
		{
			IOUtils.closeQuietly(input);
		}
	}

	public static byte[] getFileBytes(String fileName)
	{
		return getFileByteStream(fileName).toByteArray();
	}

	public static byte[] getFileBytes(File file)
	{
		return getFileByteStream(file).toByteArray();
	}

	public static byte[] getFileBytes(URL url)
	{
		InputStream input = null;
		try
		{
			input = url.openStream();
			byte[] content = IOUtils.toByteArray(input);

			return content;
		}
		catch (Exception err)
		{
			throw new RuntimeException(err);
		}
		finally
		{
			IOUtils.closeQuietly(input);
		}
	}

	public static InputStream getFileInputStream(String fileName)
	{
		try
		{
			return new FileInputStream(fileName);
		}
		catch (Exception err)
		{
			throw new RuntimeException(err);
		}
	}

	public static InputStream getFileInputStream(URL url)
	{
		try
		{
			return url.openStream();
		}
		catch (Exception err)
		{
			throw new RuntimeException(err);
		}
	}

	public static InputStream getFileInputStream(File file)
	{
		try
		{
			return new FileInputStream(file);
		}
		catch (Exception err)
		{
			throw new RuntimeException(err);
		}
	}

	static ByteArrayOutputStream getFileByteStream(File file)
	{
		ByteArrayOutputStream buffer = new ByteArrayOutputStream();
		FileInputStream fin = null;

		try
		{
			fin = new FileInputStream(file);
			int iReadCount = 0;
			byte[] temp = new byte[1024];

			do
			{
				iReadCount = fin.read(temp);
				if (iReadCount > 0)
					buffer.write(temp, 0, iReadCount);
			}
			while (iReadCount > 0);

		}
		catch (Exception err)
		{
			LOG.error(file.getAbsolutePath(), err);
		}
		finally
		{
			try
			{
				if (fin != null)
					fin.close();
			}
			catch (Exception err)
			{
			}
		}

		return buffer;
	}

	static ByteArrayOutputStream getFileByteStream(String fileName)
	{
		ByteArrayOutputStream buffer = new ByteArrayOutputStream();
		FileInputStream file = null;

		try
		{
			file = new FileInputStream(fileName);
			int iReadCount = 0;
			byte[] temp = new byte[1024];

			do
			{
				iReadCount = file.read(temp);
				if (iReadCount > 0)
					buffer.write(temp, 0, iReadCount);
			}
			while (iReadCount > 0);

		}
		catch (Exception err)
		{
			LOG.error(fileName, err);
		}
		finally
		{
			try
			{
				if (file != null)
					file.close();
			}
			catch (Exception err)
			{
			}
		}

		return buffer;
	}

	public static boolean writeFileText(String fileName, String content, boolean append)
	{
		FileWriter writer = null;

		try
		{
			writer = new FileWriter(fileName, append);
			writer.write(content);
			writer.flush();

			return true;
		}
		catch (Exception err)
		{
			LOG.error(fileName, err);
			return false;
		}
		finally
		{
			try
			{
				if (writer != null)
					writer.close();
			}
			catch (Exception err)
			{
			}
		}
	}

	public static boolean writeFileText(String fileName, String content)
	{
		return writeFileText(fileName, content, false);
	}

	public static boolean appendFileText(String fileName, String content)
	{
		return writeFileText(fileName, content, true);
	}

	public static boolean writeFileText(String fileName, String content, String encoding, boolean append)
	{
		OutputStreamWriter writer = null;
		FileOutputStream stream = null;

		try
		{
			stream = new FileOutputStream(fileName, append);

			writer = new OutputStreamWriter(stream, encoding);
			writer.write(content);
			writer.flush();

			return true;
		}
		catch (Exception err)
		{
			LOG.error(fileName, err);
			return false;
		}
		finally
		{
			IOUtils.closeQuietly(stream);
			try
			{
				if (writer != null)
					writer.close();
			}
			catch (Exception err)
			{
			}
		}
	}

	public static boolean writeFileText(String fileName, String content, String encoding)
	{
		return writeFileText(fileName, content, encoding, false);
	}

	public static boolean appendFileText(String fileName, String content, String encoding)
	{
		return writeFileText(fileName, content, encoding, true);
	}

	public static boolean writeFileBytes(String fileName, byte[] content, boolean append)
	{
		FileOutputStream writer = null;

		try
		{
			writer = new FileOutputStream(fileName, append);
			writer.write(content);
			writer.flush();

			return true;
		}
		catch (Exception err)
		{
			LOG.error(fileName, err);
			return false;
		}
		finally
		{
			try
			{
				if (writer != null)
					writer.close();
			}
			catch (Exception err)
			{
			}
		}
	}

	public static boolean writeFileBytes(String fileName, byte[] content)
	{
		return writeFileBytes(fileName, content, false);
	}

	public static boolean appendFileBytes(String fileName, byte[] content)
	{
		return writeFileBytes(fileName, content, true);
	}

	public static void mkdirs(String pathName)
	{
		File file = new File(pathName);
		if (file.exists())
			return;
		file.mkdirs();
	}

	public static boolean deleteFile(String fileName)
	{
		try
		{
			File file = new File(fileName);
			return file.delete();
		}
		catch (Exception err)
		{
			LOG.error(fileName, err);
			return false;
		}
	}

	public static boolean exists(String fileName)
	{
		File file = new File(fileName);

		return file.exists();
	}

	public static void deltree(String directory)
	{
		deltree(new File(directory));
	}

	public static void deltree(File directory)
	{
		if (directory.exists() && directory.isDirectory())
		{
			File[] fileArray = directory.listFiles();

			for (int i = 0; i < fileArray.length; i++)
			{
				if (fileArray[i].isDirectory())
				{
					deltree(fileArray[i]);
				}
				else
				{
					fileArray[i].delete();
				}
			}

			directory.delete();
		}
	}
}
