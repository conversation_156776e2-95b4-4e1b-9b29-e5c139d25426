package com.mzj.py.mservice.home.repository;

import com.mzj.py.mservice.home.entity.DubTemplateType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface DubTemplateTypeRepository extends JpaRepository<DubTemplateType,Long>, JpaSpecificationExecutor<DubTemplateType> {
    List<DubTemplateType> findAllByDelStatus(Integer del);
}
