package com.mzj.py.mservice.compound.compound;


import com.mzj.py.commons.enums.DubbingTypeEnum;
import com.mzj.py.mservice.compound.entity.SpeechSynthesizerDto;
import com.mzj.py.mservice.redis.RedisService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
public class DubbingFactory {

    @Resource
    private AliDubbingStrategy aliDubbingStrategy;

    @Resource
    private DyDubbingStrategy dyDubbingStrategy;

    @Resource
    private TxDubbingStrategy txDubbingStrategy;

    @Resource
    private YdDubbingStrategy ydDubbingStrategy;

    @Resource
    private TxShotDubbingStrategy txShotDubbingStrategy;

    @Resource
    private AzureDubbingStrategy azureDubbingStrategy;

    @Resource
    private HuaWeiDubbingStrategy huaWeiDubbingStrategy;

    /**
     * 创建支付，简单工厂模式
     *
     * @param
     * @return
     */
    public String process(SpeechSynthesizerDto dto, RedisService redisService) throws Exception {
        DubbingStrategyContext dubbingStrategyContext = null;
        if (dto.getType().equalsIgnoreCase(DubbingTypeEnum.ALI_DUBBING.name())) {
            dubbingStrategyContext = new DubbingStrategyContext(aliDubbingStrategy);
        } else if (dto.getType().equalsIgnoreCase(DubbingTypeEnum.DY_DUBBING.name())) {
            dubbingStrategyContext = new DubbingStrategyContext(dyDubbingStrategy);
        } else if (dto.getType().equalsIgnoreCase(DubbingTypeEnum.TX_LONG_DUBBING.name())) {
            dubbingStrategyContext = new DubbingStrategyContext(txDubbingStrategy);
        } else if (dto.getType().equalsIgnoreCase(DubbingTypeEnum.YD_DUBBING.name())) {
            dubbingStrategyContext = new DubbingStrategyContext(ydDubbingStrategy);
        } else if (dto.getType().equalsIgnoreCase(DubbingTypeEnum.TX_SHOT_DUBBING.name())) {
            dubbingStrategyContext = new DubbingStrategyContext(txShotDubbingStrategy);
        } else if (dto.getType().equalsIgnoreCase(DubbingTypeEnum.AUZRE_AIDUBBING.name())) {
            dubbingStrategyContext = new DubbingStrategyContext(azureDubbingStrategy);
        } else if (dto.getType().equalsIgnoreCase(DubbingTypeEnum.HUAWEI_DUBBING.name())) {
            dubbingStrategyContext = new DubbingStrategyContext(huaWeiDubbingStrategy);
        }
        return dubbingStrategyContext.process(dto, redisService);
    }

}
