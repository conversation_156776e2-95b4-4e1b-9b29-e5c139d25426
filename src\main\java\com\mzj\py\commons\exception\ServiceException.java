package com.mzj.py.commons.exception;

import org.springframework.http.HttpStatus;

public class ServiceException extends RuntimeException {

	private static final long serialVersionUID = 1L;

	private int code;
	private HttpStatus httpStatus;

	public ServiceException(String message) {
		super(message);
		this.code = 500;
	}


	public ServiceException(int code, String message) {
		super(message);

		this.code = code;
	}

	public ServiceException(HttpStatus httpStatus, int code, String message) {
		super(message);
		this.httpStatus = httpStatus;
		this.code = code;
	}

	public int getCode() {
		return code;
	}

	public HttpStatus getHttpStatus() {
		return httpStatus;
	}


}
