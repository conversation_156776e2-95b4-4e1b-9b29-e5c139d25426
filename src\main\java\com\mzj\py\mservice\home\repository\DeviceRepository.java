package com.mzj.py.mservice.home.repository;

import com.mzj.py.mservice.home.entity.Device;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

public interface DeviceRepository extends JpaRepository<Device, Long>, JpaSpecificationExecutor<Device> {
    @Transactional
    @Modifying
    @Query(value = "update dub_device set status = ?2,update_status_time = ?3 where sn = ?1 ", nativeQuery = true)
    Integer updateStatusBySn(String sn, Integer status, Date updateStatusTime);

    Device findBySn(String deviceSn);
}
