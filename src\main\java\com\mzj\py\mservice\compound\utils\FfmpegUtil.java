package com.mzj.py.mservice.compound.utils;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-07-11-13:14
 */
public class FfmpegUtil {
    public static void processAudio(File sourceFile, File outFile, Double speech, Double pitch, Integer volume) throws IOException {
        String inputPath = sourceFile.getPath();
        String outputPath = outFile.getPath();
        ProcessBuilder processBuilder = new ProcessBuilder(
                "ffmpeg",
                "-i", inputPath,
                "-af", "rubberband=pitch=" + pitch + ":tempo=" + speech + ",volume=" + volume + "dB",
                "-y",
                outputPath
        );
        Process process = processBuilder.start();

        // 等待命令执行完成
        try {
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                // 命令执行失败
                throw new RuntimeException("FFmpeg command failed with exit code " + exitCode);
            }
            sourceFile.delete();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Process interrupted", e);
        }
    }

    public static void voloumVipAudio(File sourceFile, File outFile, Integer volume) throws IOException {
        String inputPath = sourceFile.getPath();
        String outputPath = outFile.getPath();//bandpass=t=k:w=100:f=13000,
        ProcessBuilder processBuilder = new ProcessBuilder(
                "ffmpeg",
                "-i", inputPath,
                // 同时应用带通滤波器和音量调整
                "-af", "volume=" + volume + "dB",
                "-q:a", "9", // 设置最高质量（无损）
                "-y",
                outputPath
        );
        Process process = processBuilder.start();

        // 等待命令执行完成
        try {
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                // 命令执行失败
                throw new RuntimeException("FFmpeg command failed with exit code " + exitCode);
            }
            sourceFile.delete();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Process interrupted", e);
        }
    }

    public static void voloumAudio(File sourceFile, File outFile, Integer volume) throws IOException {
        String inputPath = sourceFile.getPath();
        String outputPath = outFile.getPath();//bandpass=t=k:w=100:f=13000,
        ProcessBuilder processBuilder = new ProcessBuilder(
                "ffmpeg",
                "-i", inputPath,
                // 同时应用带通滤波器和音量调整
                "-af", "volume=" + volume + "dB",
                "-y",
                outputPath
        );
        Process process = processBuilder.start();

        // 等待命令执行完成
        try {
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                // 命令执行失败
                throw new RuntimeException("FFmpeg command failed with exit code " + exitCode);
            }
            sourceFile.delete();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Process interrupted", e);
        }
    }


    public static void noiseReduction(File sourceFile, File outFile) throws IOException {
        String inputPath = sourceFile.getPath();
        String outputPath = outFile.getPath();
        ProcessBuilder processBuilder = new ProcessBuilder(
                "ffmpeg",
                "-i", inputPath,
                "-af", "highpass=f=200, lowpass=f=3000,volume=10dB",
                "-y", // 自动覆盖输出文件
                outputPath
        );
        Process process = processBuilder.start();

        // 等待命令执行完成
        try {
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                // 命令执行失败
                throw new RuntimeException("FFmpeg command failed with exit code " + exitCode);
            }
            sourceFile.delete();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Process interrupted", e);
        }
    }

    public static void pcmToMp3(File sourceFile, File outFile, int sampleRate, int channels) throws Exception {
        String pcmFilePath = sourceFile.getPath();
        String mp3FilePath = outFile.getPath();
        ProcessBuilder processBuilder = new ProcessBuilder(
                "ffmpeg",
                "-f", "s16le", // PCM格式为16位小端
                "-ar", String.valueOf(sampleRate), // 采样率
                "-ac", String.valueOf(channels), // 通道数
                "-i", pcmFilePath, // 输入文件
                mp3FilePath // 输出文件
        );
        Process process = processBuilder.start();

        try {
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                throw new RuntimeException("FFmpeg process exited with code " + exitCode);
            }
            sourceFile.delete();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Process interrupted", e);
        }
    }

    public static void mp3ToPcm(File sourceFile, File outFile, int sampleRate, int channels) throws IOException {
        String mp3FilePath = sourceFile.getPath();
        String pcmFilePath = outFile.getPath();
        ProcessBuilder builder = new ProcessBuilder(
                "ffmpeg",
                "-i", mp3FilePath, // 输入文件
                "-f", "s16le", // 输出格式为16位小端PCM
                "-ar", String.valueOf(sampleRate), // 采样率
                "-ac", String.valueOf(channels), // 通道数
                pcmFilePath // 输出文件
        );

        // 启动FFmpeg进程
        Process process = builder.start();

        try {
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                throw new RuntimeException("FFmpeg process exited with code " + exitCode);
            }
            sourceFile.delete();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Process interrupted", e);
        }
    }

    public static void mixBgm(File voiceFile, File bgmFile, File outFile, Integer beforeDelay, Integer afterDelay, Double bgmVolume) {
        if (beforeDelay > 110 || afterDelay > 110) {
            throw new RuntimeException("beforeDelay or afterDelay is too long");
        }
        String voicePath = voiceFile.getPath();
        String bgmPath = bgmFile.getPath();
        String outPath = outFile.getPath();
        Double bgmDurationInSeconds = getAudioDurationInSeconds(bgmPath);
        Double audioDurationInSeconds = getAudioDurationInSeconds(voicePath);
        BigDecimal bgmBigDecimal = BigDecimal.valueOf(bgmDurationInSeconds);
        BigDecimal audioBigDecimal = BigDecimal.valueOf(audioDurationInSeconds);
        BigDecimal beforeDelayBigDecimal = BigDecimal.valueOf(beforeDelay);
        BigDecimal afterDelayBigDecimal = BigDecimal.valueOf(afterDelay);
        BigDecimal finishBigDecimal = audioBigDecimal.add(beforeDelayBigDecimal);
        // 计算混音所需的总时间
        BigDecimal totalDuration = audioBigDecimal.add(beforeDelayBigDecimal).add(afterDelayBigDecimal);
        // 计算BGM的循环次数，向上取整
        BigDecimal loopCount = totalDuration.divide(bgmBigDecimal, 0, RoundingMode.CEILING);
        ProcessBuilder processBuilder = new ProcessBuilder(
                "ffmpeg",
                "-i", voicePath,
                "-i", bgmPath,
                "-filter_complex",
                "[0:a]adelay=delays=" + beforeDelay * 1000 + ",apad=pad_dur=" + afterDelay + ",volume=5dB [voice_delayed];" +
                        "[1:a]aloop=loop=" + loopCount.intValue() + ":size=2e+09,volume=enable='between(t," + beforeDelay + ","
                        + finishBigDecimal.doubleValue() + ")':volume=" + bgmVolume + "[bgm_looped]; " +
                        "[voice_delayed][bgm_looped]amix=inputs=2:duration=first[mixed]",
                "-map", "[mixed]",
                "-y", outPath
        );
        try {
            Process process = processBuilder.start();
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                throw new RuntimeException("FFmpeg process exited with code " + exitCode);
            }
            bgmFile.delete();
            voiceFile.delete();
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Process interrupted", e);
        }
    }

    private static final ThreadPoolTaskExecutor STREAM_EXECUTOR;

    static {
        STREAM_EXECUTOR = createThreadPool();
        STREAM_EXECUTOR.initialize(); // 重要：需要初始化线程池
    }

    /**
     * 创建线程池
     */
    private static ThreadPoolTaskExecutor createThreadPool() {
        ThreadPoolTaskExecutor threadPool = new ThreadPoolTaskExecutor();
        threadPool.setCorePoolSize(8);
        threadPool.setQueueCapacity(800);
        threadPool.setMaxPoolSize(32);
        threadPool.setKeepAliveSeconds(30);
        threadPool.setThreadNamePrefix("mixBgm-");
        threadPool.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return threadPool;
    }

    private static void dealStream(Process process) {
        if (process == null) {
            return;
        }

        // 处理标准输出流
        STREAM_EXECUTOR.execute(() -> {
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                }
            } catch (IOException e) {
            }
        });

        // 处理错误输出流
        STREAM_EXECUTOR.execute(() -> {
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getErrorStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                }
            } catch (IOException e) {
            }
        });
    }

    public static void newMixBgm(File voiceFile, File bgmFile, File outFile, Integer beforeDelay, Integer afterDelay, Double bgmVolume) {
        if (beforeDelay > 110 || afterDelay > 110) {
            throw new RuntimeException("beforeDelay or afterDelay is too long");
        }
        String voicePath = voiceFile.getPath();
        String bgmPath = bgmFile.getPath();
        String outPath = outFile.getPath();
        Double bgmDurationInSeconds = getAudioDurationInSeconds(bgmPath);
        Double audioDurationInSeconds = getAudioDurationInSeconds(voicePath);
        BigDecimal bgmBigDecimal = BigDecimal.valueOf(bgmDurationInSeconds);
        BigDecimal audioBigDecimal = BigDecimal.valueOf(audioDurationInSeconds);
        BigDecimal beforeDelayBigDecimal = BigDecimal.valueOf(beforeDelay);
        BigDecimal afterDelayBigDecimal = BigDecimal.valueOf(afterDelay);
        BigDecimal finishBigDecimal = audioBigDecimal.add(beforeDelayBigDecimal);
        // 计算混音所需的总时间
        BigDecimal totalDuration = audioBigDecimal.add(beforeDelayBigDecimal).add(afterDelayBigDecimal);
        // 计算BGM的循环次数，向上取整
        BigDecimal loopCount = totalDuration.divide(bgmBigDecimal, 0, RoundingMode.CEILING);
        ProcessBuilder processBuilder = new ProcessBuilder(
                "ffmpeg",
                "-i", voicePath,
                "-i", bgmPath,
                "-filter_complex",
                "[0:a]adelay=delays=" + beforeDelay * 1000 + ",apad=pad_dur=" + afterDelay + ",volume=5dB [voice_delayed];" +
                        "[1:a]aloop=loop=" + loopCount.intValue() + ":size=2e+09,volume=enable='between(t," + beforeDelay + ","
                        + finishBigDecimal.doubleValue() + ")':volume=" + bgmVolume + "[bgm_looped]; " +
                        "[voice_delayed][bgm_looped]amix=inputs=2:duration=first[mixed]",
                "-map", "[mixed]",
                "-y", outPath
        );
        try {
            Process process = processBuilder.start();
            dealStream(process);
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                throw new RuntimeException("FFmpeg process exited with code " + exitCode);
            }
            bgmFile.delete();
            voiceFile.delete();
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Process interrupted", e);
        }
    }

    public static Double getAudioDurationInSeconds(String filePath) {
        ProcessBuilder processBuilder = new ProcessBuilder(
                "ffprobe",
                "-v", "error",
                "-show_entries", "format=duration",
                "-of", "default=noprint_wrappers=1:nokey=1",
                filePath
        );

        try {
            Process process = processBuilder.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

            String line;
            while ((line = reader.readLine()) != null) {
                // ffprobe 的输出应该是音频时长（秒）的浮点数，但这里我们直接转换为 int
                // 注意：这可能会导致精度损失
                try {
                    return Double.parseDouble(line);
                } catch (NumberFormatException e) {
                    // 如果转换失败，可能是输出格式不符合预期，继续读取下一行
                }
            }

            // 如果没有找到时长信息，可能是命令执行失败或文件不是有效的音频文件
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                // 命令执行失败，可以打印错误信息进行调试
                try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                    String errorMessage;
                    while ((errorMessage = errorReader.readLine()) != null) {
                    }
                }
            }
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }

        // 如果发生错误或未找到时长，返回 -1
        return null;
    }

    public static void createBreak(Integer breakTime, File tempFile) {
        try {
            // 将毫秒转换为秒
            double durationSec = breakTime / 1000.0;

            // 构建 FFmpeg 命令
            ProcessBuilder processBuilder = new ProcessBuilder(
                    "ffmpeg",
                    "-f", "lavfi",              // 使用 lavfi 虚拟输入设备
                    "-i", "anullsrc=r=44100",   // 生成静音，采样率24kHz
                    "-t", String.format("%.3f", durationSec),  // 设置持续时间（秒）
                    "-c:a", "libmp3lame",       // 使用 MP3 编码器
                    "-ar", "44100",             // 采样率
                    "-ac", "1",                 // 单声道
                    "-b:a", "128k",             // 比特率
                    "-y",                       // 覆盖已存在的文件
                    tempFile.getAbsolutePath()   // 输出文件路径
            );

            // 启动进程
            Process process = processBuilder.start();
            // 等待进程完成
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                throw new RuntimeException("FFmpeg process failed with exit code: " + exitCode);
            }

        } catch (IOException | InterruptedException e) {
            throw new RuntimeException("Error creating silent audio file: " + e.getMessage(), e);
        }
    }

    public static void changeAudioBase(File sourceFile, File outFile, Double speech, Double pitch, Double volume) throws IOException {
        String inputPath = sourceFile.getPath();
        String outputPath = outFile.getPath();
        ProcessBuilder processBuilder = new ProcessBuilder(
                "ffmpeg",
                "-i", inputPath,
                "-af", "rubberband=pitch=" + pitch + ":tempo=" + speech + ",volume=" + volume + "dB",
                "-y",
                outputPath
        );
        Process process = processBuilder.start();

        // 等待命令执行完成
        try {
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                // 命令执行失败
                throw new RuntimeException("FFmpeg command failed with exit code " + exitCode);
            }
            sourceFile.delete();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Process interrupted", e);
        }
    }
}
