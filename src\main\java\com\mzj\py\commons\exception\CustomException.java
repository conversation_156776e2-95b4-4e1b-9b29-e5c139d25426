package com.mzj.py.commons.exception;

import org.springframework.http.HttpStatus;

public class CustomException extends RuntimeException {

	private static final long serialVersionUID = -5929030203833632263L;
	
	private String errCode;
	private HttpStatus httpStatus;

	public CustomException(HttpStatus httpStatus, String errCode, String errMsg) {
		super(errMsg);
		this.httpStatus = httpStatus;
		this.errCode = errCode;
	}

	public CustomException(String message) {
		super(message);
	}


	public String getErrCode() {
		return errCode;
	}

	public HttpStatus getHttpStatus() {
		return httpStatus;
	}
}