package com.mzj.py.aop;

import java.lang.annotation.*;

/**
 * 门店数据访问控制注解。
 *
 * <p>
 * 如果 operate = true，则代表该接口/方法需要门店管理员权限。
 * </p>
 * <p>
 * 如果 operate = false，则代表只要用户绑定了该门店即可访问。
 * </p>
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface BroadPlanPermission {

    /**
     * 是否需要管理员权限才能执行。
     */
    boolean operate() default false;

    /**
     * 提取门店 ID 时优先匹配的字段名称。为空时使用默认规则（storeId、shopId、id）。
     */
    String key() default "";
}