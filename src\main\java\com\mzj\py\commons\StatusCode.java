package com.mzj.py.commons;

/**
 * 状态码枚举类
 *
 * <AUTHOR>
 * @date: 2019-12-04
 */
public enum StatusCode {
    /**
     * 10000 --- 成功
     */
    SUCCESS_CODE_10000("10000", "成功"),
    /**
     * 10001 --- 失败
     */
    ERROR_CODE_10001("10001", "失败"),
    /**
     * 10003 --- 请求非法
     */
    ERROR_CODE_10003("10003", "非法请求"),
    /**
     * 10004 --- 参数错误
     */
    ERROR_CODE_10004("10004", "参数错误"),
    /**
     * 10005 --- 参数异常
     */
    ERROR_CODE_10005("10005", "参数异常"),
    /**
     * 10010 --- 暂无更多数据
     */
    ERROR_CODE_10010("10010", "暂无更多数据"),
    /**
     * 10011 --- 支付异常
     */
    ERROR_CODE_10011("10011", "支付异常"),
    /**
     * 10019 --- 订单已过时，无法支付
     */
    ERROR_CODE_10019("10019", "订单已过时，无法支付"),
    ERROR_CODE_20008("20008", "上传文件失败"),
    ERROR_CODE_20009("20009", "请勿插入表情等符号"),
    ERROR_CODE_20010("20010", "请勿重复合成"),
    ERROR_CODE_20012("20012", "请检查文字是否有符号"),
    /**
     * 30000 --- 系统繁忙，请稍后再试
     */
    ERROR_CODE_30000("30000", "系统繁忙，请稍后再试"),
    /**
     * 30001 --- 系统异常，请稍后再试
     */
    ERROR_CODE_30001("30001", "系统异常，请稍后再试"),

    /**
     * 40001 --- accessToken失效
     */
    ERROR_CODE_40001("40001", "accessToken失效"),

    ;

    String errorCode;
    String errorMsg;

    StatusCode(String errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

}
