package com.mzj.py.mservice.order.entity;

import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单管理
 *
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "dub_order")
public class Order {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 订单号
     */
    @Basic
    @Column(name = "out_trade_no")
    private String outTradeNo;

    /**
     * 用户ID
     */
    @Basic
    @Column(name = "user_id")
    private Long userId;

    /**
     * 门店ID
     */
    @Basic
    @Column(name = "shop_id")
    private Long shopId;

    /**
     * 设备ID
     */
    @Basic
    @Column(name = "device_id")
    private Long deviceId;

    /**
     * 小程序Appid
     */
    @Basic
    @Column(name = "appid")
    private String appid;

    /**
     * 商户订单号
     */
    @Basic
    @Column(name = "transaction_id")
    private String transactionId;

    /**
     * 商户号
     */
    @Basic
    @Column(name = "mchid")
    private String mchid;

    /**
     * 描述
     */
    @Basic
    @Column(name = "description")
    private String description;

    /**
     * 支付时间
     */
    @Basic
    @Column(name = "time_expire")
    private String timeExpire;

    /**
     * 支付金额，单位：分
     */
    @Basic
    @Column(name = "amount")
    private BigDecimal amount;

    /**
     * 支付者信息，用户小程序\nopenid
     */
    @Basic
    @Column(name = "openid")
    private String openid;

    /**
     * ip
     */
    @Basic
    @Column(name = "payer_client_ip")
    private String payerClientIp;

    /**
     * 流量管理ID
     */
    @Basic
    @Column(name = "package_id")
    private Long packageId;

    /**
     * 支付状态 0-支付失败  1-已支付 2-待支\n付 4-取消支付
     */
    @Basic
    @Column(name = "status")
    private Integer status;

    /**
     * 支付时间
     */
    @Basic
    @Column(name = "pay_time", nullable = true)
    private Date payTime;

    /**
     * 微信交易状态
     */
    @Basic
    @Column(name = "trade_state", nullable = true)
    private String tradeState;

    /**
     * 用户支付端0WX 1H5 2PC 3APP
     */
    @Basic
    @Column(name = "client_type", nullable = true, columnDefinition = "smallint")
    private Integer clientType;

    /**
     * 支付类型，0微信-1支付宝-2银行
     */
    @Basic
    @Column(name = "pay_type", nullable = true, columnDefinition = "smallint")
    private Integer payType;

    /**
     * 创建时间
     */
    @Basic
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Basic
    @Column(name = "update_time")
    private Date updateTime;

    @Basic
    @Column(name = "unionid")
    private String unionid;

}