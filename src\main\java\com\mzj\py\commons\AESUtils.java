package com.mzj.py.commons;

import java.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class AESUtils {
	/*
	 * 加密用的Key 可以用26个字母和数字组成 此处使用AES-128-CBC加密模式，key需要为16位。
	 */
	private static String sKey = "0123456789abcdef";
	private static String ivParameter = "0123456789abcdef";

//	private AESUtils() {
//
//	}
//	private AESUtils(String key) {
//		this.sKey=key;
//	}
//	public static AESUtils getInstance() {
//		if (instance == null)
//			instance = new AESUtils();
//		return instance;
//	}
//	public static AESUtils getInstance(String key) {
//		if (instance == null)
//			instance = new AESUtils(key);
//		return instance;
//	}
	// 加密
	public static String encrypt(String sSrc) throws Exception {
		Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
		byte[] raw = sKey.getBytes();
		SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
		IvParameterSpec iv = new IvParameterSpec(ivParameter.getBytes());// 使用CBC模式，需要一个向量iv，可增加加密算法的强度
		cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);
		byte[] encrypted = cipher.doFinal(sSrc.getBytes("utf-8"));
		
		return (Base64.getEncoder().encodeToString(encrypted));// 此处使用BASE64做转码。
	}

	// 解密
	public static String decrypt(String sSrc) throws Exception {
		try {
			byte[] raw = sKey.getBytes("ASCII");
			SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
			Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
			IvParameterSpec iv = new IvParameterSpec(ivParameter.getBytes());
			cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);
			byte[] encrypted1 = Base64.getDecoder().decode(sSrc);// 先用base64解密
			byte[] original = cipher.doFinal(encrypted1);
			String originalString = new String(original, "utf-8");
			return originalString;
		} catch (Exception ex) {
			return null;
		}
	}
	
	public  static void main(String[] args) throws Exception{
		String a =AESUtils.encrypt("abc123");
		String b = AESUtils.decrypt(a);
		System.out.println(a);
		System.out.println(b);
	}

}
