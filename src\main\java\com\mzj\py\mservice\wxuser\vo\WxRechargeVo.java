package com.mzj.py.mservice.wxuser.vo;

/**
 * 微信充值vo
 * 
 * <AUTHOR>
 * @Date 2019-12-5
 *
 */
public class WxRechargeVo {
	private Long userId;// 用户id
	private Integer rechargeFee;// 充值金额
	private Long deviceId;// 设备id
	private Long plansId;// 套餐id

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Integer getRechargeFee() {
		return rechargeFee;
	}

	public void setRechargeFee(Integer rechargeFee) {
		this.rechargeFee = rechargeFee;
	}

	public Long getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(Long deviceId) {
		this.deviceId = deviceId;
	}

	public Long getPlansId() {
		return plansId;
	}

	public void setPlansId(Long plansId) {
		this.plansId = plansId;
	}

	@Override
	public String toString() {
		return "WxRechargeVo [userId=" + userId + ", rechargeFee=" + rechargeFee + ", deviceId=" + deviceId
				+ ", plansId=" + plansId + "]";
	}

}
