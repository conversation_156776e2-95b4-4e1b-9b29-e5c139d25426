package com.mzj.py.config.http;

//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * @Author: chenshifan
 * @Description: TODO
 * @Date: Create in 13:50 2019/2/18
 * @Version: 1.0
 * @Modificd By:
 */
@Service("restTemplateInterceptor")
public class RestTemplateInterceptor implements ClientHttpRequestInterceptor {
  /*  @Value("${mcds.manager.auth.token}")
    private String mcdsManagerAuthToken;*/


    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution)
            throws IOException {
       /* HttpHeaders headers = request.getHeaders();
        headers.add("mcds-manager-auth-token", mcdsManagerAuthToken);*/
        return execution.execute(request, body);
    }


}