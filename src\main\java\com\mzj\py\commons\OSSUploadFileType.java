package com.mzj.py.commons;

/**
 * OSS 文件类型
 * 
 * <AUTHOR>
 * @date 2019-12-04
 */
public enum OSSUploadFileType {
	/**
	 * 二维码
	 */
	QRCODE("qrcode"),
	/**
	 * 微信二维码
	 */
	WECHAT_QRCODE("wechat-qrcode"),
	/**
	 * 反馈图片
	 */
	FEEDBACK_QRCODE("feedback_qrcode"),
	/**
	 * 优惠券公众号二维码图片
	 */
	COUPON_OFFICIAL_ACCOUNT_QRCODE("coupon_official_account_qrcode"),
	/**
	 * 驾驶证图片
	 */
	DRIVER_CARD("driver_card"),
	/**
	 * 设备用户照片
	 */
	DEVICE_USER_PHOTO("device-user-photo");

	private String value;

	OSSUploadFileType(String value) {
		this.value = value;
	}

	@Override
	public String toString() {
		return value;
	}

}
