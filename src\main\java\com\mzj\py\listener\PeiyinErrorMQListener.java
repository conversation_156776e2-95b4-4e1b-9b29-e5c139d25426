package com.mzj.py.listener;

import com.mzj.py.commons.model.EventMessage;
import com.mzj.py.listener.entity.EventMessageDO;
import com.mzj.py.listener.repository.EventMessageRepository;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 小滴课堂,愿景：让技术不再难学
 *
 * @Description
 * <AUTHOR>
 * @Remark 有问题直接联系我，源码-笔记-技术交流群
 * @Version 1.0
 **/

@Component
@Slf4j
@RabbitListener(queuesToDeclare = { @Queue("peiyin.error.queue") })
public class PeiyinErrorMQListener {

    @Autowired
    private EventMessageRepository eventMessageRepository;

    @RabbitHandler
    public void messageErrorHandler(EventMessage eventMessage, Message message, Channel channel) throws IOException {
        EventMessageDO eventMessageDO = new EventMessageDO();
        BeanUtils.copyProperties(eventMessage,eventMessageDO);
        eventMessageRepository.save(eventMessageDO);
    }
}
