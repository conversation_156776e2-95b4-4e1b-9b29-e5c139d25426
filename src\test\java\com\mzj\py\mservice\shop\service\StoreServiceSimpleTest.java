package com.mzj.py.mservice.shop.service;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.enums.StoreReviewEnum;
import com.mzj.py.commons.enums.StoreTypeEnum;
import com.mzj.py.commons.enums.StoreUserTypeEnum;
import com.mzj.py.mservice.common.PageResult;
import com.mzj.py.mservice.home.entity.Device;
import com.mzj.py.mservice.home.repository.DeviceRepository;
import com.mzj.py.mservice.shop.entity.Shop;
import com.mzj.py.mservice.shop.entity.ShopUserRef;
import com.mzj.py.mservice.shop.repository.ShopRepository;
import com.mzj.py.mservice.shop.repository.ShopUserRefRepository;
import com.mzj.py.mservice.shop.vo.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * StoreService简化单元测试类
 */
@ExtendWith(MockitoExtension.class)
class StoreServiceSimpleTest {

    @InjectMocks
    private StoreService storeService;

    @Mock
    private JdbcTemplate jdbcTemplate;
    @Mock
    private ShopRepository shopRepository;
    @Mock
    private ShopUserRefRepository shopUserRefRepository;
    @Mock
    private DeviceRepository deviceRepository;

    private Shop mockShop;
    private ShopUserRef mockShopUserRef;
    private StorePageParams mockPageParams;
    private StoreAddVo mockStoreAddVo;

    @BeforeEach
    void setUp() {
        // 初始化Mock对象
        mockShop = new Shop();
        mockShop.setId(1L);
        mockShop.setShopName("测试门店");
        mockShop.setParentId(0L);
        mockShop.setType(StoreTypeEnum.PARENT.getCode());
        mockShop.setStatus(StoreReviewEnum.PASS.ordinal());
        mockShop.setCreateUserId(1L);
        mockShop.setCreateTime(new Date());

        mockShopUserRef = new ShopUserRef();
        mockShopUserRef.setId(1L);
        mockShopUserRef.setShopId(1L);
        mockShopUserRef.setUserId(1L);
        mockShopUserRef.setRole(StoreUserTypeEnum.ADMIN.getCode());

        mockPageParams = new StorePageParams();
        mockPageParams.setCurrentPage(1);
        mockPageParams.setPageSize(10);
        mockPageParams.setStoreId(1L);

        mockStoreAddVo = new StoreAddVo();
        mockStoreAddVo.setStoreName("新门店");
        mockStoreAddVo.setParentId(0L);
        mockStoreAddVo.setCUserId(1L);
    }

    @Test
    @DisplayName("测试分页列表查询 - 成功场景")
    void testPageList_Success() {
        // Given
        // 由于实际调用含有两个 Long 类型的可变参数，因此显式匹配两个 anyLong()，避免 PotentialStubbingProblem
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), anyLong(), anyLong()))
                .thenReturn(1L);

        List<StorePageVo> storePageVos = Arrays.asList(createStorePageVo());
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(storePageVos);

        // When
        ResultBean<PageResult<StorePageVo>> result = storeService.pageList(mockPageParams);

        // Then
        assertTrue(result.isOk());
        PageResult<StorePageVo> pageResult = result.getResultData();
        assertNotNull(pageResult);
        assertEquals(1L, pageResult.getTotal());
        assertEquals(1, pageResult.getResult().size());
    }

    @Test
    @DisplayName("测试分页列表查询 - 空结果")
    void testPageList_EmptyResult() {
        // Given
        // 同理，显式匹配两个 anyLong() 参数
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), anyLong(), anyLong()))
                .thenReturn(0L);

        // When
        ResultBean<PageResult<StorePageVo>> result = storeService.pageList(mockPageParams);

        // Then
        assertTrue(result.isOk());
        PageResult<StorePageVo> pageResult = result.getResultData();
        assertNotNull(pageResult);
        assertEquals(0L, pageResult.getTotal());
        assertTrue(pageResult.getResult().isEmpty());
    }

    @Test
    @DisplayName("测试新增门店 - 父店铺成功")
    void testAdd_Success_ParentStore() {
        // Given
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                .thenReturn(Collections.emptyList()); // 用户无绑定关系
        when(shopRepository.save(any(Shop.class))).thenReturn(mockShop);
        when(shopUserRefRepository.save(any(ShopUserRef.class))).thenReturn(mockShopUserRef);

        // When
        ResultBean<String> result = storeService.add(mockStoreAddVo);

        // Then
        assertTrue(result.isOk());
        assertEquals("新增成功", result.getMsg());
        verify(shopRepository).save(any(Shop.class));
        verify(shopUserRefRepository).save(any(ShopUserRef.class));
    }

    @Test
    @DisplayName("测试新增门店 - 用户已有绑定关系")
    void testAdd_UserAlreadyBound() {
        // Given
        List<ShopUserRef> existingRefs = Arrays.asList(mockShopUserRef);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                .thenReturn(existingRefs);

        // When
        ResultBean<String> result = storeService.add(mockStoreAddVo);

        // Then
        assertFalse(result.isOk());
        assertEquals("该用户已有绑定关系，不允许新增门店", result.getMsg());
        verify(shopRepository, never()).save(any(Shop.class));
    }

    @Test
    @DisplayName("测试门店更新 - 成功场景")
    void testUpdate_Success() {
        // Given
        mockStoreAddVo.setId(1L);
        mockStoreAddVo.setStoreName("更新门店名称");

        when(shopRepository.findById(1L)).thenReturn(Optional.of(mockShop));
        when(shopRepository.save(any(Shop.class))).thenReturn(mockShop);

        // When
        ResultBean<String> result = storeService.update(mockStoreAddVo);

        // Then
        assertTrue(result.isOk());
        assertEquals("修改成功", result.getMsg());
        verify(shopRepository).save(any(Shop.class));
    }

    @Test
    @DisplayName("测试门店更新 - 门店不存在")
    void testUpdate_ShopNotExists() {
        // Given
        mockStoreAddVo.setId(999L);
        when(shopRepository.findById(999L)).thenReturn(Optional.empty());

        // When
        ResultBean<String> result = storeService.update(mockStoreAddVo);

        // Then
        assertFalse(result.isOk());
        assertEquals("店铺不存在", result.getMsg());
    }

    @Test
    @DisplayName("测试门店删除 - 成功场景")
    void testDelete_Success() {
        // Given
        when(shopRepository.getOne(1L)).thenReturn(mockShop);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(1L)))
                .thenReturn(Collections.emptyList()); // 无子店铺

        // When
        ResultBean<String> result = storeService.delete(1L);

        // Then
        assertTrue(result.isOk());
        assertEquals("删除成功", result.getMsg());
        verify(shopRepository).deleteById(1L);
        verify(jdbcTemplate).update("delete from dub_user_shop_ref where shop_id = ?", 1L);
        verify(jdbcTemplate).update("delete from dub_device where shop_id = ?", 1L);
    }

    @Test
    @DisplayName("测试门店删除 - 有子店铺")
    void testDelete_HasChildShops() {
        // Given
        Shop childShop = new Shop();
        childShop.setId(2L);
        childShop.setParentId(1L);

        when(shopRepository.getOne(1L)).thenReturn(mockShop);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(1L)))
                .thenReturn(Arrays.asList(childShop));

        // When
        ResultBean<String> result = storeService.delete(1L);

        // Then
        assertFalse(result.isOk());
        assertEquals("该店铺有子店铺，不能删除", result.getMsg());
        verify(shopRepository, never()).deleteById(anyLong());
    }

    @Test
    @DisplayName("测试门店绑定设备 - 成功场景")
    void testBindDevice_Success() {
        // Given
        StoreBindDeviceParams bindParams = new StoreBindDeviceParams();
        bindParams.setStoreId(1L);
        bindParams.setDeviceIds(Arrays.asList(1L, 2L));
        bindParams.setUserId(1L);

        Device device1 = new Device();
        device1.setId(1L);
        device1.setShopId(null);

        Device device2 = new Device();
        device2.setId(2L);
        device2.setShopId(null);

        when(deviceRepository.findAllById(Arrays.asList(1L, 2L)))
                .thenReturn(Arrays.asList(device1, device2));
        when(shopUserRefRepository.findByShopIdAndRole(1L, StoreUserTypeEnum.ADMIN.getCode()))
                .thenReturn(mockShopUserRef);
        when(deviceRepository.saveAll(anyList())).thenReturn(Arrays.asList(device1, device2));

        // When
        ResultBean<String> result = storeService.bindDevice(bindParams);

        // Then
        assertTrue(result.isOk());
        assertEquals("绑定设备成功", result.getResultData());
        verify(deviceRepository).saveAll(anyList());
    }

    @Test
    @DisplayName("测试门店绑定设备 - 设备不存在")
    void testBindDevice_DevicesNotExist() {
        // Given
        StoreBindDeviceParams bindParams = new StoreBindDeviceParams();
        bindParams.setStoreId(1L);
        bindParams.setDeviceIds(Arrays.asList(1L, 2L));
        bindParams.setUserId(1L);

        when(deviceRepository.findAllById(Arrays.asList(1L, 2L)))
                .thenReturn(Collections.emptyList());

        // When
        ResultBean<String> result = storeService.bindDevice(bindParams);

        // Then
        assertFalse(result.isOk());
        assertEquals("设备不存在", result.getMsg());
    }

    @Test
    @DisplayName("测试自动创建门店 - 成功场景")
    void testCreateShop_Success() {
        // Given
        when(shopRepository.save(any(Shop.class))).thenReturn(mockShop);
        when(shopUserRefRepository.save(any(ShopUserRef.class))).thenReturn(mockShopUserRef);

        // When
        ShopUserRef result = storeService.createShop(1L);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getShopId());
        assertEquals(1L, result.getUserId());
        assertEquals(1, result.getRole());

        verify(shopRepository).save(any(Shop.class));
        verify(shopUserRefRepository).save(any(ShopUserRef.class));
    }

    // Helper methods
    private StorePageVo createStorePageVo() {
        StorePageVo vo = new StorePageVo();
        vo.setId(1L);
        vo.setStoreName("测试门店");
        vo.setStoreType(String.valueOf(StoreTypeEnum.PARENT.getCode()));
        vo.setMerchantCount(1);
        vo.setDeviceCount(0);
        return vo;
    }
}
