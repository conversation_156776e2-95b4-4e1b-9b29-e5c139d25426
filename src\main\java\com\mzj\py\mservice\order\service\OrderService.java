package com.mzj.py.mservice.order.service;

import com.alibaba.fastjson.JSONObject;
import com.mzj.py.commons.*;
import com.mzj.py.commons.constant.TimeConstant;
import com.mzj.py.commons.enums.DeviceFlowStateEnum;
import com.mzj.py.commons.enums.EventMessageType;
import com.mzj.py.commons.enums.ProductOrderPayTypeEnum;
import com.mzj.py.commons.enums.ProductOrderStateEnum;
import com.mzj.py.commons.model.EventMessage;
import com.mzj.py.config.rabbit.RabbitMQConfig;
import com.mzj.py.mservice.order.component.PayFactory;
import com.mzj.py.mservice.order.entity.Order;
import com.mzj.py.mservice.order.entity.OrderItem;
import com.mzj.py.mservice.order.repository.OrderItemRepository;
import com.mzj.py.mservice.order.repository.OrderRepository;
import com.mzj.py.mservice.order.vo.OrderVo;
import com.mzj.py.mservice.order.vo.PayInfoVO;
import com.mzj.py.mservice.order.vo.dto.OrderPay;
import com.mzj.py.mservice.paidPackages.entity.PaidPackages;
import com.mzj.py.mservice.paidPackages.repository.PaidPackagesRepository;
import com.mzj.py.mservice.redis.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.util.*;

@SuppressWarnings("ALL")
@Service
@Slf4j
public class OrderService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private OrderPayRecordService orderPayRecordService;

    @Autowired
    private PayFactory payFactory;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private RabbitMQConfig rabbitMQConfig;

    @Autowired
    private OrderItemRepository orderItemRepository;


    public ResultBean list(String accessToken, Integer pageSize, Integer pageNumber, int status) {

        TokenRedisVo tokenVo = redisService.findTokenVo(accessToken);
        if (tokenVo == null) {
            return ResultBean.failedResultWithMsg("用户不存在");
        }

        StringBuilder where = new StringBuilder();
        List<Object> args = new ArrayList<>();

        if (status != -1) {
            where.append(" and o.status= ? ");
            args.add(status);
        }

        String sql = "SELECT\n" +
                " o.id, \n " +
                "o.out_trade_no ,\n" +
                "  p.`name` packagesName,\n" +
                "  o.amount,\n" +
                "  u.nickname,\n" +
                "  u.phone,\n" +
                "  s.shop_name,\n" +
                "  d.`name` deviceName,\n" +
                "  d.sn deviceSn,\n" +
                "  o.`status`,\n" +
                "  o.create_time,\n" +
                "  o.time_expire,\n" +
                "  0 invoiceStatus \n" +
                "FROM\n" +
                "  dub_order o\n" +
                "  LEFT JOIN dub_device d ON d.id = o.device_id\n" +
                "  LEFT JOIN dub_shop s ON s.id = o.shop_id\n" +
                "  LEFT JOIN dub_paid_packages p ON p.id = o.package_id\n" +
                "  LEFT JOIN dub_wechat_user u ON u.id = o.user_id " +
                " WHERE 1=1  and o.user_id =  " + tokenVo.getId() + " ";


        Map<String, Object> map = new HashMap<>();

        Long count = jdbcTemplate.queryForObject("select count(1) from(" + sql + where + ")t ", Long.class, args.toArray());
        if (count == 0) {
            map.put("count", 0);
            map.put("result", null);
            return ResultBean.successfulResult(map);
        }


        where.append(" order by o.create_time desc ");
        where.append(" limit ? offset ? ");
        args.add(pageSize);
        args.add(pageSize * pageNumber);
        List<OrderVo> devices = jdbcTemplate.query(sql + where, new BeanPropertyRowMapper<>(OrderVo.class), args.toArray());
        map.put("count", count);
        map.put("result", devices);
        return ResultBean.successfulResult(map);
    }

    @Autowired
    private PaidPackagesRepository paidPackagesRepository;


    @Value("${wx.pay.appid}")
    private String appid;

    @Value("${wx.pay.mchid}")
    private String mchid;


    public ResultBean createOrder(String accessToken, OrderPay vo) {
        TokenRedisVo tokenVo = redisService.findTokenVo(accessToken);
        if (tokenVo == null) {
            return ResultBean.failedResultWithMsg("用户不存在");
        }
        String openid = tokenVo.getOpenid();
        Long userId = tokenVo.getId();
        String unionid = tokenVo.getUnionid();
        //获取流量包商品
        PaidPackages paidPackages = paidPackagesRepository.getOne(vo.getProductId());
        if (paidPackages == null) {
            return ResultBean.failedResultWithMsg("暂无该商品");
        }
        BigDecimal sellingPrice = paidPackages.getSellingPrice();//商品单价  100.5 元
        String outTradeNo = UUID.randomUUID().toString().replace("-", "");
        List<Long> deviceIdList = vo.getDeviceIdList();
        //创建订单
        Order order = new Order();
        order.setOutTradeNo(outTradeNo);//我们平台订单号
        order.setUserId(tokenVo.getId());//微信用户id
        order.setShopId(vo.getShopId());//门店id
        order.setAppid(appid);
        order.setMchid(mchid);
        order.setPackageId(vo.getProductId());//流量包商品id
        order.setAmount(sellingPrice);//金额 分
        order.setOpenid(openid);//用户小程序openid
        order.setPayerClientIp("127.0.0.1");//ip
        order.setUnionid(unionid);
        order.setDescription("测试");
        order.setCreateTime(new Date());
        order.setStatus(ProductOrderStateEnum.NEW.ordinal());//待支付
        order.setPayType(vo.getPayType());
        order = orderRepository.save(order);
        List<OrderItem> orderItems = new ArrayList<>(deviceIdList.size());
        for (Long deviceId : deviceIdList) {
            //创建支付记录表
            OrderItem orderItem = new OrderItem();
            orderItem.setDeviceId(deviceId);//设备id
            orderItem.setPackageId(vo.getProductId());//流量包商品id
            orderItem.setFlowAmount(paidPackages.getDataNum());
            orderItem.setStatus(DeviceFlowStateEnum.NEW.ordinal());
            orderItem.setOrderId(order.getId());//订单id
            orderItem.setCreateTime(new Date());//支付时间
            orderItems.add(orderItem);
        }
        orderItemRepository.saveAll(orderItems);
        //发送延迟消息
        EventMessage eventMessage = new EventMessage();
        eventMessage.setEventMessageType(EventMessageType.PRODUCT_ORDER_NEW.name());
        eventMessage.setOpenId(openid);
        eventMessage.setBizId(outTradeNo);
        eventMessage.setUserId(userId);
        eventMessage.setUnionId(unionid);
        eventMessage.setRemark("用户下单延时关单");
        rabbitTemplate.convertAndSend(rabbitMQConfig.getOrderEventExchange(), rabbitMQConfig.getOrderCloseDelayRoutingKey(), eventMessage);
        PayInfoVO payInfoVO = new PayInfoVO();
        payInfoVO.setOutTradeNo(order.getOutTradeNo());
        payInfoVO.setPayAmount(sellingPrice);
        payInfoVO.setPayType(vo.getPayType());
        payInfoVO.setClientType(vo.getClientType());
        payInfoVO.setOrderPayTimeoutMills(TimeConstant.ORDER_PAY_TIMEOUT_MILLS);
        payInfoVO.setOpenid(openid);
        payInfoVO.setUnionid(unionid);
        payInfoVO.setUserId(userId);
        String stringObjectHashMap = payFactory.pay(payInfoVO);
        return ResultBean.successfulResult(stringObjectHashMap);
    }

    public ResultBean clearOrder(String accessToken, Long orderId) {
        TokenRedisVo tokenVo = redisService.findTokenVo(accessToken);
        if (tokenVo == null) {
            return ResultBean.failedResultWithMsg("用户不存在");
        }
        Order order = orderRepository.getOne(orderId);
        if (order != null) {
            order.setStatus(4);
            orderRepository.save(order);
        }
        //更新订单支付记录
//        orderPayRecordService.paySuccess(order.getId(), null, null, 4);
        return ResultBean.successfulResult(true);
    }

    /**
     * 支付成功回调
     */
    public void orderCallback(String out_trade_no, String transaction_id, String success_time) {
        List<Order> order = orderRepository.getByNo(out_trade_no);
        for (Order order1 : order) {
            //更新订单
            order1.setTimeExpire(success_time);//支付时间
            order1.setStatus(1);//已支付
            orderRepository.paySuccess(order1.getId());
            //更新订单支付记录
//            orderPayRecordService.paySuccess(order1.getId(), transaction_id, success_time, 1);
        }

    }

    public void handleProductOrderMessage(EventMessage eventMessage) {
        String messageType = eventMessage.getEventMessageType();
        try {
            if (EventMessageType.PRODUCT_ORDER_NEW.name().equalsIgnoreCase(messageType)) {
                //关闭订单
                this.closeProductOrder(eventMessage);
            } else if (EventMessageType.PRODUCT_ORDER_PAY.name().equalsIgnoreCase(messageType)) {
                //订单已经支付，更新订单状态
                String outTradeNo = eventMessage.getBizId();
                String openId = eventMessage.getOpenId();
                String content = eventMessage.getContent();
                JSONObject jsonObject = JSONObject.parseObject(content);
                String transactionId = jsonObject.getString("transactionId");
                String successTime = (String) jsonObject.get("successTime");
                String tradeType = jsonObject.getString("tradeType");
                String tradeState = jsonObject.getString("tradeState");
                int rows = orderRepository.updateOrderPayState(outTradeNo, openId, transactionId, TimeUtils.isoStrToDate(successTime), tradeType, tradeState,
                        ProductOrderStateEnum.PAY.name(), ProductOrderStateEnum.NEW.name());
                log.info("订单更新成功:rows={},eventMessage={}", rows, eventMessage);
            }
        } catch (Exception e) {
            log.error("订单消费者消费失败:{}", eventMessage);
            throw new RuntimeException(e);
        }
    }

    private Boolean closeProductOrder(EventMessage eventMessage) {
        String outTradeNo = eventMessage.getBizId();
        String openId = eventMessage.getOpenId();

        Order order = orderRepository.findByOutTradeNoAndOpenid(outTradeNo, openId);

        if (order == null) {
            //订单不存在
            log.warn("订单不存在");
            return true;
        }

        if (order.getStatus().equals(ProductOrderStateEnum.PAY.ordinal())) {
            //已经支付
            log.info("直接确认消息，订单已经支付:{}", eventMessage);
            return true;
        }

        //未支付，需要向第三方支付平台查询状态
        if (order.getStatus().equals(ProductOrderStateEnum.NEW.ordinal())) {
            //向第三方查询状态
            PayInfoVO payInfoVO = new PayInfoVO();
            payInfoVO.setOutTradeNo(outTradeNo);
            payInfoVO.setOpenid(openId);
            payInfoVO.setPayAmount(order.getAmount());
            payInfoVO.setUserId(order.getUserId());
            payInfoVO.setUnionid(order.getUnionid());
            payInfoVO.setPayType(order.getPayType());
            payInfoVO.setClientType(order.getClientType());
            // 需要向第三方支付平台查询状态
            String payResult = payFactory.queryPayStatus(payInfoVO);
            JSONObject jsonObject = JSONObject.parseObject(payResult);
            String transactionId = jsonObject.getString("transaction_id");
            String successTime = jsonObject.getString("success_time");
            String tradeType = jsonObject.getString("trade_type");
            String tradeState = jsonObject.getString("trade_state");
            if (tradeState.equalsIgnoreCase("SUCCESS")) {
                //支付成功，主动把订单状态更新成支付
                log.warn("支付成功，但是微信回调通知失败，需要排查问题:{}", eventMessage);
                orderRepository.updateOrderState(outTradeNo, openId, ProductOrderStateEnum.PAY.ordinal(), ProductOrderStateEnum.NEW.ordinal());
                //触发支付成功后的逻辑，
                Map<String, Object> content = new HashMap<>(6);
                content.put("orderNo", outTradeNo);
                content.put("openid", order.getOpenid());
                content.put("userId", order.getUserId());
                content.put("unionid", order.getUnionid());
                content.put("product", order.getPackageId());
                content.put("tradeType", tradeType);
                content.put("tradeState", tradeState);
                content.put("transactionId", transactionId);
                content.put("successTime", successTime);
                EventMessage payEventMessage = new EventMessage();
                payEventMessage.setBizId(outTradeNo);
                payEventMessage.setOpenId(openId);
                payEventMessage.setUserId(order.getUserId());
                payEventMessage.setMessageId(outTradeNo);
                payEventMessage.setContent(JsonUtil.obj2Json(content));
                payEventMessage.setEventMessageType(EventMessageType.PRODUCT_ORDER_PAY.name());
                payEventMessage.setRemark("已经支付但是微信回调异常，重新发放vip和更新订单状态");
                rabbitTemplate.convertAndSend(rabbitMQConfig.getOrderEventExchange(),
                        rabbitMQConfig.getOrderUpdateVipRoutingKey(), payEventMessage);
            } else {
                //如果为空，则未支付成功，本地取消订单
                orderRepository.updateOrderAndWxState(outTradeNo, openId, ProductOrderStateEnum.CANCEL.name(), ProductOrderStateEnum.NEW.name(), tradeState);
                log.info("未支付成功，本地取消订单:{}", eventMessage);
            }
        }

        return true;
    }

    public void processOrderCallbackMsg(ProductOrderPayTypeEnum wechatPay, Map<String, Object> paramsMap) {
        String openId = (String) paramsMap.get("open_id");
        String outTradeNo = (String) paramsMap.get("out_trade_no");
        String tradeState = (String) paramsMap.get("trade_state");
        Long userId = Long.parseLong(paramsMap.get("user_id").toString());
        String transactionId = (String) paramsMap.get("transaction_id");
        String tradeType = (String) paramsMap.get("trade_type");
        String successTime = (String) paramsMap.get("success_time");
        if (wechatPay.name().equalsIgnoreCase(ProductOrderPayTypeEnum.WECHAT_PAY.name())) {
            if (tradeState.equalsIgnoreCase("SUCCESS")) {
                Order order = orderRepository.findByOutTradeNoAndOpenid(outTradeNo, openId);
                List<OrderItem> orderItems = orderItemRepository.findByOrderId(order.getId());
                for (OrderItem orderItem : orderItems) {
                    Map<String, Object> content = new HashMap<>(8);
                    content.put("outTradeNo", outTradeNo);
                    content.put("openId", openId);
                    content.put("tradeType", tradeType);
                    content.put("tradeState", tradeState);
                    content.put("transactionId", transactionId);
                    content.put("successTime", successTime);
                    EventMessage eventMessage = new EventMessage();
                    eventMessage.setBizId(String.valueOf(orderItem.getDeviceId()));
                    eventMessage.setOpenId(openId);
                    eventMessage.setUserId(userId);
                    eventMessage.setMessageId(outTradeNo);
                    eventMessage.setContent(JsonUtil.obj2Json(content));
                    eventMessage.setEventMessageType(EventMessageType.PRODUCT_ORDER_PAY.name());
                    eventMessage.setRemark("微信支付回调发放vip和更新订单状态");
                    rabbitTemplate.convertAndSend(rabbitMQConfig.getOrderEventExchange(),
                            rabbitMQConfig.getOrderUpdateVipRoutingKey(), eventMessage);
                }
            }
        }
    }

    public ResultBean<Map<String, Object>> cancelOrder(String openid, String outTradeNo) {
        Map<String, Object> returnMap = new HashMap<>();
        ResultBean<Map<String, Object>> resultBean = new ResultBean<>();
        Order order = orderRepository.findByOutTradeNoAndOpenid(outTradeNo, openid);
        Integer res = orderRepository.updateOrderState(outTradeNo, openid, ProductOrderStateEnum.CANCEL.ordinal(), ProductOrderStateEnum.NEW.ordinal());
        if (res == 0) {
            resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
            resultBean.setMsg("取消订单失败");
            return resultBean;
        } else if (res == 1) {
            PayInfoVO payInfoVO = new PayInfoVO();
            payInfoVO.setOutTradeNo(outTradeNo);
            payInfoVO.setPayType(order.getPayType());
            payFactory.closeOrder(payInfoVO);
        }
        returnMap.put("result", res);
        return ResultBean.successfulResult(returnMap);
    }
}
