package com.mzj.py.mservice.compound.entity;

import com.alibaba.fastjson.annotation.JSONField;


/**
 * @ Tool：IntelliJ IDEA
 * @ Author：杨念勇
 * @ Date：2024-01-16-14:21
 */

public class TtsAddRequest {
    @JSONField(name = "appid")
    private String appId;
    @JSONField(name = "reqid")
    private String reqId; // 目前未生效，使用默认值即可
    @JSONField(name = "text")
    private String text;
    @JSONField(name = "format")
    private String format;
    @JSONField(name = "voice_type")
    private String voiceType;
    @JSONField(name = "sample_rate")
    private Integer sampleRate;
    @JSONField(name = "volume")
    private Double volume;
    @JSO<PERSON>ield(name = "speed")
    private Double speed;
    @JSONField(name = "pitch")
    private Double pitch;
    @JSONField(name = "language")
    private String language;
    @JSONField(name = "enable_subtitle")
    private Integer enableSubtitle;

    @JSONField(name = "bitrate")
    private Integer bitrate;

    @JSONField(name = "style")
    private String style;
    public Integer getBitrate() {
        return bitrate;
    }

    public void setBitrate(Integer bitrate) {
        this.bitrate = bitrate;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public String getVoiceType() {
        return voiceType;
    }

    public void setVoiceType(String voiceType) {
        this.voiceType = voiceType;
    }

    public Integer getSampleRate() {
        return sampleRate;
    }

    public void setSampleRate(Integer sampleRate) {
        this.sampleRate = sampleRate;
    }

    public Double getVolume() {
        return volume;
    }

    public void setVolume(Double volume) {
        this.volume = volume;
    }

    public Double getSpeed() {
        return speed;
    }

    public void setSpeed(Double speed) {
        this.speed = speed;
    }

    public Double getPitch() {
        return pitch;
    }

    public void setPitch(Double pitch) {
        this.pitch = pitch;
    }

    public Integer getEnableSubtitle() {
        return enableSubtitle;
    }

    public void setEnableSubtitle(Integer enableSubtitle) {
        this.enableSubtitle = enableSubtitle;
    }

    public String getStyle() {
        return style;
    }

    public void setStyle(String style) {
        this.style = style;
    }
}
