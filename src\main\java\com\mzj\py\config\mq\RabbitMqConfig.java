/*

package com.mzj.cc.config.mq;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.mzj.cc.commons.constant.RabbitMQConstant;

*/
/**
 * RabbitMQ配置类
 * 
 * <AUTHOR>
 * @date: 2019-12-19
 *//*

@Configuration
public class RabbitMqConfig {
    private final Logger log = LoggerFactory.getLogger(getClass());

    public RabbitMqConfig(RabbitTemplate rabbitTemplate) {
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) ->
                log.info("消息发送成功:correlationData({}), ack({}), cause({})", correlationData, ack, cause));
        rabbitTemplate.setReturnCallback((message, replyCode, replyText, exchange, routingKey) ->
                log.info("消息丢失:exchange({}), route({}), replyCode({}), replyText({}), message:{}", exchange, routingKey, replyCode, replyText, message));
    }
    
    */
/**
	 * 创建订单队列
	 * 
	 * @return
	 * <AUTHOR>
	 *//*

	@Bean
	public Queue createOrderQueue() {
		return new Queue(RabbitMQConstant.MQ_ORDER_CREATE_QUEUE, true);
	}

	@Bean
	public TopicExchange createOrderExchange() {
		return new TopicExchange(RabbitMQConstant.MQ_ORDER_CREATE_EXCHANGE);
	}

	@Bean
	public Binding createOrderBinding() {
		return BindingBuilder.bind(createOrderQueue()).to(createOrderExchange()).with(RabbitMQConstant.MQ_ORDER_CREATE_ROUTING_KEY);
	}
	
	*/
/**
	 * 保存订单错误队列
	 * 
	 * @return
	 * <AUTHOR>
	 *//*

	@Bean
	public Queue createErrorOrderQueue() {
		return new Queue(RabbitMQConstant.MQ_ERROR_ORDER_CREATE_QUEUE, true);
	}
	
	@Bean
	public TopicExchange createErrorOrderExchange() {
		return new TopicExchange(RabbitMQConstant.MQ_ERROR_ORDER_CREATE_EXCHANGE);
	}
	
	@Bean
	public Binding createErrorOrderBinding() {
		return BindingBuilder.bind(createErrorOrderQueue()).to(createErrorOrderExchange()).with(RabbitMQConstant.MQ_ERROR_ORDER_CREATE_ROUTING_KEY);
	}
}


*/
