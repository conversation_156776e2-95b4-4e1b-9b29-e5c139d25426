package com.mzj.py.mservice.home.entity;

import com.mzj.py.mservice.wxuser.entity.IdEntity;
import lombok.Data;

import javax.persistence.*;

/**
 * 微信用户表
 */
@Entity
@Table(name = "wx_key")
@Data
public class WxKey {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Basic
    @Column(name = "name")
    private String name;

    @Basic
    @Column(name = "app_id")
    private String appId;

    @Basic
    @Column(name = "mch_id")
    private String mchId;
    @Basic
    @Column(name = "secret")
    private String secret;

    @Basic
    @Column(name = "model_id")
    private String modelId;

    @Basic
    @Column(name = "template_id")
    private String templateId;
}
